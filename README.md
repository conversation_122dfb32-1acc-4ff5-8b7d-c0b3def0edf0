# Currency Exchange System with Historical Tracking

A comprehensive multi-currency exchange rate system built with Laravel and Vue.js, featuring real-time conversion, historical rate tracking, and modern UI components.

## Features

### 🏦 Multi-Currency Support
- Support for multiple currencies (USD, EUR, GBP, IQD, JPY, CAD, AUD, CHF, CNY, TRY, SAR, AED)
- Configurable decimal places per currency
- Currency flags and symbols
- Active/inactive currency management
- Base currency designation

### 📈 Exchange Rate Management
- Real-time exchange rate updates
- Manual, API, and automatic rate sources
- Bulk rate updates
- Rate activation/deactivation
- Bidirectional currency conversion

### 📊 Historical Tracking
- Complete history of all rate changes
- Change percentage calculations
- Rate change types (increase/decrease/no_change)
- Source tracking (manual/api/automatic)
- Notes and timestamps for each change
- User attribution for manual changes

### 🎯 Public API
- RESTful API for currency data
- Real-time conversion endpoints
- Historical data access
- Statistics and analytics
- Rate comparison tools

### 🎨 Modern Frontend
- Vue.js 3 with Composition API
- Quasar Framework components
- Responsive design
- Real-time updates
- Interactive charts and visualizations
- Multi-language support

## Installation

### Prerequisites
- PHP 8.2+
- Node.js 18+
- Composer
- SQLite/MySQL/PostgreSQL

### Setup

1. **Clone the repository**
```bash
git clone <repository-url>
cd daryay-bawar
```

2. **Install PHP dependencies**
```bash
composer install
```

3. **Install Node.js dependencies**
```bash
npm install
# or
pnpm install
```

4. **Environment setup**
```bash
cp .env.example .env
php artisan key:generate
```

5. **Database setup**
```bash
php artisan migrate:fresh --seed
```

6. **Build frontend assets**
```bash
npm run build
# or for development
npm run dev
```

7. **Start the development server**
```bash
php artisan serve
```

## Database Schema

### Currencies Table
- `id` - Primary key
- `name` - Currency name (translatable)
- `code` - 3-letter currency code (ISO 4217)
- `symbol` - Currency symbol
- `country` - Country name
- `order` - Display order
- `is_active` - Active status
- `is_base_currency` - Base currency flag
- `decimal_places` - Number of decimal places
- `flag_icon` - Flag emoji/icon
- `description` - Currency description (translatable)
- `min_amount` - Minimum transaction amount
- `max_amount` - Maximum transaction amount

### Currency Exchange Rates Table
- `id` - Primary key
- `from_currency_id` - Source currency
- `to_currency_id` - Target currency
- `rate` - Exchange rate
- `is_active` - Active status

### Exchange Rate History Table
- `id` - Primary key
- `from_currency_id` - Source currency
- `to_currency_id` - Target currency
- `rate` - Exchange rate at time
- `previous_rate` - Previous rate for comparison
- `change_percentage` - Percentage change
- `change_type` - Type of change (increase/decrease/no_change)
- `source` - Rate source (manual/api/automatic)
- `notes` - Additional notes
- `effective_date` - When rate became effective
- `created_by` - User who created the rate
- `created_at` - Creation timestamp
- `updated_at` - Update timestamp

## API Endpoints

### Public API

#### Get Currencies
```http
GET /api/exchange/currencies
```

#### Get Exchange Rates
```http
GET /api/exchange/rates?base_currency=USD
```

#### Get Specific Rate
```http
GET /api/exchange/rate?from=USD&to=IQD
```

#### Convert Currency
```http
GET /api/exchange/convert?from=USD&to=IQD&amount=100
```

#### Get Historical Data
```http
GET /api/exchange/history?from=USD&to=IQD&period=30
```

#### Get Latest Rates for Multiple Pairs
```http
GET /api/exchange/latest
Content-Type: application/json

{
  "pairs": ["USD/IQD", "EUR/USD", "GBP/USD"]
}
```

#### Get Statistics
```http
GET /api/exchange/statistics?period=30
```

### Admin API

#### Currency Management
```http
GET    /admin/currencies
POST   /admin/currencies
PUT    /admin/currencies/{id}
DELETE /admin/currencies/{id}
PATCH  /admin/currencies/{id}/toggle-status
```

#### Exchange Rate Management
```http
GET    /admin/currency-exchange-rates
POST   /admin/currency-exchange-rates
PUT    /admin/currency-exchange-rates/{id}
DELETE /admin/currency-exchange-rates/{id}
PATCH  /admin/currency-exchange-rates/{id}/toggle-status
PATCH  /admin/currency-exchange-rates/bulk-update
```

#### Exchange Rate History
```http
GET    /admin/exchange-rate-history
POST   /admin/exchange-rate-history
PUT    /admin/exchange-rate-history/{id}
DELETE /admin/exchange-rate-history/{id}
GET    /admin/exchange-rate-history/chart-data
GET    /admin/exchange-rate-history/analytics
```

## Usage Examples

### Basic Currency Conversion
```javascript
// Get current rate
const response = await fetch('/api/exchange/convert?from=USD&to=IQD&amount=100');
const data = await response.json();
console.log(`100 USD = ${data.data.converted_amount} IQD`);
```

### Historical Data Analysis
```javascript
// Get 30-day history
const response = await fetch('/api/exchange/history?from=USD&to=IQD&period=30');
const data = await response.json();
const rates = data.data.history;

// Calculate volatility
const changes = rates.map(r => Math.abs(r.change_percentage));
const avgVolatility = changes.reduce((a, b) => a + b, 0) / changes.length;
```

### Rate Updates with History
```php
use App\Models\CurrencyExchangeRate;

// Update rate and automatically create history
$exchangeRate = CurrencyExchangeRate::find(1);
$exchangeRate->updateRate(
    newRate: 1320.50,
    source: 'api',
    notes: 'Updated from external API',
    userId: auth()->id()
);
```

## Configuration

### Currency Configuration
```php
// Add new currency
Currency::create([
    'name' => 'Bitcoin',
    'code' => 'BTC',
    'symbol' => '₿',
    'country' => 'Digital',
    'decimal_places' => 8,
    'is_active' => true,
    'min_amount' => 0.00000001,
    'max_amount' => 21000000,
]);
```

### Rate Source Configuration
The system supports three rate sources:
- **manual**: Manually entered rates
- **api**: Rates from external APIs
- **automatic**: System-calculated rates

## Frontend Components

### Currency Converter
- Real-time conversion
- Currency selection with flags
- Swap functionality
- Historical rate display

### Rate History Charts
- Interactive charts
- Multiple time periods (7D, 30D, 90D, 1Y)
- Statistical analysis
- Trend indicators

### Rate Management Dashboard
- CRUD operations for currencies
- Exchange rate management
- Historical data visualization
- Analytics and reporting

## Localization

The system supports multiple languages:
- English (en)
- Kurdish (ku)
- Arabic (ar)

Add translations in `resources/js/lang/` directory.

## Security

- API rate limiting
- Input validation
- SQL injection protection
- XSS protection
- CSRF protection
- User authentication for admin functions

## Performance

- Database indexing for fast queries
- Caching for frequently accessed data
- Pagination for large datasets
- Optimized API responses
- Lazy loading for frontend components

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please open an issue in the repository.

---

**Built with ❤️ using Laravel, Vue.js, and Quasar Framework** 
