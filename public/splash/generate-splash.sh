#!/bin/bash

# This script generates iOS splash screen images from your logo
# Make sure you have ImageMagick installed (brew install imagemagick)

# Set background color
BG_COLOR="white"

# Get the absolute path to the logo
LOGO_PATH=$(realpath ../logo.png)
echo "Using logo from: $LOGO_PATH"

# Create directory if it doesn't exist
mkdir -p $(dirname "$0")

# Function to generate splash screen
generate_splash() {
  local width=$1
  local height=$2
  local output="apple-splash-${width}-${height}.png"

  echo "Generating $output..."

  # Calculate logo size (40% of the smallest dimension)
  local logo_size=$(( $(( $width < $height ? $width : $height )) * 4 / 10 ))

  # Create splash screen with centered logo
  convert -size ${width}x${height} xc:$BG_COLOR \
    -gravity center \
    -draw "image SrcOver 0,0 $logo_size,$logo_size '$LOGO_PATH'" \
    "$output"

  echo "Created $output"
}

# Generate all required splash screens
echo "Starting splash screen generation..."

# iPad Pro 12.9"
generate_splash 2048 2732

# iPad Pro 11"
generate_splash 1668 2388

# iPad 10.2"
generate_splash 1536 2048

# iPhone 11 Pro Max
generate_splash 1242 2688

# iPhone X/XS
generate_splash 1125 2436

# iPhone XR
generate_splash 828 1792

# iPhone 8/7/6s/6
generate_splash 750 1334

# iPhone SE
generate_splash 640 1136

echo "Splash screen images generated in $(pwd)"

# Create placeholder if ImageMagick fails
for file in apple-splash-*.png; do
  if [ ! -f "$file" ]; then
    echo "Creating placeholder for $file"
    cp "$LOGO_PATH" "$file"
  fi
done

echo "All splash screens created successfully"
