<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>PWA Installation Check</title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/manifest.json">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #2563eb;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .success {
            background-color: #dcfce7;
            color: #166534;
            border: 1px solid #86efac;
        }
        .warning {
            background-color: #fef9c3;
            color: #854d0e;
            border: 1px solid #fde047;
        }
        .error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        img {
            max-width: 100px;
            display: block;
            margin: 20px 0;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        .debug {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8fafc;
            border-radius: 8px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>PWA Installation Check</h1>

    <div id="pwa-status" class="status">Checking PWA status...</div>

    <div>
        <h2>App Icon</h2>
        <img src="/apple-touch-icon.png" alt="App Icon" id="app-icon">
        <p id="icon-status"></p>
    </div>

    <div>
        <h2>Splash Screen</h2>
        <img src="/splash/apple-splash-750-1334.png" alt="Splash Screen" id="splash-image">
        <p id="splash-status"></p>
    </div>

    <button id="reload-btn">Reload Page</button>
    <button id="clear-cache-btn">Clear Cache & Reload</button>

    <div class="debug" id="debug-info">
        <h3>Debug Information</h3>
        <pre id="debug-content"></pre>
    </div>

    <script>
        // Check if running as PWA
        const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                     (window.navigator.standalone === true);

        // Check if on iOS
        const ua = navigator.userAgent;
        const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;

        // Update status
        const pwaStatus = document.getElementById('pwa-status');
        const iconStatus = document.getElementById('icon-status');
        const splashStatus = document.getElementById('splash-status');
        const debugContent = document.getElementById('debug-content');

        // Check icon loading
        const appIcon = document.getElementById('app-icon');
        appIcon.onload = () => {
            iconStatus.textContent = 'App icon loaded successfully';
            iconStatus.style.color = '#166534';
        };
        appIcon.onerror = () => {
            iconStatus.textContent = 'Failed to load app icon';
            iconStatus.style.color = '#991b1b';
        };

        // Check splash screen loading
        const splashImage = document.getElementById('splash-image');
        splashImage.onload = () => {
            splashStatus.textContent = 'Splash screen loaded successfully';
            splashStatus.style.color = '#166534';
        };
        splashImage.onerror = () => {
            splashStatus.textContent = 'Failed to load splash screen';
            splashStatus.style.color = '#991b1b';
        };

        // Update PWA status
        if (isPWA) {
            pwaStatus.textContent = 'Running as PWA! 🎉';
            pwaStatus.className = 'status success';

            if (isIOS) {
                pwaStatus.textContent += ' (iOS)';
            }
        } else {
            pwaStatus.textContent = 'Not running as PWA. Add to Home Screen to install.';
            pwaStatus.className = 'status warning';
        }

        // Collect debug info
        const debugInfo = {
            userAgent: navigator.userAgent,
            isPWA: isPWA,
            isIOS: isIOS,
            displayModeStandalone: window.matchMedia('(display-mode: standalone)').matches,
            navigatorStandalone: window.navigator.standalone === true,
            serviceWorkerSupported: 'serviceWorker' in navigator,
            screen: {
                width: window.screen.width,
                height: window.screen.height,
                devicePixelRatio: window.devicePixelRatio || 1
            },
            time: new Date().toISOString()
        };

        debugContent.textContent = JSON.stringify(debugInfo, null, 2);

        // Button handlers
        document.getElementById('reload-btn').addEventListener('click', () => {
            window.location.reload();
        });

        document.getElementById('clear-cache-btn').addEventListener('click', async () => {
            if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    await Promise.all(cacheNames.map(name => caches.delete(name)));
                    alert('Cache cleared successfully');
                    window.location.reload();
                } catch (error) {
                    alert('Error clearing cache: ' + error.message);
                }
            } else {
                alert('Cache API not supported in this browser');
            }
        });

        // Register service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/service-worker.js')
                .then(reg => {
                    console.log('Service Worker registered with scope:', reg.scope);
                })
                .catch(err => {
                    console.error('Service Worker registration failed:', err);
                });
        }
    </script>
</body>
</html>
