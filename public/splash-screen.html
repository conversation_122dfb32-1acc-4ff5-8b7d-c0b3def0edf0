<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title><PERSON><PERSON><PERSON></title>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/manifest.json">

    <!-- Preload main app assets to ensure faster transition -->
    <link rel="preload" href="/" as="document">
    <link rel="preload" href="/logo.png" as="image">
    <link rel="preload" href="/build/assets/app.css" as="style">
    <link rel="preload" href="/build/assets/app.js" as="script">

    <script src="/update-sw.js"></script>

    <script>
        // Detect language and set app name accordingly
        function getAppName() {
            // Get browser language
            const lang = navigator.language || navigator.userLanguage;

            // Check if language is Kurdish (ku, ku-IQ, ku-IR, etc.)
            if (lang.startsWith('ku')) {
                return "دەریای باوەڕ";
            }

            // Default to English
            return "Daryay Bawar";
        }

        // Get tagline based on language
        function getTagline() {
            // Get browser language
            const lang = navigator.language || navigator.userLanguage;

            // Check if language is Kurdish (ku, ku-IQ, ku-IR, etc.)
            if (lang.startsWith('ku')) {
                return "نرخی گۆڕینەوەی دراو";
            }

            // Default to English
            return "Currency Exchange Rates";
        }

        // Set the document title
        document.title = getAppName();
    </script>

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #3b82f6;
            --accent-color: #60a5fa;
            --text-color: #0f172a;
            --background-color: #ffffff;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --primary-color: #3b82f6;
                --secondary-color: #60a5fa;
                --accent-color: #93c5fd;
                --text-color: #f8fafc;
                --background-color: #0f172a;
            }
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* Safe area insets for iOS */
        @supports (padding: max(0px)) {
            body {
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
                padding-left: env(safe-area-inset-left);
                padding-right: env(safe-area-inset-right);
            }
        }

        .splash-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            z-index: 10;
        }

        .logo-container {
            position: relative;
            width: 120px;
            height: 120px;
            margin-bottom: 30px;
            animation: logoEntrance 1.2s ease-out forwards;
        }

        .logo {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
        }

        .app-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease-out 0.6s forwards;
        }

        .app-tagline {
            font-size: 16px;
            opacity: 0;
            transform: translateY(15px);
            animation: fadeInUp 0.8s ease-out 0.8s forwards;
            color: var(--secondary-color);
        }

        .loading-indicator {
            margin-top: 40px;
            opacity: 0;
            animation: fadeIn 0.8s ease-out 1s forwards;
        }

        .progress-bar {
            width: 200px;
            height: 4px;
            background-color: rgba(59, 130, 246, 0.2);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }

        .progress {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 0%;
            background-color: var(--primary-color);
            transition: width 0.3s ease-out;
        }

        .gradient-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
        }

        .gradient-circle {
            position: absolute;
            border-radius: 50%;
            filter: blur(60px);
            opacity: 0.6;
        }

        .circle-1 {
            width: 300px;
            height: 300px;
            background-color: var(--primary-color);
            top: -100px;
            right: -50px;
            animation: floatAnimation 8s ease-in-out infinite alternate;
        }

        .circle-2 {
            width: 250px;
            height: 250px;
            background-color: var(--accent-color);
            bottom: -80px;
            left: -80px;
            animation: floatAnimation 7s ease-in-out infinite alternate-reverse;
        }

        .circle-3 {
            width: 180px;
            height: 180px;
            background-color: var(--secondary-color);
            top: 40%;
            right: 10%;
            animation: floatAnimation 9s ease-in-out infinite alternate;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--accent-color);
            opacity: 0.4;
        }

        @keyframes logoEntrance {
            0% {
                transform: scale(0.5);
                opacity: 0;
            }
            40% {
                transform: scale(1.1);
            }
            60% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
            }
            20% {
                width: 40%;
            }
            50% {
                width: 60%;
            }
            70% {
                width: 75%;
            }
            90% {
                width: 90%;
            }
            100% {
                width: 100%;
            }
        }

        @keyframes floatAnimation {
            0% {
                transform: translate(0, 0);
            }
            100% {
                transform: translate(15px, 15px);
            }
        }

        .redirect-message {
            position: fixed;
            bottom: 20px;
            left: 0;
            width: 100%;
            text-align: center;
            font-size: 14px;
            opacity: 0;
            animation: fadeIn 0.5s ease-out 2.5s forwards;
        }

        .currency-symbols {
            position: absolute;
            font-size: 20px;
            opacity: 0.2;
            z-index: 3;
            color: var(--primary-color);
            font-weight: bold;
            animation: float 10s ease-in-out infinite;
        }

        .symbol-1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .symbol-2 {
            top: 25%;
            right: 15%;
            animation-delay: 1s;
        }

        .symbol-3 {
            bottom: 20%;
            right: 25%;
            animation-delay: 2s;
        }

        .symbol-4 {
            bottom: 30%;
            left: 20%;
            animation-delay: 3s;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-10px) rotate(5deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }

        .pulse {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: var(--primary-color);
            opacity: 0.3;
            animation: pulse 2s ease-out infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                opacity: 0.7;
            }
            70% {
                transform: scale(1.1);
                opacity: 0;
            }
            100% {
                transform: scale(0.95);
                opacity: 0;
            }
        }
    </style>

    <script>
        // Check if this page is being loaded directly (not as a splash screen)
        // If so, redirect to the main app
        if (!window.navigator.standalone && !window.location.search.includes('source=pwa')) {
            window.location.href = '/?source=pwa';
        }
    </script>
</head>
<body>
    <div class="gradient-bg">
        <div class="gradient-circle circle-1"></div>
        <div class="gradient-circle circle-2"></div>
        <div class="gradient-circle circle-3"></div>
    </div>

    <div class="particles" id="particles"></div>

    <div class="currency-symbols symbol-1">$</div>
    <div class="currency-symbols symbol-2">€</div>
    <div class="currency-symbols symbol-3">£</div>
    <div class="currency-symbols symbol-4">¥</div>

    <div class="splash-container">
        <div class="logo-container">
            <div class="pulse"></div>
            <img src="/logo.png" alt="Daryay Bawar Logo" class="logo">
        </div>
        <h1 class="app-name" id="app-name-element">Daryay Bawar</h1>
        <p class="app-tagline" id="app-tagline-element">Currency Exchange Rates</p>
        <div class="loading-indicator">
            <div class="progress-bar">
                <div class="progress"></div>
            </div>
        </div>
    </div>

    <div class="redirect-message" id="loading-message">Loading application...</div>

    <script>
        // Update app name and tagline in the UI
        document.getElementById('app-name-element').textContent = getAppName();
        document.getElementById('app-tagline-element').textContent = getTagline();

        // Create particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');

                // Random position
                const x = Math.random() * 100;
                const y = Math.random() * 100;

                // Random size
                const size = Math.random() * 4 + 2;

                // Random opacity
                const opacity = Math.random() * 0.5 + 0.1;

                // Apply styles
                particle.style.left = `${x}%`;
                particle.style.top = `${y}%`;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.opacity = opacity;

                // Add animation
                particle.style.animation = `floatAnimation ${Math.random() * 5 + 5}s ease-in-out infinite alternate`;
                particle.style.animationDelay = `${Math.random() * 5}s`;

                particlesContainer.appendChild(particle);
            }
        }

        // Redirect to main app after animation and when app is loaded
        function redirectToApp() {
            // Get progress bar element
            const progressBar = document.querySelector('.progress');
            const loadingMessage = document.getElementById('loading-message');

            // Create an iframe to preload the main app
            const preloadFrame = document.createElement('iframe');
            preloadFrame.style.display = 'none';
            preloadFrame.src = '/?source=pwa&preload=true';
            document.body.appendChild(preloadFrame);

            // Track loading state
            let animationComplete = false;
            let appLoaded = false;
            let progressValue = 0;

            // Loading message states
            const loadingMessages = {
                en: [
                    "Loading application...",
                    "Loading resources...",
                    "Preparing interface...",
                    "Almost ready...",
                    "Starting application..."
                ],
                ku: [
                    "داگرتنی بەرنامە...",
                    "داگرتنی سەرچاوەکان...",
                    "ئامادەکردنی ڕووکار...",
                    "نزیکە لە تەواوبوون...",
                    "دەستپێکردنی بەرنامە..."
                ]
            };

            // Get current language
            const currentLang = getAppName() === "Daryay Bawar" ? "en" : "ku";
            let currentMessageIndex = 0;

            // Update loading message periodically
            function updateLoadingMessage() {
                if (!appLoaded || !animationComplete) {
                    const messages = loadingMessages[currentLang];
                    loadingMessage.textContent = messages[currentMessageIndex];
                    currentMessageIndex = (currentMessageIndex + 1) % messages.length;
                    setTimeout(updateLoadingMessage, 2000);
                }
            }

            // Start updating loading message
            updateLoadingMessage();

            // Animate progress bar
            function updateProgress() {
                // Increment progress based on loading state
                if (!appLoaded) {
                    // Slower progress until app is loaded
                    progressValue += (appLoaded ? 10 : 1);
                    progressValue = Math.min(progressValue, appLoaded ? 100 : 70);
                } else {
                    // Faster progress after app is loaded
                    progressValue += 5;
                    progressValue = Math.min(progressValue, 100);
                }

                // Update progress bar width
                progressBar.style.width = progressValue + '%';

                // Continue animation until complete
                if (progressValue < 100 && (!animationComplete || !appLoaded)) {
                    requestAnimationFrame(() => {
                        setTimeout(updateProgress, 50);
                    });
                }
            }

            // Start progress animation
            updateProgress();

            // Set minimum display time for the animation
            setTimeout(() => {
                animationComplete = true;
                progressValue = Math.max(progressValue, 70);
                progressBar.style.width = progressValue + '%';
                checkRedirect();
            }, 3000);

            // Listen for the app to load in the iframe
            preloadFrame.onload = () => {
                // Try to access the iframe content to ensure it's fully loaded
                try {
                    // Check if the iframe content is accessible
                    if (preloadFrame.contentWindow && preloadFrame.contentWindow.document) {
                        // Wait a bit more to ensure scripts are executed
                        setTimeout(() => {
                            appLoaded = true;
                            progressValue = Math.max(progressValue, 80);
                            progressBar.style.width = progressValue + '%';
                            checkRedirect();
                        }, 1000);
                    } else {
                        console.log('Iframe content not accessible, waiting longer...');
                        setTimeout(() => {
                            appLoaded = true;
                            checkRedirect();
                        }, 2000);
                    }
                } catch (e) {
                    // Cross-origin issues might prevent access, but the page is still loaded
                    console.log('Iframe access error (likely cross-origin):', e);
                    setTimeout(() => {
                        appLoaded = true;
                        progressValue = Math.max(progressValue, 80);
                        progressBar.style.width = progressValue + '%';
                        checkRedirect();
                    }, 1000);
                }
            };

            // Redirect when both conditions are met
            function checkRedirect() {
                if (animationComplete && appLoaded) {
                    // Complete the progress bar
                    progressBar.style.width = '100%';

                    // Update loading message
                    loadingMessage.textContent = currentLang === 'en' ?
                        "Starting application..." :
                        "دەستپێکردنی بەرنامە...";

                    // Wait for progress bar animation to complete
                    setTimeout(() => {
                        // Set a flag in sessionStorage to avoid showing splash again on this session
                        sessionStorage.setItem('splashShown', 'true');

                        // Get parameters from current URL
                        const currentUrl = new URL(window.location.href);
                        const source = currentUrl.searchParams.get('source') || 'pwa';

                        // Redirect to main app with source parameter
                        window.location.href = `/?source=${source}`;
                    }, 300);
                }
            }

            // Fallback in case the iframe doesn't load
            setTimeout(() => {
                if (!appLoaded) {
                    appLoaded = true;
                    checkRedirect();
                }
            }, 8000); // Maximum wait time
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Skip splash screen if already shown in this session
            if (sessionStorage.getItem('splashShown') === 'true') {
                window.location.href = '/?source=pwa';
                return;
            }

            createParticles();
            redirectToApp();
        });

        // For iOS PWA
        if (window.navigator.standalone === true) {
            // Force preload of main app assets
            const preloadLink = document.createElement('link');
            preloadLink.rel = 'preload';
            preloadLink.href = '/';
            preloadLink.as = 'document';
            document.head.appendChild(preloadLink);

            // Preload app icons
            const preloadIcon = document.createElement('link');
            preloadIcon.rel = 'preload';
            preloadIcon.href = '/apple-touch-icon.png';
            preloadIcon.as = 'image';
            document.head.appendChild(preloadIcon);
        }
    </script>
</body>
</html>
