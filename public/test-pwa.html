<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>PWA Test Page</title>
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#2563eb">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #2563eb;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        pre {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
        }
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: none;
        }
    </style>
</head>
<body>
    <h1>PWA Test Page</h1>
    <p>Use this page to test PWA functionality, especially for iOS Safari.</p>

    <div>
        <h2>Service Worker</h2>
        <button id="register-sw">Register Service Worker</button>
        <button id="unregister-sw">Unregister Service Worker</button>
        <button id="check-sw">Check Service Worker Status</button>
        <div id="sw-result" class="result">Results will appear here</div>
    </div>

    <div>
        <h2>Push Notifications</h2>
        <button id="request-permission">Request Permission</button>
        <button id="show-notification">Show Test Notification</button>
        <div id="notification-result" class="result">Results will appear here</div>
    </div>

    <div>
        <h2>Environment Detection</h2>
        <button id="detect-env">Detect Environment</button>
        <div id="env-result" class="result">Results will appear here</div>
    </div>

    <div>
        <h2>IndexedDB Test</h2>
        <button id="test-indexeddb">Test IndexedDB</button>
        <div id="indexeddb-result" class="result">Results will appear here</div>
    </div>

    <div class="notification" id="custom-notification">
        <h3 id="notification-title">Title</h3>
        <p id="notification-body">Body</p>
        <button id="close-notification">Close</button>
    </div>

    <script>
        // Service Worker functions
        document.getElementById('register-sw').addEventListener('click', async () => {
            const resultDiv = document.getElementById('sw-result');

            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.register('/service-worker.js', { scope: '/' });
                    resultDiv.innerHTML = `Service Worker registered with scope: ${registration.scope}`;
                } else {
                    resultDiv.innerHTML = 'Service Workers not supported in this browser';
                }
            } catch (error) {
                resultDiv.innerHTML = `Error registering Service Worker: ${error.message}`;
            }
        });

        document.getElementById('unregister-sw').addEventListener('click', async () => {
            const resultDiv = document.getElementById('sw-result');

            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        await registration.unregister();
                        resultDiv.innerHTML = 'Service Worker unregistered successfully';
                    } else {
                        resultDiv.innerHTML = 'No Service Worker registration found';
                    }
                } else {
                    resultDiv.innerHTML = 'Service Workers not supported in this browser';
                }
            } catch (error) {
                resultDiv.innerHTML = `Error unregistering Service Worker: ${error.message}`;
            }
        });

        document.getElementById('check-sw').addEventListener('click', async () => {
            const resultDiv = document.getElementById('sw-result');

            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        resultDiv.innerHTML = `
                            Service Worker Status:<br>
                            Scope: ${registration.scope}<br>
                            Installing: ${registration.installing ? 'Yes' : 'No'}<br>
                            Waiting: ${registration.waiting ? 'Yes' : 'No'}<br>
                            Active: ${registration.active ? 'Yes' : 'No'}<br>
                            Controller: ${navigator.serviceWorker.controller ? 'Yes' : 'No'}
                        `;
                    } else {
                        resultDiv.innerHTML = 'No Service Worker registration found';
                    }
                } else {
                    resultDiv.innerHTML = 'Service Workers not supported in this browser';
                }
            } catch (error) {
                resultDiv.innerHTML = `Error checking Service Worker: ${error.message}`;
            }
        });

        // Notification functions
        document.getElementById('request-permission').addEventListener('click', async () => {
            const resultDiv = document.getElementById('notification-result');

            try {
                if ('Notification' in window) {
                    const permission = await Notification.requestPermission();
                    resultDiv.innerHTML = `Notification permission: ${permission}`;
                } else {
                    resultDiv.innerHTML = 'Notifications not supported in this browser';
                }
            } catch (error) {
                resultDiv.innerHTML = `Error requesting notification permission: ${error.message}`;
            }
        });

        document.getElementById('show-notification').addEventListener('click', async () => {
            const resultDiv = document.getElementById('notification-result');

            try {
                // First try standard notification
                if ('Notification' in window && Notification.permission === 'granted') {
                    const notification = new Notification('Test Notification', {
                        body: 'This is a test notification',
                        icon: '/logo.png'
                    });

                    resultDiv.innerHTML = 'Standard notification shown';
                } else if (navigator.serviceWorker && navigator.serviceWorker.controller) {
                    // Try service worker notification
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        await registration.showNotification('Test Notification', {
                            body: 'This is a test notification via service worker',
                            icon: '/logo.png'
                        });
                        resultDiv.innerHTML = 'Service Worker notification shown';
                    } else {
                        // Show custom notification
                        showCustomNotification('Test Notification', 'This is a custom notification');
                        resultDiv.innerHTML = 'Custom notification shown';
                    }
                } else {
                    // Show custom notification
                    showCustomNotification('Test Notification', 'This is a custom notification');
                    resultDiv.innerHTML = 'Custom notification shown';
                }
            } catch (error) {
                resultDiv.innerHTML = `Error showing notification: ${error.message}`;
                // Fallback to custom notification
                showCustomNotification('Test Notification', 'This is a custom notification');
            }
        });

        // Environment detection
        document.getElementById('detect-env').addEventListener('click', () => {
            const resultDiv = document.getElementById('env-result');

            const ua = navigator.userAgent;
            const iOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
            const displayModeStandalone = window.matchMedia('(display-mode: standalone)').matches;
            const navigatorStandalone = window.navigator.standalone === true;
            const isPWAContext = document.referrer.startsWith('android-app://') ||
                                navigator.userAgent.includes(' wv') ||
                                window.matchMedia('(display-mode: standalone)').matches;

            const isPWA = displayModeStandalone || navigatorStandalone || isPWAContext;

            resultDiv.innerHTML = `
                User Agent: ${ua}<br>
                iOS Device: ${iOS}<br>
                Running as PWA: ${isPWA}<br>
                display-mode: standalone: ${displayModeStandalone}<br>
                navigator.standalone: ${navigatorStandalone}<br>
                PWA context detection: ${isPWAContext}<br>
                Service Worker supported: ${'serviceWorker' in navigator}<br>
                Push Manager supported: ${'PushManager' in window}<br>
                Notification supported: ${'Notification' in window}<br>
                Notification permission: ${Notification.permission}<br>
                IndexedDB supported: ${!!window.indexedDB}
            `;
        });

        // IndexedDB Test
        document.getElementById('test-indexeddb').addEventListener('click', () => {
            const resultDiv = document.getElementById('indexeddb-result');

            if (!window.indexedDB) {
                resultDiv.innerHTML = 'IndexedDB not supported in this browser';
                return;
            }

            try {
                const request = indexedDB.open('test-db', 1);

                request.onerror = function(event) {
                    resultDiv.innerHTML = `IndexedDB error: ${event.target.errorCode}`;
                };

                request.onupgradeneeded = function(event) {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('test-store')) {
                        db.createObjectStore('test-store', { keyPath: 'id' });
                    }
                };

                request.onsuccess = function(event) {
                    const db = event.target.result;
                    const transaction = db.transaction(['test-store'], 'readwrite');
                    const store = transaction.objectStore('test-store');

                    const testData = {
                        id: Date.now(),
                        title: 'Test Data',
                        timestamp: new Date().toISOString()
                    };

                    const addRequest = store.add(testData);

                    addRequest.onsuccess = function() {
                        resultDiv.innerHTML = `Data successfully added to IndexedDB: ${JSON.stringify(testData)}`;
                    };

                    addRequest.onerror = function(event) {
                        resultDiv.innerHTML = `Error adding data to IndexedDB: ${event.target.errorCode}`;
                    };
                };
            } catch (error) {
                resultDiv.innerHTML = `IndexedDB error: ${error.message}`;
            }
        });

        // Custom notification
        function showCustomNotification(title, body) {
            const notification = document.getElementById('custom-notification');
            document.getElementById('notification-title').textContent = title;
            document.getElementById('notification-body').textContent = body;

            notification.style.display = 'block';

            document.getElementById('close-notification').addEventListener('click', () => {
                notification.style.display = 'none';
            });

            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Check if running as PWA on load
        window.addEventListener('load', () => {
            const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                         (window.navigator.standalone === true);

            if (isPWA) {
                document.body.classList.add('pwa-mode');
                showCustomNotification('PWA Detected', 'Running as installed PWA');
            }

            // Auto-register service worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                        console.log('Service Worker registered with scope:', registration.scope);
                    })
                    .catch(error => {
                        console.error('Service Worker registration failed:', error);
                    });
            }
        });
    </script>
</body>
</html>
