// Service Worker for <PERSON><PERSON><PERSON> PWA
const CACHE_NAME = 'daryay-bawar-v1';
const OFFLINE_URL = '/offline.html';
const SPLASH_SCREEN_URL = '/splash-screen.html';

// Add a version number for cache busting
const APP_VERSION = '1.0.2';

const urlsToCache = [
  '/',
  '/offline.html',
  '/manifest.json',
  '/logo.png',
  '/apple-touch-icon.png',
  '/favicon.ico',
  '/build/assets/app.css',
  '/build/assets/app.js',
  '/splash-screen.html'  // Add splash screen to cache
];

// Install event - cache assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker v' + APP_VERSION);
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching app shell');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('[Service Worker] Install completed');
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker v' + APP_VERSION);
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[Service Worker] Removing old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
    .then(() => {
      console.log('[Service Worker] Claiming clients');
      return self.clients.claim();
    })
  );
});

// Fetch event - serve from cache, fall back to network
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Parse the URL
  const url = new URL(event.request.url);

  // Check if this is a navigation request for the PWA
  if (event.request.mode === 'navigate') {
    // If it's already the splash screen, let it through
    if (url.pathname === '/splash-screen.html') {
      return;
    }

    // If it's the root with source=pwa or standalone mode is detected, show splash screen
    if (url.pathname === '/' &&
        (url.searchParams.get('source') === 'pwa' ||
         event.request.headers.get('Sec-Fetch-Mode') === 'navigate')) {
      console.log('[Service Worker] Redirecting to splash screen');
      event.respondWith(caches.match(SPLASH_SCREEN_URL));
      return;
    }
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          console.log('[Service Worker] Serving from cache:', event.request.url);
          return response;
        }

        console.log('[Service Worker] Fetching resource:', event.request.url);
        return fetch(event.request)
          .then((networkResponse) => {
            // Don't cache if not a valid response or not a GET request
            if (!networkResponse || networkResponse.status !== 200 || event.request.method !== 'GET') {
              return networkResponse;
            }

            // Clone the response
            const responseToCache = networkResponse.clone();

            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
                console.log('[Service Worker] Resource cached:', event.request.url);
              });

            return networkResponse;
          })
          .catch((error) => {
            console.log('[Service Worker] Fetch failed:', error);
            // If fetch fails, show offline page for navigation requests
            if (event.request.mode === 'navigate') {
              console.log('[Service Worker] Serving offline page');
              return caches.match(OFFLINE_URL);
            }

            // For image requests, return a fallback image
            if (event.request.destination === 'image') {
              return caches.match('/logo.png');
            }

            // Let the error propagate for other resources
            throw error;
          });
      })
  );
});

// Push notification event
self.addEventListener('push', (event) => {
  console.log('[Service Worker] Push received');
  let data = {};

  if (event.data) {
    try {
      data = event.data.json();
      console.log('[Service Worker] Push data:', data);
    } catch (e) {
      console.log('[Service Worker] Push has non-JSON data:', event.data.text());
      data = {
        title: 'Daryay Bawar',
        body: event.data.text(),
        icon: '/logo.png'
      };
    }
  }

  const title = data.title || 'Daryay Bawar';
  const options = {
    body: data.body || 'New update available',
    icon: data.icon || '/logo.png',
    badge: '/apple-touch-icon.png',
    data: {
      url: data.url || '/',
      timestamp: Date.now()
    },
    vibrate: [100, 50, 100],
    actions: [
      {
        action: 'explore',
        title: 'View',
      }
    ],
    // For iOS
    renotify: true,
    tag: 'daryay-bawar-notification'
  };

  // Store notification in IndexedDB for iOS
  if (self.indexedDB) {
    const request = self.indexedDB.open('notifications-store', 1);

    request.onupgradeneeded = function(event) {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('notifications')) {
        db.createObjectStore('notifications', { keyPath: 'timestamp' });
      }
    };

    request.onsuccess = function(event) {
      const db = event.target.result;
      const transaction = db.transaction(['notifications'], 'readwrite');
      const store = transaction.objectStore('notifications');

      const notification = {
        title: title,
        body: options.body,
        icon: options.icon,
        timestamp: options.data.timestamp,
        url: options.data.url
      };

      store.add(notification);
    };
  }

  event.waitUntil(
    self.registration.showNotification(title, options)
      .then(() => {
        console.log('[Service Worker] Notification displayed');
        // Broadcast to clients that a notification was shown
        return self.clients.matchAll({ type: 'window' })
          .then(clients => {
            clients.forEach(client => {
              client.postMessage({
                type: 'NOTIFICATION_SHOWN',
                notification: {
                  title: title,
                  body: options.body,
                  timestamp: options.data.timestamp
                }
              });
            });
          });
      })
      .catch(error => {
        console.error('[Service Worker] Error showing notification:', error);
      })
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('[Service Worker] Notification click received');
  event.notification.close();

  const urlToOpen = event.notification.data && event.notification.data.url ?
    event.notification.data.url : '/';

  event.waitUntil(
    self.clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    })
    .then((windowClients) => {
      // Check if there is already a window/tab open with the target URL
      for (let i = 0; i < windowClients.length; i++) {
        const client = windowClients[i];
        // If so, focus on it
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      // If not, open a new window
      if (self.clients.openWindow) {
        return self.clients.openWindow(urlToOpen);
      }
    })
  );
});

// Listen for messages from the client
self.addEventListener('message', (event) => {
  console.log('[Service Worker] Message received:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'CHECK_FOR_UPDATES') {
    // Respond with the current version
    event.ports[0].postMessage({
      type: 'VERSION_INFO',
      version: APP_VERSION
    });
  }

  if (event.data && event.data.type === 'FIX_IOS_PWA') {
    console.log('[Service Worker] Applying iOS PWA fixes');

    // Cache the logo and splash screens for iOS PWA
    const iosCacheFiles = [
      '/logo.png',
      '/apple-touch-icon.png',
      '/splash-screen.html',
      '/splash/apple-splash-2048-2732.png',
      '/splash/apple-splash-1668-2388.png',
      '/splash/apple-splash-1536-2048.png',
      '/splash/apple-splash-1242-2688.png',
      '/splash/apple-splash-1125-2436.png',
      '/splash/apple-splash-828-1792.png',
      '/splash/apple-splash-750-1334.png',
      '/splash/apple-splash-640-1136.png'
    ];

    event.waitUntil(
      caches.open(CACHE_NAME).then(cache => {
        console.log('[Service Worker] Caching iOS PWA assets');
        return cache.addAll(iosCacheFiles);
      })
    );

    // Respond that the fix was applied
    if (event.ports && event.ports[0]) {
      event.ports[0].postMessage({
        type: 'IOS_PWA_FIX_APPLIED',
        success: true
      });
    }
  }
});
