#!/usr/bin/env node

/**
 * Test script for the enhanced currency management system
 * This script tests the new API endpoints and components
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:8000'; // Adjust as needed
const API_BASE = `${BASE_URL}/api`;

// Test data
const testCurrency = {
    name: {
        en: 'Test Currency',
        ku: 'دراوی تاقیکردنەوە'
    },
    code: 'TST',
    symbol: '₮',
    country: 'Test Country',
    decimal_places: 2,
    min_amount: 1,
    max_amount: 1000000,
    order: 999,
    is_active: true,
    is_base_currency: false,
    description: {
        en: 'A test currency for system validation',
        ku: 'دراوێکی تاقیکردنەوە بۆ پشتڕاستکردنەوەی سیستەم'
    }
};

// Helper functions
const log = (message, data = null) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
    if (data) {
        console.log(JSON.stringify(data, null, 2));
    }
};

const error = (message, err = null) => {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
    if (err) {
        console.error(err.response?.data || err.message || err);
    }
};

// Test functions
async function testPublicCurrenciesAPI() {
    log('Testing public currencies API...');
    try {
        const response = await axios.get(`${API_BASE}/public/currencies`);
        
        if (response.data.success) {
            log(`✅ Public currencies API working. Found ${response.data.currencies.length} currencies`);
            
            // Check if currencies have proper flag data
            const currenciesWithFlags = response.data.currencies.filter(c => c.flag_icon);
            log(`✅ ${currenciesWithFlags.length} currencies have flag data`);
            
            // Sample currency data
            if (response.data.currencies.length > 0) {
                log('Sample currency:', response.data.currencies[0]);
            }
            
            return true;
        } else {
            error('Public currencies API returned success: false');
            return false;
        }
    } catch (err) {
        error('Failed to test public currencies API', err);
        return false;
    }
}

async function testCurrencyStatistics() {
    log('Testing currency statistics API...');
    try {
        const response = await axios.get(`${API_BASE}/admin/currencies-statistics`, {
            headers: {
                'Authorization': 'Bearer test-token' // You'll need a valid token
            }
        });
        
        if (response.data.success) {
            log('✅ Currency statistics API working');
            log('Statistics:', response.data.statistics);
            return true;
        } else {
            error('Currency statistics API returned success: false');
            return false;
        }
    } catch (err) {
        if (err.response?.status === 401) {
            log('⚠️  Currency statistics API requires authentication (expected)');
            return true; // This is expected behavior
        }
        error('Failed to test currency statistics API', err);
        return false;
    }
}

async function testFlagMapping() {
    log('Testing flag mapping functionality...');
    
    const testCases = [
        { code: 'USD', expected: 'us' },
        { code: 'EUR', expected: 'eu' },
        { code: 'GBP', expected: 'gb' },
        { code: 'IQD', expected: 'iq' },
        { code: 'KURDISTAN', expected: 'kurdistan' },
        { code: 'XYZ', expected: 'xy' } // fallback case
    ];
    
    // This would normally be tested in the frontend, but we can simulate it
    const mapCurrencyToCountryCode = (currencyCode) => {
        const mapping = {
            'USD': 'us',
            'EUR': 'eu',
            'GBP': 'gb',
            'JPY': 'jp',
            'CAD': 'ca',
            'AUD': 'au',
            'CHF': 'ch',
            'CNY': 'cn',
            'INR': 'in',
            'TRY': 'tr',
            'IRR': 'ir',
            'IQD': 'iq',
            'SAR': 'sa',
            'AED': 'ae',
            'KWD': 'kw',
            'BHD': 'bh',
            'OMR': 'om',
            'QAR': 'qa',
            'JOD': 'jo',
            'LBP': 'lb',
            'EGP': 'eg',
            'KURDISTAN': 'kurdistan'
        };
        
        return mapping[currencyCode.toUpperCase()] || currencyCode.toLowerCase().slice(0, 2);
    };
    
    let allPassed = true;
    
    for (const testCase of testCases) {
        const result = mapCurrencyToCountryCode(testCase.code);
        if (result === testCase.expected) {
            log(`✅ ${testCase.code} -> ${result}`);
        } else {
            error(`❌ ${testCase.code} -> ${result} (expected: ${testCase.expected})`);
            allPassed = false;
        }
    }
    
    return allPassed;
}

async function testComponentStructure() {
    log('Testing component file structure...');
    
    const fs = require('fs');
    const path = require('path');
    
    const requiredFiles = [
        'resources/js/components/CountryFlag.vue',
        'resources/js/components/Currency/CurrencySelector.vue',
        'resources/js/components/Currency/FlagSelector.vue',
        'resources/js/components/Currency/CurrencyForm.vue',
        'app/Services/CurrencyManagementService.php',
        'app/Http/Resources/CurrencyResource.php',
        'app/Http/Requests/StoreCurrencyRequest.php',
        'app/Http/Requests/UpdateCurrencyRequest.php'
    ];
    
    let allExist = true;
    
    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            log(`✅ ${file} exists`);
        } else {
            error(`❌ ${file} missing`);
            allExist = false;
        }
    }
    
    return allExist;
}

// Main test runner
async function runTests() {
    log('🚀 Starting Currency Management System Tests');
    log('='.repeat(50));
    
    const results = {
        publicAPI: await testPublicCurrenciesAPI(),
        statistics: await testCurrencyStatistics(),
        flagMapping: await testFlagMapping(),
        componentStructure: await testComponentStructure()
    };
    
    log('='.repeat(50));
    log('📊 Test Results Summary:');
    
    let passedTests = 0;
    let totalTests = 0;
    
    for (const [testName, passed] of Object.entries(results)) {
        totalTests++;
        if (passed) {
            passedTests++;
            log(`✅ ${testName}: PASSED`);
        } else {
            log(`❌ ${testName}: FAILED`);
        }
    }
    
    log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        log('🎉 All tests passed! The currency management system is ready.');
    } else {
        log('⚠️  Some tests failed. Please check the issues above.');
    }
    
    return passedTests === totalTests;
}

// Run tests if this script is executed directly
if (require.main === module) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(err => {
        error('Test runner failed', err);
        process.exit(1);
    });
}

module.exports = { runTests, testPublicCurrenciesAPI, testFlagMapping };
