<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCurrencyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->can('create currencies');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array'],
            'name.en' => ['required', 'string', 'max:255'],
            'name.ku' => ['nullable', 'string', 'max:255'],
            'code' => [
                'required',
                'string',
                'max:3',
                'min:3',
                'uppercase',
                Rule::unique('currencies', 'code')
            ],
            'symbol' => ['nullable', 'string', 'max:10'],
            'country' => ['nullable', 'string', 'max:255'],
            'order' => ['nullable', 'integer', 'min:0'],
            'is_active' => ['boolean'],
            'is_base_currency' => ['boolean'],
            'decimal_places' => ['required', 'integer', 'min:0', 'max:8'],
            'flag_icon' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'array'],
            'description.en' => ['nullable', 'string', 'max:1000'],
            'description.ku' => ['nullable', 'string', 'max:1000'],
            'min_amount' => ['required', 'numeric', 'min:0'],
            'max_amount' => ['required', 'numeric', 'gt:min_amount'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Currency name is required.',
            'name.en.required' => 'English currency name is required.',
            'code.required' => 'Currency code is required.',
            'code.unique' => 'This currency code already exists.',
            'code.uppercase' => 'Currency code must be uppercase.',
            'code.min' => 'Currency code must be exactly 3 characters.',
            'code.max' => 'Currency code must be exactly 3 characters.',
            'decimal_places.required' => 'Decimal places is required.',
            'decimal_places.max' => 'Decimal places cannot exceed 8.',
            'min_amount.required' => 'Minimum amount is required.',
            'max_amount.required' => 'Maximum amount is required.',
            'max_amount.gt' => 'Maximum amount must be greater than minimum amount.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name.en' => 'English name',
            'name.ku' => 'Kurdish name',
            'description.en' => 'English description',
            'description.ku' => 'Kurdish description',
            'decimal_places' => 'decimal places',
            'min_amount' => 'minimum amount',
            'max_amount' => 'maximum amount',
            'is_active' => 'active status',
            'is_base_currency' => 'base currency status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure code is uppercase
        if ($this->has('code')) {
            $this->merge([
                'code' => strtoupper($this->code),
            ]);
        }

        // Set default values
        $this->merge([
            'is_active' => $this->boolean('is_active', true),
            'is_base_currency' => $this->boolean('is_base_currency', false),
            'order' => $this->order ?? 0,
        ]);

        // Auto-generate flag_icon if not provided
        if (!$this->has('flag_icon') && $this->has('code')) {
            $this->merge([
                'flag_icon' => $this->mapCurrencyToCountryCode($this->code),
            ]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation logic
            if ($this->is_base_currency) {
                // Check if there's already a base currency (only for create)
                $existingBaseCurrency = \App\Models\Currency::where('is_base_currency', true)->first();
                if ($existingBaseCurrency) {
                    $validator->addFailure('is_base_currency', 'Only one base currency is allowed. Current base currency is: ' . $existingBaseCurrency->code);
                }
            }

            // Validate currency code format
            if ($this->code && !preg_match('/^[A-Z]{3}$/', $this->code)) {
                $validator->addFailure('code', 'Currency code must be exactly 3 uppercase letters.');
            }
        });
    }

    /**
     * Map currency code to country code for flags
     */
    private function mapCurrencyToCountryCode(string $currencyCode): string
    {
        $mapping = [
            'USD' => 'us',
            'EUR' => 'eu',
            'GBP' => 'gb',
            'JPY' => 'jp',
            'CAD' => 'ca',
            'AUD' => 'au',
            'CHF' => 'ch',
            'CNY' => 'cn',
            'INR' => 'in',
            'TRY' => 'tr',
            'IRR' => 'ir',
            'IQD' => 'iq',
            'SAR' => 'sa',
            'AED' => 'ae',
            'KWD' => 'kw',
            'BHD' => 'bh',
            'OMR' => 'om',
            'QAR' => 'qa',
            'JOD' => 'jo',
            'LBP' => 'lb',
            'EGP' => 'eg',
            'MAD' => 'ma',
            'DZD' => 'dz',
            'TND' => 'tn',
            'RUB' => 'ru',
            'BRL' => 'br',
            'MXN' => 'mx',
            'KRW' => 'kr',
            'SGD' => 'sg',
            'HKD' => 'hk',
            'NZD' => 'nz',
            'SEK' => 'se',
            'NOK' => 'no',
            'DKK' => 'dk',
            'PLN' => 'pl',
            'CZK' => 'cz',
            'HUF' => 'hu',
            'RON' => 'ro',
            'BGN' => 'bg',
            'HRK' => 'hr',
            'RSD' => 'rs',
            'KURDISTAN' => 'kurdistan',
        ];

        return $mapping[strtoupper($currencyCode)] ?? strtolower(substr($currencyCode, 0, 2));
    }
}
