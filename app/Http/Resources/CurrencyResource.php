<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CurrencyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'symbol' => $this->symbol,
            'country' => $this->country,
            'flag_icon' => $this->flag_icon,
            'flag_country_code' => $this->mapCurrencyToCountryCode($this->code),
            'order' => $this->order,
            'is_active' => $this->is_active,
            'is_base_currency' => $this->is_base_currency,
            'decimal_places' => $this->decimal_places,
            'description' => $this->description,
            'min_amount' => $this->min_amount,
            'max_amount' => $this->max_amount,
            'display_name' => $this->display_name,
            'formatted_min_amount' => $this->formatAmount($this->min_amount),
            'formatted_max_amount' => $this->formatAmount($this->max_amount),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Conditional includes
            'exchange_rates_from' => ExchangeRateResource::collection($this->whenLoaded('exchangeRatesFrom')),
            'exchange_rates_to' => ExchangeRateResource::collection($this->whenLoaded('exchangeRatesTo')),
            'recent_history' => ExchangeRateHistoryResource::collection($this->whenLoaded('historyFrom')),
            
            // Statistics (when requested)
            'statistics' => $this->when($request->has('include_stats'), function () {
                return [
                    'total_rates_from' => $this->exchangeRatesFrom()->count(),
                    'total_rates_to' => $this->exchangeRatesTo()->count(),
                    'active_rates_from' => $this->exchangeRatesFrom()->active()->count(),
                    'active_rates_to' => $this->exchangeRatesTo()->active()->count(),
                    'last_rate_update' => $this->exchangeRatesFrom()->latest('updated_at')->first()?->updated_at,
                ];
            }),
        ];
    }

    /**
     * Map currency code to country code for flags
     */
    private function mapCurrencyToCountryCode(string $currencyCode): string
    {
        $mapping = [
            'USD' => 'us',
            'EUR' => 'eu',
            'GBP' => 'gb',
            'JPY' => 'jp',
            'CAD' => 'ca',
            'AUD' => 'au',
            'CHF' => 'ch',
            'CNY' => 'cn',
            'INR' => 'in',
            'TRY' => 'tr',
            'IRR' => 'ir',
            'IQD' => 'iq',
            'SAR' => 'sa',
            'AED' => 'ae',
            'KWD' => 'kw',
            'BHD' => 'bh',
            'OMR' => 'om',
            'QAR' => 'qa',
            'JOD' => 'jo',
            'LBP' => 'lb',
            'EGP' => 'eg',
            'MAD' => 'ma',
            'DZD' => 'dz',
            'TND' => 'tn',
            'RUB' => 'ru',
            'BRL' => 'br',
            'MXN' => 'mx',
            'KRW' => 'kr',
            'SGD' => 'sg',
            'HKD' => 'hk',
            'NZD' => 'nz',
            'SEK' => 'se',
            'NOK' => 'no',
            'DKK' => 'dk',
            'PLN' => 'pl',
            'CZK' => 'cz',
            'HUF' => 'hu',
            'RON' => 'ro',
            'BGN' => 'bg',
            'HRK' => 'hr',
            'RSD' => 'rs',
            'KURDISTAN' => 'kurdistan',
        ];

        return $mapping[strtoupper($currencyCode)] ?? strtolower(substr($currencyCode, 0, 2));
    }
}
