<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'is_active' => $this->is_active ?? true,
            'email_verified_at' => $this->email_verified_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'last_login_at' => $this->last_login_at ?? null,
            
            // Role information
            'roles' => $this->whenLoaded('roles', function () {
                return $this->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'guard_name' => $role->guard_name,
                        'permissions' => $this->whenLoaded('permissions', function () use ($role) {
                            return $role->permissions->map(function ($permission) {
                                return [
                                    'id' => $permission->id,
                                    'name' => $permission->name,
                                    'guard_name' => $permission->guard_name,
                                ];
                            });
                        }),
                    ];
                });
            }),
            
            // Direct permissions
            'permissions' => $this->whenLoaded('permissions', function () {
                return $this->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'guard_name' => $permission->guard_name,
                    ];
                });
            }),
            
            // Computed fields for frontend compatibility
            'role' => $this->whenLoaded('roles', function () {
                return $this->roles->first()?->name ?? 'No Role';
            }),
            
            'is_online' => $this->last_login_at && $this->last_login_at->gt(now()->subMinutes(15)),
            
            'last_login' => $this->last_login_at?->toISOString(),
            
            // Avatar placeholder (you can implement file upload later)
            'avatar' => null,
        ];
    }
}
