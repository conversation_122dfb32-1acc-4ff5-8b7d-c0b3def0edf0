<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExchangeRateHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'from_currency_id' => $this->from_currency_id,
            'to_currency_id' => $this->to_currency_id,
            'rate' => $this->rate,
            'previous_rate' => $this->previous_rate,
            'change_percentage' => $this->change_percentage,
            'change_type' => $this->change_type,
            'source' => $this->source,
            'notes' => $this->notes,
            'effective_date' => $this->effective_date,
            'created_at' => $this->created_at,
            'created_by' => $this->created_by,
            
            // Formatted values
            'formatted_rate' => number_format($this->rate, 8),
            'formatted_previous_rate' => $this->previous_rate ? number_format($this->previous_rate, 8) : null,
            'formatted_change_percentage' => $this->formatted_change_percentage,
            'formatted_effective_date' => $this->effective_date?->format('Y-m-d H:i:s'),
            'human_readable_date' => $this->effective_date?->diffForHumans(),
            
            // Related currencies
            'from_currency' => new CurrencyResource($this->whenLoaded('fromCurrency')),
            'to_currency' => new CurrencyResource($this->whenLoaded('toCurrency')),
            'created_by_user' => $this->whenLoaded('createdBy', function () {
                return [
                    'id' => $this->createdBy->id,
                    'name' => $this->createdBy->name,
                    'email' => $this->createdBy->email,
                ];
            }),
            
            // Change indicators
            'change_indicator' => [
                'icon' => $this->getChangeIcon(),
                'color' => $this->getChangeColor(),
                'direction' => $this->change_type,
                'magnitude' => $this->getChangeMagnitude(),
            ],
            
            // Rate comparison
            'rate_comparison' => $this->when($this->previous_rate, function () {
                return [
                    'absolute_change' => $this->rate - $this->previous_rate,
                    'percentage_change' => $this->change_percentage,
                    'is_significant' => abs($this->change_percentage) > 1, // More than 1% change
                    'direction_text' => $this->getDirectionText(),
                ];
            }),
        ];
    }
    
    /**
     * Get change icon based on change type
     */
    private function getChangeIcon(): string
    {
        return match ($this->change_type) {
            'increase' => 'trending_up',
            'decrease' => 'trending_down',
            default => 'trending_flat',
        };
    }
    
    /**
     * Get change color based on change type
     */
    private function getChangeColor(): string
    {
        return match ($this->change_type) {
            'increase' => 'positive',
            'decrease' => 'negative',
            default => 'grey',
        };
    }
    
    /**
     * Get change magnitude description
     */
    private function getChangeMagnitude(): string
    {
        $abs = abs($this->change_percentage);
        
        if ($abs >= 5) {
            return 'major';
        } elseif ($abs >= 1) {
            return 'moderate';
        } elseif ($abs >= 0.1) {
            return 'minor';
        }
        
        return 'minimal';
    }
    
    /**
     * Get direction text
     */
    private function getDirectionText(): string
    {
        return match ($this->change_type) {
            'increase' => 'increased',
            'decrease' => 'decreased',
            default => 'remained stable',
        };
    }
}
