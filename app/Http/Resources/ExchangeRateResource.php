<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExchangeRateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'from_currency_id' => $this->from_currency_id,
            'to_currency_id' => $this->to_currency_id,
            'rate' => $this->rate,
            'formatted_rate' => number_format($this->rate, 8),
            'is_active' => $this->is_active,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            
            // Related currencies
            'from_currency' => new CurrencyResource($this->whenLoaded('fromCurrency')),
            'to_currency' => new CurrencyResource($this->whenLoaded('toCurrency')),
            
            // Rate pair display
            'rate_pair' => $this->when($this->relationLoaded('fromCurrency') && $this->relationLoaded('toCurrency'), function () {
                return [
                    'from' => [
                        'code' => $this->fromCurrency->code,
                        'name' => $this->fromCurrency->name,
                        'symbol' => $this->fromCurrency->symbol,
                        'flag_icon' => $this->fromCurrency->flag_icon,
                    ],
                    'to' => [
                        'code' => $this->toCurrency->code,
                        'name' => $this->toCurrency->name,
                        'symbol' => $this->toCurrency->symbol,
                        'flag_icon' => $this->toCurrency->flag_icon,
                    ],
                    'display' => "{$this->fromCurrency->code}/{$this->toCurrency->code}",
                    'rate_display' => "1 {$this->fromCurrency->code} = {$this->formatted_rate} {$this->toCurrency->code}",
                ];
            }),
            
            // Recent history (when requested)
            'recent_history' => ExchangeRateHistoryResource::collection($this->whenLoaded('history')),
            
            // Rate statistics (when requested)
            'statistics' => $this->when($request->has('include_stats'), function () {
                $history = $this->history()->latest()->limit(30)->get();
                
                if ($history->isEmpty()) {
                    return null;
                }
                
                $rates = $history->pluck('rate');
                $changes = $history->pluck('change_percentage');
                
                return [
                    'avg_rate_30d' => $rates->avg(),
                    'min_rate_30d' => $rates->min(),
                    'max_rate_30d' => $rates->max(),
                    'volatility_30d' => $changes->map(fn($c) => abs($c))->avg(),
                    'trend' => $this->calculateTrend($history),
                    'last_change' => $history->first()?->change_percentage,
                    'last_change_type' => $history->first()?->change_type,
                ];
            }),
        ];
    }
    
    /**
     * Calculate trend based on recent history
     */
    private function calculateTrend($history)
    {
        if ($history->count() < 2) {
            return 'stable';
        }
        
        $recent = $history->take(5);
        $increases = $recent->where('change_type', 'increase')->count();
        $decreases = $recent->where('change_type', 'decrease')->count();
        
        if ($increases > $decreases) {
            return 'upward';
        } elseif ($decreases > $increases) {
            return 'downward';
        }
        
        return 'stable';
    }
}
