<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check for user preference in session
        if (session()->has('locale')) {
            $locale = session()->get('locale');
        } else {
            // Get browser language
            $browserLang = substr($request->server('HTTP_ACCEPT_LANGUAGE') ?? 'en', 0, 2);

            // Check if we have a translation for this language
            $locale = in_array($browserLang, ['en', 'ku']) ? $browserLang : 'en';

            // Store in session
            session()->put('locale', $locale);
        }

        // Set application locale
        App::setLocale($locale);

        return $next($request);
    }
}
