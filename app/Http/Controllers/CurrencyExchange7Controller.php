<?php

namespace App\Http\Controllers;

use App\Models\CurrencyExchangeRate;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CurrencyExchange7Controller extends Controller
{
    public function index()
    {
        $rates = CurrencyExchangeRate::active()->get();
        return Inertia::render('Admin/CurrencyExchange/Index', [
            'rates' => $rates
        ]);
    }

    public function getRates()
    {
        $rates = CurrencyExchangeRate::active()
            ->select('id', 'from_currency', 'to_currency', 'rate', 'updated_at')
            ->get();

        return response()->json($rates);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3',
            'rate' => 'required|numeric|min:0',
        ]);

        $exchangeRate = CurrencyExchangeRate::updateOrCreate(
            [
                'from_currency' => $validated['from_currency'],
                'to_currency' => $validated['to_currency'],
            ],
            [
                'rate' => $validated['rate'],
                'is_active' => true,
            ]
        );

        return redirect()->back()->with('success', 'Exchange rate updated successfully');
    }

    public function convert(Request $request)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric|min:0',
            'from_currency' => 'required|string|size:3',
            'to_currency' => 'required|string|size:3',
        ]);

        $rate = CurrencyExchangeRate::where('from_currency', $validated['from_currency'])
            ->where('to_currency', $validated['to_currency'])
            ->where('is_active', true)
            ->first();

        if (!$rate) {
            return response()->json([
                'error' => 'Exchange rate not found'
            ], 404);
        }

        $convertedAmount = $validated['amount'] * $rate->rate;

        return response()->json([
            'original_amount' => $validated['amount'],
            'from_currency' => $validated['from_currency'],
            'to_currency' => $validated['to_currency'],
            'rate' => $rate->rate,
            'converted_amount' => $convertedAmount
        ]);
    }
}
