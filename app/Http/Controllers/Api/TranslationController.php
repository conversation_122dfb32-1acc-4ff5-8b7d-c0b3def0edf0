<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Spatie\Translatable\HasTranslations;

class TranslationController extends Controller
{
    public function getTranslations($locale)
    {
        // Use cache to improve performance
        $cacheKey = "translations.{$locale}";

        return Cache::remember($cacheKey, 60 * 60, function () use ($locale) {
            // Load JSON translations from Laravel's lang directory
            $laravelTranslations = [];
            $jsonPath = lang_path("{$locale}.json");

            if (File::exists($jsonPath)) {
                $laravelTranslations = json_decode(File::get($jsonPath), true);
            }

            // Load PHP translations from Laravel's lang directory
            $phpTranslations = [];
            $phpPath = lang_path($locale);

            if (File::exists($phpPath)) {
                foreach (File::allFiles($phpPath) as $file) {
                    $group = $file->getBasename('.php');
                    $phpTranslations[$group] = require $file->getPathname();
                }
            }

            // Load Spatie translations from the database
            $spatieTranslations = $this->getSpatieTranslations($locale);

            // Merge all translations
            $translations = array_merge(
                $laravelTranslations,
                $phpTranslations,
                $spatieTranslations
            );

            return $translations;
        });
    }

    /**
     * Get translations from Spatie translatable models
     */
    private function getSpatieTranslations($locale)
    {
        $translations = [];

        // Get all models that use the Translatable trait
        $models = $this->getTranslatableModels();

        foreach ($models as $model) {
            $modelName = class_basename($model);
            $tableName = (new $model)->getTable();

            // Get all records for this model
            $records = $model::all();

            foreach ($records as $record) {
                foreach ($record->getTranslatableAttributes() as $attribute) {
                    $key = "{$tableName}.{$record->id}.{$attribute}";
                    $translations[$key] = $record->getTranslation($attribute, $locale);
                }
            }
        }

        return $translations;
    }

    /**
     * Get all models that use the Translatable trait
     */
    private function getTranslatableModels()
    {
        $models = [];
        $modelPath = app_path('Models');

        foreach (File::allFiles($modelPath) as $file) {
            $className = 'App\\Models\\' . basename($file->getFilename(), '.php');

            if (class_exists($className)) {
                $reflection = new \ReflectionClass($className);

                if ($reflection->isSubclassOf('Illuminate\\Database\\Eloquent\\Model') &&
                    in_array(HasTranslations::class, $reflection->getTraitNames())) {
                    $models[] = $className;
                }
            }
        }

        return $models;
    }
}
