<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use App\Models\ExchangeRateHistory;
use App\Services\ExchangeRateService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    protected $exchangeRateService;

    public function __construct(ExchangeRateService $exchangeRateService)
    {
        $this->exchangeRateService = $exchangeRateService;
    }

    /**
     * Get dashboard statistics
     */
    public function getStats()
    {
        $currencies = Currency::count();
        $activeCurrencies = Currency::where('is_active', true)->count();
        $totalRates = CurrencyExchangeRate::count();
        $activeRates = CurrencyExchangeRate::where('is_active', true)->count();

        // Get today's updates
        $todayUpdates = ExchangeRateHistory::whereDate('created_at', today())->count();

        // Calculate average volatility (last 24 hours)
        $yesterday = Carbon::now()->subDay();
        $volatilityData = ExchangeRateHistory::where('created_at', '>=', $yesterday)
            ->whereNotNull('change_percentage')
            ->avg('change_percentage');

        $avgVolatility = $volatilityData ? abs($volatilityData) : 0;

        return response()->json([
            'success' => true,
            'data' => [
                'total_currencies' => $currencies,
                'active_currencies' => $activeCurrencies,
                'total_rates' => $totalRates,
                'active_rates' => $activeRates,
                'total_updates' => $todayUpdates,
                'avg_volatility' => round($avgVolatility, 2)
            ]
        ]);
    }

    /**
     * Get chart data for dashboard
     */
    public function getChartData(Request $request)
    {
        $period = $request->get('period', 7);
        $startDate = Carbon::now()->subDays($period);

        $chartData = ExchangeRateHistory::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $chartData
        ]);
    }

    /**
     * Get recent activity
     */
    public function getRecentActivity()
    {
        $recentActivity = ExchangeRateHistory::with(['fromCurrency', 'toCurrency', 'createdBy'])
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($history) {
                return [
                    'id' => $history->id,
                    'type' => 'rate_update',
                    'description' => 'Exchange rate updated',
                    'from_currency' => $history->fromCurrency->code,
                    'to_currency' => $history->toCurrency->code,
                    'rate' => number_format($history->rate, 4),
                    'source' => $history->source,
                    'created_at' => $history->created_at->toISOString(),
                    'user' => $history->createdBy ? $history->createdBy->name : 'System'
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $recentActivity
        ]);
    }

    /**
     * Trigger manual rate update
     */
    public function triggerRateUpdate(Request $request)
    {
        $provider = $request->get('provider', 'exchangerate');
        $baseCurrency = $request->get('base_currency', 'USD');

        try {
            $updatedCount = $this->exchangeRateService->updateRatesFromAPI($provider, $baseCurrency);

            if ($updatedCount === false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update exchange rates. Check logs for details.'
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updatedCount} exchange rates",
                'data' => [
                    'updated_count' => $updatedCount,
                    'provider' => $provider,
                    'base_currency' => $baseCurrency
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating rates: ' . $e->getMessage()
            ], 500);
        }
    }
}
