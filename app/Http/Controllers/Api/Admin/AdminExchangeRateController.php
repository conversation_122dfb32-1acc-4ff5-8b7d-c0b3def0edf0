<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use App\Models\ExchangeRateHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminExchangeRateController extends Controller
{
    /**
     * Display a listing of exchange rates
     */
    public function index(Request $request)
    {
        $query = CurrencyExchangeRate::with(['fromCurrency', 'toCurrency']);

        // Filter by currency pair
        if ($request->from_currency_id) {
            $query->where('from_currency_id', $request->from_currency_id);
        }

        if ($request->to_currency_id) {
            $query->where('to_currency_id', $request->to_currency_id);
        }

        // Filter by status
        if ($request->has('is_active') && $request->is_active !== null) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by source
        if ($request->source) {
            $query->where('source', $request->source);
        }

        $rates = $query->latest()
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'rates' => $rates
        ]);
    }

    /**
     * Store a newly created exchange rate
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_currency_id' => 'required|exists:currencies,id',
            'to_currency_id' => 'required|exists:currencies,id|different:from_currency_id',
            'rate' => 'required|numeric|min:0.00000001',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check if rate already exists for this pair
        $existingRate = CurrencyExchangeRate::forCurrencyPair(
            $request->from_currency_id,
            $request->to_currency_id
        )->first();

        if ($existingRate) {
            return response()->json([
                'success' => false,
                'message' => 'Exchange rate already exists for this currency pair'
            ], 422);
        }

        $rate = CurrencyExchangeRate::createOrUpdateRate(
            $request->from_currency_id,
            $request->to_currency_id,
            $request->rate,
            'manual',
            $request->notes,
            Auth::id()
        );

        $rate->load(['fromCurrency', 'toCurrency']);

        return response()->json([
            'success' => true,
            'message' => 'Exchange rate created successfully',
            'rate' => $rate
        ], 201);
    }

    /**
     * Display the specified exchange rate
     */
    public function show(CurrencyExchangeRate $rate)
    {
        $rate->load(['fromCurrency', 'toCurrency']);

        // Get rate history
        $history = $rate->getLatestHistory(90);

        return response()->json([
            'success' => true,
            'rate' => $rate,
            'history' => $history
        ]);
    }

    /**
     * Update the specified exchange rate
     */
    public function update(Request $request, CurrencyExchangeRate $rate)
    {
        $request->validate([
            'rate' => 'required|numeric|min:0.00000001',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Update rate with history tracking
        $rate->updateRate(
            $request->rate,
            'manual',
            $request->notes,
            Auth::id()
        );

        // Update status if changed
        if ($request->has('is_active')) {
            $rate->update(['is_active' => $request->is_active]);
        }

        $rate->load(['fromCurrency', 'toCurrency']);

        return response()->json([
            'success' => true,
            'message' => 'Exchange rate updated successfully',
            'rate' => $rate
        ]);
    }

    /**
     * Remove the specified exchange rate
     */
    public function destroy(CurrencyExchangeRate $rate)
    {
        $rate->delete();

        return response()->json([
            'success' => true,
            'message' => 'Exchange rate deleted successfully'
        ]);
    }

    /**
     * Toggle exchange rate status
     */
    public function toggleStatus(CurrencyExchangeRate $rate)
    {
        $rate->update(['is_active' => !$rate->is_active]);

        return response()->json([
            'success' => true,
            'message' => 'Exchange rate status updated successfully',
            'rate' => $rate
        ]);
    }

    /**
     * Bulk update exchange rates
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'rates' => 'required|array',
            'rates.*.id' => 'required|exists:currency_exchange_rates,id',
            'rates.*.rate' => 'required|numeric|min:0.00000001',
            'notes' => 'nullable|string|max:1000',
        ]);

        $updatedCount = 0;

        foreach ($request->rates as $rateData) {
            $exchangeRate = CurrencyExchangeRate::find($rateData['id']);
            if ($exchangeRate) {
                $exchangeRate->updateRate(
                    $rateData['rate'],
                    'manual',
                    $request->notes,
                    Auth::id()
                );
                $updatedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully updated {$updatedCount} exchange rates",
            'updated_count' => $updatedCount
        ]);
    }

    /**
     * Get exchange rate history
     */
    public function getHistory(CurrencyExchangeRate $rate, Request $request)
    {
        $period = $request->get('period', 30);

        $history = ExchangeRateHistory::forCurrencyPair(
            $rate->from_currency_id,
            $rate->to_currency_id
        )
            ->latest()
            ->limit($period)
            ->get();

        return response()->json([
            'success' => true,
            'history' => $history
        ]);
    }

    /**
     * Check if exchange rate exists for currency pair
     */
    public function checkExisting(Request $request)
    {
        $request->validate([
            'from_currency_id' => 'required|exists:currencies,id',
            'to_currency_id' => 'required|exists:currencies,id',
        ]);

        $existingRate = CurrencyExchangeRate::forCurrencyPair(
            $request->from_currency_id,
            $request->to_currency_id
        )->first();

        return response()->json([
            'success' => true,
            'exists' => $existingRate !== null,
            'rate' => $existingRate ? [
                'id' => $existingRate->id,
                'rate' => $existingRate->rate,
                'updated_at' => $existingRate->updated_at
            ] : null
        ]);
    }
}
