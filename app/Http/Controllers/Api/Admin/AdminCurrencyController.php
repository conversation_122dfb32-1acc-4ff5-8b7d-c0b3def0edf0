<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Services\CurrencyManagementService;
use App\Http\Resources\CurrencyResource;
use App\Http\Requests\StoreCurrencyRequest;
use App\Http\Requests\UpdateCurrencyRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class AdminCurrencyController extends Controller
{
    protected CurrencyManagementService $currencyService;

    public function __construct(CurrencyManagementService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * Display a listing of currencies with enhanced filtering and statistics
     */
    public function index(Request $request)
    {
        $query = Currency::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('code', 'like', '%' . $request->search . '%')
                    ->orWhere('country', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->has('is_active') && $request->is_active !== null) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by base currency
        if ($request->has('is_base_currency') && $request->is_base_currency !== null) {
            $query->where('is_base_currency', $request->boolean('is_base_currency'));
        }

        $currencies = $query->ordered()
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'currencies' => $currencies
        ]);
    }

    /**
     * Store a newly created currency
     */
    public function store(StoreCurrencyRequest $request): JsonResponse
    {
        try {
            $currency = $this->currencyService->createCurrency($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Currency created successfully',
                'currency' => new CurrencyResource($currency)
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create currency',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Display the specified currency
     */
    public function show(Currency $currency, Request $request): JsonResponse
    {
        try {
            $currency->load(['exchangeRatesFrom.toCurrency', 'exchangeRatesTo.fromCurrency']);

            // Get recent history
            $recentHistory = $currency->historyFrom()
                ->with(['toCurrency', 'createdBy'])
                ->latest()
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'currency' => new CurrencyResource($currency),
                'recent_history' => $recentHistory
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch currency details',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update the specified currency
     */
    public function update(UpdateCurrencyRequest $request, Currency $currency): JsonResponse
    {
        try {
            $updatedCurrency = $this->currencyService->updateCurrency($currency, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Currency updated successfully',
                'currency' => new CurrencyResource($updatedCurrency)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update currency',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Archive the specified currency (soft delete)
     */
    public function destroy(Currency $currency): JsonResponse
    {
        try {
            // Check if this is the base currency
            if ($currency->is_base_currency) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot archive the base currency. Please set another currency as base first.'
                ], 422);
            }

            $this->currencyService->archiveCurrency($currency);

            return response()->json([
                'success' => true,
                'message' => 'Currency archived successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to archive currency',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Toggle currency status
     */
    public function toggleStatus(Currency $currency): JsonResponse
    {
        try {
            // Prevent deactivating base currency
            if ($currency->is_base_currency && $currency->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot deactivate the base currency. Please set another currency as base first.'
                ], 422);
            }

            $currency->update(['is_active' => !$currency->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Currency status updated successfully',
                'currency' => new CurrencyResource($currency)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update currency status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get currency statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $statistics = $this->currencyService->getCurrencyStatistics();

            return response()->json([
                'success' => true,
                'statistics' => $statistics
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Bulk update currency orders
     */
    public function updateOrders(Request $request): JsonResponse
    {
        $request->validate([
            'orders' => 'required|array',
            'orders.*' => 'required|integer|min:0'
        ]);

        try {
            $this->currencyService->updateCurrencyOrders($request->orders);

            return response()->json([
                'success' => true,
                'message' => 'Currency orders updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update currency orders',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get currencies for public display
     */
    public function publicCurrencies(): JsonResponse
    {
        try {
            $currencies = $this->currencyService->getPublicCurrencies();

            return response()->json([
                'success' => true,
                'currencies' => $currencies
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch public currencies',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}
