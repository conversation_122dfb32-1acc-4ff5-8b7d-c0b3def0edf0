<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class ExchangeRateController extends Controller
{
    /**
     * Get all active currencies
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrencies(Request $request)
    {
        // Set locale based on request header
        $locale = $request->header('Accept-Language', 'en');
        App::setLocale($locale);

        $currencies = Currency::active()
            ->ordered()
            ->get()
            ->map(function ($currency) {
                return [
                    'id' => $currency->id,
                    'name' => $currency->name,
                    'code' => $currency->code,
                    'symbol' => $currency->symbol,
                    'country' => $currency->country,
                    'order' => $currency->order,
                    'is_active' => $currency->is_active,
                    'is_base_currency' => $currency->is_base_currency,
                    'decimal_places' => $currency->decimal_places,
                    'flag_icon' => $currency->flag_icon,
                    'flag_country_code' => $currency->flag_country_code,
                    'flag_info' => $currency->flag_info,
                    'description' => $currency->description,
                    'min_amount' => $currency->min_amount,
                    'max_amount' => $currency->max_amount,
                    'created_at' => $currency->created_at,
                    'updated_at' => $currency->updated_at,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $currencies,
            'timestamp' => now()
        ]);
    }

    /**
     * Get all active exchange rates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExchangeRates()
    {
        $rates = CurrencyExchangeRate::with(['fromCurrency', 'toCurrency'])
            ->active()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $rates,
            'timestamp' => now()
        ]);
    }

    /**
     * Convert an amount between currencies
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function convertCurrency(Request $request)
    {
        $request->validate([
            'from' => 'required|string|exists:currencies,code',
            'to' => 'required|string|exists:currencies,code',
            'amount' => 'required|numeric|min:0'
        ]);

        $fromCurrency = Currency::where('code', $request->from)->first();
        $toCurrency = Currency::where('code', $request->to)->first();
        $amount = $request->amount;

        // If trying to convert to the same currency, return the amount unchanged
        if ($fromCurrency->id === $toCurrency->id) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $amount,
                    'rate' => 1,
                    'last_updated' => now()
                ]
            ]);
        }

        // Try to find direct rate
        $rate = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->active()
            ->first();

        if ($rate) {
            $convertedAmount = $amount * $rate->rate;
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $convertedAmount,
                    'rate' => $rate->rate,
                    'last_updated' => $rate->updated_at
                ]
            ]);
        }

        // Try inverse rate
        $inverseRate = CurrencyExchangeRate::where('from_currency_id', $toCurrency->id)
            ->where('to_currency_id', $fromCurrency->id)
            ->active()
            ->first();

        if ($inverseRate) {
            $effectiveRate = 1 / $inverseRate->rate;
            $convertedAmount = $amount * $effectiveRate;
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $convertedAmount,
                    'rate' => $effectiveRate,
                    'last_updated' => $inverseRate->updated_at
                ]
            ]);
        }

        // If no direct or inverse rate, try to convert via base currency (usually IQD)
        $baseCurrency = Currency::baseCurrency()->first();

        if (!$baseCurrency) {
            return response()->json([
                'success' => false,
                'message' => 'No base currency defined for intermediary conversion'
            ], 404);
        }

        // Get rate from source currency to base currency
        $fromBaseRate = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $baseCurrency->id)
            ->active()
            ->first();

        // Get rate from base currency to target currency
        $toBaseRate = CurrencyExchangeRate::where('from_currency_id', $baseCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->active()
            ->first();

        if ($fromBaseRate && $toBaseRate) {
            $effectiveRate = $toBaseRate->rate / $fromBaseRate->rate;
            $convertedAmount = $amount * $effectiveRate;
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $convertedAmount,
                    'rate' => $effectiveRate,
                    'last_updated' => max($fromBaseRate->updated_at, $toBaseRate->updated_at)
                ]
            ]);
        }

        // If we still haven't found a conversion path
        return response()->json([
            'success' => false,
            'message' => 'No conversion rate available between these currencies'
        ], 404);
    }

    /**
     * Get update status of exchange rates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus()
    {
        // Check if rates are stale (older than 1 hour)
        $latestRate = CurrencyExchangeRate::latest('updated_at')->first();
        $isStale = !$latestRate || $latestRate->updated_at->diffInHours(now()) > 1;

        return response()->json([
            'success' => true,
            'data' => [
                'is_stale' => $isStale,
                'last_update' => $latestRate ? $latestRate->updated_at : null,
                'total_rates' => CurrencyExchangeRate::count(),
                'active_rates' => CurrencyExchangeRate::active()->count(),
            ]
        ]);
    }
}
