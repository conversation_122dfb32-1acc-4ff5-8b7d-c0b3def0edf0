<?php

namespace App\Http\Controllers;

use App\Models\ExchangeRateHistory;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;

class ExchangeRateHistoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ExchangeRateHistory::with(['fromCurrency', 'toCurrency', 'createdBy'])
            ->latest();

        // Filter by currency pair
        if ($request->from_currency_id) {
            $query->where('from_currency_id', $request->from_currency_id);
        }

        if ($request->to_currency_id) {
            $query->where('to_currency_id', $request->to_currency_id);
        }

        // Filter by date range
        if ($request->start_date) {
            $query->where('effective_date', '>=', $request->start_date);
        }

        if ($request->end_date) {
            $query->where('effective_date', '<=', $request->end_date);
        }

        // Filter by source
        if ($request->source) {
            $query->where('source', $request->source);
        }

        $history = $query->paginate(20);
        $currencies = Currency::active()->ordered()->get();

        return Inertia::render('Admin/ExchangeRateHistory/Index', [
            'history' => $history,
            'currencies' => $currencies,
            'filters' => $request->only(['from_currency_id', 'to_currency_id', 'start_date', 'end_date', 'source']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $currencies = Currency::active()->ordered()->get();

        return Inertia::render('Admin/ExchangeRateHistory/Create', [
            'currencies' => $currencies,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_currency_id' => 'required|exists:currencies,id',
            'to_currency_id' => 'required|exists:currencies,id|different:from_currency_id',
            'rate' => 'required|numeric|min:0.00000001',
            'source' => 'required|in:manual,api,automatic',
            'notes' => 'nullable|string|max:1000',
            'effective_date' => 'required|date',
        ]);

        // Get previous rate for comparison
        $previousRate = ExchangeRateHistory::forCurrencyPair(
            $request->from_currency_id,
            $request->to_currency_id
        )->latest()->first();

        $history = ExchangeRateHistory::create([
            'from_currency_id' => $request->from_currency_id,
            'to_currency_id' => $request->to_currency_id,
            'rate' => $request->rate,
            'previous_rate' => $previousRate?->rate,
            'source' => $request->source,
            'notes' => $request->notes,
            'effective_date' => $request->effective_date,
            'created_by' => Auth::id(),
        ]);

        return redirect()->route('admin.exchange-rate-history.index')
            ->with('success', 'Exchange rate history created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ExchangeRateHistory $exchangeRateHistory)
    {
        $exchangeRateHistory->load(['fromCurrency', 'toCurrency', 'createdBy']);

        // Get related history for the same currency pair
        $relatedHistory = ExchangeRateHistory::forCurrencyPair(
            $exchangeRateHistory->from_currency_id,
            $exchangeRateHistory->to_currency_id
        )->where('id', '!=', $exchangeRateHistory->id)
            ->latest()
            ->limit(10)
            ->get();

        return Inertia::render('Admin/ExchangeRateHistory/Show', [
            'history' => $exchangeRateHistory,
            'relatedHistory' => $relatedHistory,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ExchangeRateHistory $exchangeRateHistory)
    {
        $currencies = Currency::active()->ordered()->get();

        return Inertia::render('Admin/ExchangeRateHistory/Edit', [
            'history' => $exchangeRateHistory,
            'currencies' => $currencies,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ExchangeRateHistory $exchangeRateHistory)
    {
        $request->validate([
            'rate' => 'required|numeric|min:0.00000001',
            'source' => 'required|in:manual,api,automatic',
            'notes' => 'nullable|string|max:1000',
            'effective_date' => 'required|date',
        ]);

        $exchangeRateHistory->update([
            'rate' => $request->rate,
            'source' => $request->source,
            'notes' => $request->notes,
            'effective_date' => $request->effective_date,
        ]);

        return redirect()->route('admin.exchange-rate-history.index')
            ->with('success', 'Exchange rate history updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ExchangeRateHistory $exchangeRateHistory)
    {
        $exchangeRateHistory->delete();

        return redirect()->route('admin.exchange-rate-history.index')
            ->with('success', 'Exchange rate history deleted successfully.');
    }

    /**
     * Get chart data for a currency pair
     */
    public function chartData(Request $request)
    {
        $request->validate([
            'from_currency_id' => 'required|exists:currencies,id',
            'to_currency_id' => 'required|exists:currencies,id',
            'period' => 'required|in:7,30,90,365',
        ]);

        $startDate = Carbon::now()->subDays($request->period);

        $history = ExchangeRateHistory::forCurrencyPair(
            $request->from_currency_id,
            $request->to_currency_id
        )->inDateRange($startDate, Carbon::now())
            ->orderBy('effective_date')
            ->get();

        $chartData = $history->map(function ($item) {
            return [
                'date' => $item->effective_date->format('Y-m-d H:i'),
                'rate' => (float) $item->rate,
                'change_percentage' => (float) $item->change_percentage,
                'change_type' => $item->change_type,
            ];
        });

        return response()->json([
            'data' => $chartData,
            'currency_pair' => [
                'from' => Currency::find($request->from_currency_id),
                'to' => Currency::find($request->to_currency_id),
            ],
        ]);
    }

    /**
     * Get analytics data for dashboard
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', 30);
        $startDate = Carbon::now()->subDays($period);

        // Most volatile currency pairs
        $volatilePairs = ExchangeRateHistory::with(['fromCurrency', 'toCurrency'])
            ->inDateRange($startDate, Carbon::now())
            ->selectRaw('from_currency_id, to_currency_id,
                                                       AVG(ABS(change_percentage)) as avg_volatility,
                                                       COUNT(*) as changes_count')
            ->groupBy('from_currency_id', 'to_currency_id')
            ->orderBy('avg_volatility', 'desc')
            ->limit(10)
            ->get();

        // Recent significant changes
        $significantChanges = ExchangeRateHistory::with(['fromCurrency', 'toCurrency'])
            ->where('effective_date', '>=', $startDate)
            ->whereRaw('ABS(change_percentage) > 5')
            ->latest()
            ->limit(10)
            ->get();

        // Rate update frequency by source
        $updatesBySource = ExchangeRateHistory::where('effective_date', '>=', $startDate)
            ->selectRaw('source, COUNT(*) as count')
            ->groupBy('source')
            ->get();

        return response()->json([
            'volatile_pairs' => $volatilePairs,
            'significant_changes' => $significantChanges,
            'updates_by_source' => $updatesBySource,
            'period' => $period,
        ]);
    }
}
