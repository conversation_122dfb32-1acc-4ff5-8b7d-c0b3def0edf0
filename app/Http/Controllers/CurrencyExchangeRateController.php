<?php

namespace App\Http\Controllers;

use App\Models\CurrencyExchangeRate;
use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CurrencyExchangeRateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CurrencyExchangeRate::with(['fromCurrency', 'toCurrency']);

        // Filter by currency pair
        if ($request->from_currency_id) {
            $query->where('from_currency_id', $request->from_currency_id);
        }

        if ($request->to_currency_id) {
            $query->where('to_currency_id', $request->to_currency_id);
        }

        // Filter by status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $rates = $query->latest()->paginate(20);
        $currencies = Currency::active()->ordered()->get();

        return Inertia::render('Admin/ExchangeRates/Index', [
            'rates' => $rates,
            'currencies' => $currencies,
            'filters' => $request->only(['from_currency_id', 'to_currency_id', 'is_active']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $currencies = Currency::active()->ordered()->get();

        return Inertia::render('Admin/ExchangeRates/Create', [
            'currencies' => $currencies,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_currency_id' => 'required|exists:currencies,id',
            'to_currency_id' => 'required|exists:currencies,id|different:from_currency_id',
            'rate' => 'required|numeric|min:0.00000001',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Check if rate already exists for this pair
        $existingRate = CurrencyExchangeRate::forCurrencyPair(
            $request->from_currency_id,
            $request->to_currency_id
        )->first();

        if ($existingRate) {
            return redirect()->back()
                ->withErrors(['rate' => 'Exchange rate already exists for this currency pair.'])
                ->withInput();
        }

        CurrencyExchangeRate::createOrUpdateRate(
            $request->from_currency_id,
            $request->to_currency_id,
            $request->rate,
            'manual',
            $request->notes,
            Auth::id()
        );

        return redirect()->route('admin.currency-exchange-rates.index')
            ->with('success', 'Exchange rate created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(CurrencyExchangeRate $currencyExchangeRate)
    {
        $currencyExchangeRate->load(['fromCurrency', 'toCurrency']);

        // Get rate history
        $history = $currencyExchangeRate->getLatestHistory(90);

        return Inertia::render('Admin/ExchangeRates/Show', [
            'rate' => $currencyExchangeRate,
            'history' => $history,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CurrencyExchangeRate $currencyExchangeRate)
    {
        $currencies = Currency::active()->ordered()->get();

        return Inertia::render('Admin/ExchangeRates/Edit', [
            'rate' => $currencyExchangeRate,
            'currencies' => $currencies,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CurrencyExchangeRate $currencyExchangeRate)
    {
        $request->validate([
            'rate' => 'required|numeric|min:0.00000001',
            'is_active' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Update rate with history tracking
        $currencyExchangeRate->updateRate(
            $request->rate,
            'manual',
            $request->notes,
            Auth::id()
        );

        // Update status if changed
        if ($request->has('is_active')) {
            $currencyExchangeRate->update(['is_active' => $request->is_active]);
        }

        return redirect()->route('admin.currency-exchange-rates.index')
            ->with('success', 'Exchange rate updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CurrencyExchangeRate $currencyExchangeRate)
    {
        $currencyExchangeRate->delete();

        return redirect()->route('admin.currency-exchange-rates.index')
            ->with('success', 'Exchange rate deleted successfully.');
    }

    /**
     * Toggle rate status
     */
    public function toggleStatus(CurrencyExchangeRate $currencyExchangeRate)
    {
        $currencyExchangeRate->update(['is_active' => !$currencyExchangeRate->is_active]);

        return redirect()->back()
            ->with('success', 'Exchange rate status updated successfully.');
    }

    /**
     * Bulk update rates
     */
    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'rates' => 'required|array',
            'rates.*.id' => 'required|exists:currency_exchange_rates,id',
            'rates.*.rate' => 'required|numeric|min:0.00000001',
            'notes' => 'nullable|string|max:1000',
        ]);

        foreach ($request->rates as $rateData) {
            $exchangeRate = CurrencyExchangeRate::find($rateData['id']);
            $exchangeRate->updateRate(
                $rateData['rate'],
                'manual',
                $request->notes,
                Auth::id()
            );
        }

        return redirect()->route('admin.currency-exchange-rates.index')
            ->with('success', 'Exchange rates updated successfully.');
    }

    /**
     * Get current rate for conversion
     */
    public function getRate(Request $request)
    {
        $request->validate([
            'from_currency_id' => 'required|exists:currencies,id',
            'to_currency_id' => 'required|exists:currencies,id',
        ]);

        $rate = CurrencyExchangeRate::getActiveRateForPair(
            $request->from_currency_id,
            $request->to_currency_id
        );

        if (!$rate) {
            return response()->json(['error' => 'Exchange rate not found'], 404);
        }

        return response()->json([
            'rate' => $rate->rate,
            'from_currency' => $rate->fromCurrency,
            'to_currency' => $rate->toCurrency,
            'last_updated' => $rate->updated_at,
        ]);
    }
}
