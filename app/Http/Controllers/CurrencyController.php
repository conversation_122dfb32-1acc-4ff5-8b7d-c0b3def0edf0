<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CurrencyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Currency::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('code', 'like', '%' . $request->search . '%')
                    ->orWhere('country', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $currencies = $query->ordered()->paginate(20);

        return Inertia::render('Admin/Currencies/Index', [
            'currencies' => $currencies,
            'filters' => $request->only(['search', 'is_active']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Currencies/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:3|unique:currencies,code',
            'symbol' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_base_currency' => 'boolean',
            'decimal_places' => 'required|integer|min:0|max:8',
            'flag_icon' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'required|numeric|gt:min_amount',
        ]);

        // Ensure only one base currency
        if ($request->is_base_currency) {
            Currency::where('is_base_currency', true)->update(['is_base_currency' => false]);
        }

        Currency::create($request->all());

        return redirect()->route('admin.currencies.index')
            ->with('success', 'Currency created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Currency $currency)
    {
        $currency->load(['exchangeRatesFrom.toCurrency', 'exchangeRatesTo.fromCurrency']);

        // Get recent history
        $recentHistory = $currency->historyFrom()
            ->with(['toCurrency'])
            ->latest()
            ->limit(10)
            ->get();

        return Inertia::render('Admin/Currencies/Show', [
            'currency' => $currency,
            'recentHistory' => $recentHistory,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Currency $currency)
    {
        return Inertia::render('Admin/Currencies/Edit', [
            'currency' => $currency,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Currency $currency)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:3|unique:currencies,code,' . $currency->id,
            'symbol' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:255',
            'order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_base_currency' => 'boolean',
            'decimal_places' => 'required|integer|min:0|max:8',
            'flag_icon' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'required|numeric|gt:min_amount',
        ]);

        // Ensure only one base currency
        if ($request->is_base_currency && !$currency->is_base_currency) {
            Currency::where('is_base_currency', true)->update(['is_base_currency' => false]);
        }

        $currency->update($request->all());

        return redirect()->route('admin.currencies.index')
            ->with('success', 'Currency updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Currency $currency)
    {
        // Check if currency has exchange rates
        if ($currency->exchangeRatesFrom()->exists() || $currency->exchangeRatesTo()->exists()) {
            return redirect()->route('admin.currencies.index')
                ->with('error', 'Cannot delete currency with existing exchange rates.');
        }

        $currency->delete();

        return redirect()->route('admin.currencies.index')
            ->with('success', 'Currency deleted successfully.');
    }

    /**
     * Toggle currency status
     */
    public function toggleStatus(Currency $currency)
    {
        $currency->update(['is_active' => !$currency->is_active]);

        return redirect()->back()
            ->with('success', 'Currency status updated successfully.');
    }
}
