<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Currency extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'code',
        'symbol',
        'country',
        'order',
        'is_active',
        'is_base_currency',
        'decimal_places',
        'flag_icon',
        'description',
        'min_amount',
        'max_amount',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_base_currency' => 'boolean',
        'decimal_places' => 'integer',
        'order' => 'integer',
        'min_amount' => 'decimal:8',
        'max_amount' => 'decimal:8',
    ];

    public $translatable = ['name', 'description'];

    // Relationships
    public function exchangeRatesFrom(): HasMany
    {
        return $this->hasMany(CurrencyExchangeRate::class, 'from_currency_id');
    }

    public function exchangeRatesTo(): HasMany
    {
        return $this->hasMany(CurrencyExchangeRate::class, 'to_currency_id');
    }

    public function historyFrom(): HasMany
    {
        return $this->hasMany(ExchangeRateHistory::class, 'from_currency_id');
    }

    public function historyTo(): HasMany
    {
        return $this->hasMany(ExchangeRateHistory::class, 'to_currency_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeBaseCurrency($query)
    {
        return $query->where('is_base_currency', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('name');
    }

    // Helper methods
    public function getFormattedSymbolAttribute()
    {
        return $this->symbol ?: $this->code;
    }

    public function formatAmount($amount)
    {
        return number_format($amount, $this->decimal_places);
    }

    public function getDisplayNameAttribute()
    {
        return $this->name . ' (' . $this->code . ')';
    }

    public function getCurrentRateTo($toCurrencyId)
    {
        return $this->exchangeRatesFrom()
            ->where('to_currency_id', $toCurrencyId)
            ->active()
            ->first();
    }

    public function getCurrentRateFrom($fromCurrencyId)
    {
        return $this->exchangeRatesTo()
            ->where('from_currency_id', $fromCurrencyId)
            ->active()
            ->first();
    }

    public static function getBaseCurrency()
    {
        return static::baseCurrency()->first();
    }

    public static function getActiveCurrencies()
    {
        return static::active()->ordered()->get();
    }

    /**
     * Get the country code for flag display
     */
    public function getFlagCountryCodeAttribute()
    {
        // Map currency codes to country codes for flags
        $currencyToCountryMap = [
            'USD' => 'us',
            'EUR' => 'eu',
            'GBP' => 'gb',
            'JPY' => 'jp',
            'CAD' => 'ca',
            'AUD' => 'au',
            'CHF' => 'ch',
            'CNY' => 'cn',
            'INR' => 'in',
            'TRY' => 'tr',
            'IRR' => 'ir',
            'IQD' => 'iq',
            'SAR' => 'sa',
            'AED' => 'ae',
        ];

        // Return the mapped country code or use the flag_icon field if available
        return $this->flag_icon ?: ($currencyToCountryMap[strtoupper($this->code)] ?? strtolower(substr($this->code, 0, 2)));
    }

    /**
     * Get flag information for API responses
     */
    public function getFlagInfoAttribute()
    {
        return [
            'country_code' => $this->flag_country_code,
            'flag_icon' => $this->flag_icon,
            'country' => $this->country,
        ];
    }
}
