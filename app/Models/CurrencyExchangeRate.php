<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class CurrencyExchangeRate extends Model
{
    protected $fillable = [
        'from_currency_id',
        'to_currency_id',
        'rate',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'rate' => 'decimal:8',
    ];

    // Relationships
    public function fromCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'from_currency_id');
    }

    public function toCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'to_currency_id');
    }

    public function history(): HasMany
    {
        return $this->hasMany(ExchangeRateHistory::class, 'from_currency_id', 'from_currency_id')
            ->where('to_currency_id', $this->to_currency_id);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeForCurrencyPair($query, $fromCurrencyId, $toCurrencyId)
    {
        return $query->where('from_currency_id', $fromCurrencyId)
            ->where('to_currency_id', $toCurrencyId);
    }

    // Helper methods
    public function updateRate($newRate, $source = 'manual', $notes = null, $userId = null)
    {
        $previousRate = $this->rate;

        // Create history record
        ExchangeRateHistory::create([
            'from_currency_id' => $this->from_currency_id,
            'to_currency_id' => $this->to_currency_id,
            'rate' => $newRate,
            'previous_rate' => $previousRate,
            'change_percentage' => $this->calculateChangePercentage($previousRate, $newRate),
            'change_type' => $this->getChangeType($previousRate, $newRate),
            'source' => $source,
            'notes' => $notes,
            'effective_date' => Carbon::now(),
            'created_by' => $userId,
        ]);

        // Update current rate
        $this->update(['rate' => $newRate]);

        return $this;
    }

    public function calculateChangePercentage($oldRate, $newRate)
    {
        if ($oldRate && $oldRate > 0) {
            return (($newRate - $oldRate) / $oldRate) * 100;
        }
        return 0;
    }

    public function getChangeType($oldRate, $newRate)
    {
        if ($oldRate) {
            if ($newRate > $oldRate) {
                return 'increase';
            } elseif ($newRate < $oldRate) {
                return 'decrease';
            }
        }
        return 'no_change';
    }

    public function getFormattedRateAttribute()
    {
        return number_format($this->rate, $this->toCurrency->decimal_places ?? 4);
    }

    public function getLatestHistory($days = 30)
    {
        return ExchangeRateHistory::forCurrencyPair($this->from_currency_id, $this->to_currency_id)
            ->inDateRange(Carbon::now()->subDays($days), Carbon::now())
            ->latest()
            ->get();
    }

    public function convert($amount)
    {
        return $amount * $this->rate;
    }

    public function reverseConvert($amount)
    {
        return $this->rate > 0 ? $amount / $this->rate : 0;
    }

    // Static methods
    public static function getActiveRateForPair($fromCurrencyId, $toCurrencyId)
    {
        // Try direct conversion first
        $directRate = static::active()
            ->forCurrencyPair($fromCurrencyId, $toCurrencyId)
            ->first();

        if ($directRate) {
            return $directRate;
        }

        // Try reverse conversion
        $reverseRate = static::active()
            ->forCurrencyPair($toCurrencyId, $fromCurrencyId)
            ->first();

        if ($reverseRate) {
            // Return a virtual rate object with inverted rate
            $directRate = new static([
                'from_currency_id' => $fromCurrencyId,
                'to_currency_id' => $toCurrencyId,
                'rate' => 1 / $reverseRate->rate,
                'is_active' => true,
            ]);

            // Set timestamps to match the reverse rate
            $directRate->created_at = $reverseRate->created_at;
            $directRate->updated_at = $reverseRate->updated_at;

            return $directRate;
        }

        // No direct or reverse rate found
        return null;
    }

    public static function createOrUpdateRate($fromCurrencyId, $toCurrencyId, $rate, $source = 'manual', $notes = null, $userId = null)
    {
        $exchangeRate = static::forCurrencyPair($fromCurrencyId, $toCurrencyId)->first();

        if ($exchangeRate) {
            $exchangeRate->updateRate($rate, $source, $notes, $userId);
        } else {
            $exchangeRate = static::create([
                'from_currency_id' => $fromCurrencyId,
                'to_currency_id' => $toCurrencyId,
                'rate' => $rate,
                'is_active' => true,
            ]);

            // Create initial history record
            ExchangeRateHistory::create([
                'from_currency_id' => $fromCurrencyId,
                'to_currency_id' => $toCurrencyId,
                'rate' => $rate,
                'previous_rate' => null,
                'change_percentage' => 0,
                'change_type' => 'no_change',
                'source' => $source,
                'notes' => $notes,
                'effective_date' => Carbon::now(),
                'created_by' => $userId,
            ]);
        }

        return $exchangeRate;
    }
}
