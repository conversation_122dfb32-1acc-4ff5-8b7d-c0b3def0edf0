<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ExchangeRateHistory extends Model
{
    protected $table = 'exchange_rate_history';

    protected $fillable = [
        'from_currency_id',
        'to_currency_id',
        'rate',
        'previous_rate',
        'change_percentage',
        'change_type',
        'source',
        'notes',
        'effective_date',
        'created_by',
    ];

    protected $casts = [
        'rate' => 'decimal:8',
        'previous_rate' => 'decimal:8',
        'change_percentage' => 'decimal:4',
        'effective_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $dates = [
        'effective_date',
        'created_at',
        'updated_at',
    ];

    // Relationships
    public function fromCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'from_currency_id');
    }

    public function toCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'to_currency_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeForCurrencyPair($query, $fromCurrencyId, $toCurrencyId)
    {
        return $query->where('from_currency_id', $fromCurrencyId)
            ->where('to_currency_id', $toCurrencyId);
    }

    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('effective_date', [$startDate, $endDate]);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('effective_date', 'desc');
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('source', $source);
    }

    // Helper methods
    public function calculateChangePercentage()
    {
        if ($this->previous_rate && $this->previous_rate > 0) {
            $change = (($this->rate - $this->previous_rate) / $this->previous_rate) * 100;
            return round($change, 4);
        }
        return 0;
    }

    public function getChangeTypeAttribute($value)
    {
        if ($value) {
            return $value;
        }

        if ($this->previous_rate) {
            if ($this->rate > $this->previous_rate) {
                return 'increase';
            } elseif ($this->rate < $this->previous_rate) {
                return 'decrease';
            }
        }
        return 'no_change';
    }

    public function getFormattedRateAttribute()
    {
        return number_format($this->rate, $this->toCurrency->decimal_places ?? 4);
    }

    public function getFormattedChangePercentageAttribute()
    {
        $percentage = $this->change_percentage ?? $this->calculateChangePercentage();
        $sign = $percentage > 0 ? '+' : '';
        return $sign . number_format($percentage, 2) . '%';
    }

    // Static methods
    public static function getLatestRateForPair($fromCurrencyId, $toCurrencyId)
    {
        return static::forCurrencyPair($fromCurrencyId, $toCurrencyId)
            ->latest()
            ->first();
    }

    public static function getRateHistory($fromCurrencyId, $toCurrencyId, $days = 30)
    {
        $startDate = Carbon::now()->subDays($days);
        return static::forCurrencyPair($fromCurrencyId, $toCurrencyId)
            ->inDateRange($startDate, Carbon::now())
            ->latest()
            ->get();
    }
}
