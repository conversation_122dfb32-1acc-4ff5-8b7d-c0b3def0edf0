<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ExchangeRateService;

class UpdateExchangeRates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rates:update
                            {--provider=exchangerate : The API provider to use (exchangerate, fixer, currencyapi)}
                            {--base=USD : The base currency code}
                            {--force : Force update even if recently updated}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update exchange rates from external API providers';

    protected $exchangeRateService;

    public function __construct(ExchangeRateService $exchangeRateService)
    {
        parent::__construct();
        $this->exchangeRateService = $exchangeRateService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $provider = $this->option('provider');
        $baseCurrency = $this->option('base');
        $force = $this->option('force');

        $this->info("Starting exchange rate update...");
        $this->info("Provider: {$provider}");
        $this->info("Base Currency: {$baseCurrency}");

        // Check if update is needed
        if (!$force) {
            $status = $this->exchangeRateService->getRateUpdateStatus();
            if (!$status['is_stale']) {
                $this->info("Rates were recently updated. Use --force to update anyway.");
                $this->info("Last update: " . $status['last_update']->format('Y-m-d H:i:s'));
                return 0;
            }
        }

        // Validate provider
        $availableProviders = $this->exchangeRateService->getAvailableProviders();
        if (!in_array($provider, $availableProviders)) {
            $this->error("Invalid provider. Available providers: " . implode(', ', $availableProviders));
            return 1;
        }

        try {
            $this->info("Fetching rates from {$provider}...");

            $updatedCount = $this->exchangeRateService->updateRatesFromAPI($provider, $baseCurrency);

            if ($updatedCount === false) {
                $this->error("Failed to update exchange rates. Check logs for details.");
                return 1;
            }

            $this->info("✅ Successfully updated {$updatedCount} exchange rates!");

            // Show some updated rates
            $this->showUpdatedRates($baseCurrency);
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    protected function showUpdatedRates($baseCurrency)
    {
        $this->info("\nRecent rate updates:");

        // Get some recent rates to display
        $rates = \App\Models\CurrencyExchangeRate::with(['fromCurrency', 'toCurrency'])
            ->whereHas('fromCurrency', function ($q) use ($baseCurrency) {
                $q->where('code', $baseCurrency);
            })
            ->latest('updated_at')
            ->limit(5)
            ->get();

        $headers = ['From', 'To', 'Rate', 'Updated'];
        $rows = [];

        foreach ($rates as $rate) {
            $rows[] = [
                $rate->fromCurrency->code,
                $rate->toCurrency->code,
                number_format($rate->rate, 4),
                $rate->updated_at->format('H:i:s')
            ];
        }

        $this->table($headers, $rows);
    }
}
