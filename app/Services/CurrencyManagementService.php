<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use App\Models\ExchangeRateHistory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CurrencyManagementService
{
    /**
     * Get all currencies with enhanced data
     */
    public function getAllCurrencies($filters = [])
    {
        $query = Currency::query()
            ->with(['exchangeRatesFrom.toCurrency', 'exchangeRatesTo.fromCurrency']);

        // Apply filters
        if (isset($filters['search']) && !empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name->en', 'like', "%{$search}%")
                  ->orWhere('name->ku', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('country', 'like', "%{$search}%");
            });
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['is_base_currency'])) {
            $query->where('is_base_currency', $filters['is_base_currency']);
        }

        return $query->ordered()->get();
    }

    /**
     * Create a new currency with proper flag mapping
     */
    public function createCurrency(array $data)
    {
        DB::beginTransaction();
        
        try {
            // Ensure only one base currency
            if (isset($data['is_base_currency']) && $data['is_base_currency']) {
                Currency::where('is_base_currency', true)->update(['is_base_currency' => false]);
            }

            // Auto-map flag if not provided
            if (empty($data['flag_icon']) && !empty($data['code'])) {
                $data['flag_icon'] = $this->mapCurrencyToCountryCode($data['code']);
            }

            // Set default order if not provided
            if (empty($data['order'])) {
                $data['order'] = Currency::max('order') + 1;
            }

            $currency = Currency::create($data);

            DB::commit();
            
            // Clear cache
            $this->clearCurrencyCache();
            
            Log::info('Currency created successfully', ['currency_id' => $currency->id, 'code' => $currency->code]);
            
            return $currency;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create currency', ['error' => $e->getMessage(), 'data' => $data]);
            throw $e;
        }
    }

    /**
     * Update currency with validation
     */
    public function updateCurrency(Currency $currency, array $data)
    {
        DB::beginTransaction();
        
        try {
            // Ensure only one base currency
            if (isset($data['is_base_currency']) && $data['is_base_currency'] && !$currency->is_base_currency) {
                Currency::where('is_base_currency', true)->update(['is_base_currency' => false]);
            }

            // Auto-map flag if code changed and flag not explicitly set
            if (isset($data['code']) && $data['code'] !== $currency->code && empty($data['flag_icon'])) {
                $data['flag_icon'] = $this->mapCurrencyToCountryCode($data['code']);
            }

            $currency->update($data);

            DB::commit();
            
            // Clear cache
            $this->clearCurrencyCache();
            
            Log::info('Currency updated successfully', ['currency_id' => $currency->id, 'code' => $currency->code]);
            
            return $currency;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update currency', ['error' => $e->getMessage(), 'currency_id' => $currency->id]);
            throw $e;
        }
    }

    /**
     * Soft delete currency (archive)
     */
    public function archiveCurrency(Currency $currency)
    {
        DB::beginTransaction();
        
        try {
            // Deactivate all exchange rates for this currency
            CurrencyExchangeRate::where('from_currency_id', $currency->id)
                ->orWhere('to_currency_id', $currency->id)
                ->update(['is_active' => false]);

            // Mark currency as inactive
            $currency->update(['is_active' => false]);

            // If this was the base currency, we need to set another one
            if ($currency->is_base_currency) {
                $newBaseCurrency = Currency::where('is_active', true)
                    ->where('id', '!=', $currency->id)
                    ->first();
                
                if ($newBaseCurrency) {
                    $newBaseCurrency->update(['is_base_currency' => true]);
                }
            }

            DB::commit();
            
            // Clear cache
            $this->clearCurrencyCache();
            
            Log::info('Currency archived successfully', ['currency_id' => $currency->id, 'code' => $currency->code]);
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to archive currency', ['error' => $e->getMessage(), 'currency_id' => $currency->id]);
            throw $e;
        }
    }

    /**
     * Get currency statistics
     */
    public function getCurrencyStatistics()
    {
        return Cache::remember('currency_statistics', 300, function () {
            return [
                'total_currencies' => Currency::count(),
                'active_currencies' => Currency::active()->count(),
                'inactive_currencies' => Currency::inactive()->count(),
                'base_currency' => Currency::baseCurrency()->first(),
                'total_exchange_rates' => CurrencyExchangeRate::count(),
                'active_exchange_rates' => CurrencyExchangeRate::active()->count(),
                'last_rate_update' => ExchangeRateHistory::latest()->first()?->created_at,
                'rate_updates_today' => ExchangeRateHistory::whereDate('created_at', today())->count(),
            ];
        });
    }

    /**
     * Map currency code to country code for flags
     */
    private function mapCurrencyToCountryCode(string $currencyCode): string
    {
        $mapping = [
            'USD' => 'us',
            'EUR' => 'eu',
            'GBP' => 'gb',
            'JPY' => 'jp',
            'CAD' => 'ca',
            'AUD' => 'au',
            'CHF' => 'ch',
            'CNY' => 'cn',
            'INR' => 'in',
            'TRY' => 'tr',
            'IRR' => 'ir',
            'IQD' => 'iq',
            'SAR' => 'sa',
            'AED' => 'ae',
            'KWD' => 'kw',
            'BHD' => 'bh',
            'OMR' => 'om',
            'QAR' => 'qa',
            'JOD' => 'jo',
            'LBP' => 'lb',
            'EGP' => 'eg',
            'MAD' => 'ma',
            'DZD' => 'dz',
            'TND' => 'tn',
            'RUB' => 'ru',
            'BRL' => 'br',
            'MXN' => 'mx',
            'KRW' => 'kr',
            'SGD' => 'sg',
            'HKD' => 'hk',
            'NZD' => 'nz',
            'SEK' => 'se',
            'NOK' => 'no',
            'DKK' => 'dk',
            'PLN' => 'pl',
            'CZK' => 'cz',
            'HUF' => 'hu',
            'RON' => 'ro',
            'BGN' => 'bg',
            'HRK' => 'hr',
            'RSD' => 'rs',
            'KURDISTAN' => 'kurdistan', // Special case
        ];

        return $mapping[strtoupper($currencyCode)] ?? strtolower(substr($currencyCode, 0, 2));
    }

    /**
     * Clear currency-related cache
     */
    private function clearCurrencyCache()
    {
        Cache::forget('currency_statistics');
        Cache::forget('active_currencies');
        Cache::forget('base_currency');
        Cache::tags(['currencies'])->flush();
    }

    /**
     * Bulk update currency orders
     */
    public function updateCurrencyOrders(array $orders)
    {
        DB::beginTransaction();
        
        try {
            foreach ($orders as $currencyId => $order) {
                Currency::where('id', $currencyId)->update(['order' => $order]);
            }

            DB::commit();
            
            $this->clearCurrencyCache();
            
            Log::info('Currency orders updated successfully', ['orders' => $orders]);
            
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update currency orders', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * Get currencies for public display (home page)
     */
    public function getPublicCurrencies()
    {
        return Cache::remember('public_currencies', 300, function () {
            return Currency::active()
                ->with(['exchangeRatesFrom' => function ($query) {
                    $query->active()->with('toCurrency');
                }])
                ->ordered()
                ->get()
                ->map(function ($currency) {
                    return [
                        'id' => $currency->id,
                        'name' => $currency->name,
                        'code' => $currency->code,
                        'symbol' => $currency->symbol,
                        'flag_icon' => $currency->flag_icon,
                        'country' => $currency->country,
                        'is_base_currency' => $currency->is_base_currency,
                        'decimal_places' => $currency->decimal_places,
                        'rates' => $currency->exchangeRatesFrom->map(function ($rate) {
                            return [
                                'to_currency' => $rate->toCurrency->code,
                                'to_currency_name' => $rate->toCurrency->name,
                                'rate' => $rate->rate,
                                'updated_at' => $rate->updated_at,
                            ];
                        }),
                    ];
                });
        });
    }
}
