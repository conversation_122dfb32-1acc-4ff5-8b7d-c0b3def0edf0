<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;

class ExchangeRateService
{
    protected $providers = [
        'fixer' => [
            'name' => 'Fixer.io',
            'url' => 'https://api.fixer.io/latest',
            'key_param' => 'access_key',
            'base_param' => 'base',
            'symbols_param' => 'symbols',
            'priority' => 1,
        ],
        'exchangerate' => [
            'name' => 'ExchangeRate-API',
            'url' => 'https://api.exchangerate-api.com/v4/latest',
            'priority' => 2,
        ],
        'currencyapi' => [
            'name' => 'CurrencyAPI',
            'url' => 'https://api.currencyapi.com/v3/latest',
            'key_param' => 'apikey',
            'base_param' => 'base_currency',
            'currencies_param' => 'currencies',
            'priority' => 3,
        ],
        'openexchangerates' => [
            'name' => 'Open Exchange Rates',
            'url' => 'https://openexchangerates.org/api/latest.json',
            'key_param' => 'app_id',
            'base_param' => 'base',
            'symbols_param' => 'symbols',
            'priority' => 4,
        ],
        'currencylayer' => [
            'name' => 'CurrencyLayer',
            'url' => 'http://api.currencylayer.com/live',
            'key_param' => 'access_key',
            'currencies_param' => 'currencies',
            'priority' => 5,
        ],
    ];

    /**
     * Fetch rates from multiple sources and return the best available data
     */
    public function fetchRatesFromMultipleSources($baseCurrency = 'USD', $targetCurrencies = null, $callback = null)
    {
        $results = [];
        $errors = [];
        $successfulProviders = [];

        // Get target currencies if not provided
        if (!$targetCurrencies) {
            $targetCurrencies = Currency::where('code', '!=', $baseCurrency)
                ->where('is_active', true)
                ->pluck('code')
                ->toArray();
        }

        // Sort providers by priority
        $sortedProviders = collect($this->providers)
            ->sortBy('priority')
            ->toArray();

        foreach ($sortedProviders as $providerKey => $providerConfig) {
            try {
                if ($callback) {
                    $callback([
                        'status' => 'fetching',
                        'provider' => $providerConfig['name'],
                        'message' => "Fetching rates from {$providerConfig['name']}..."
                    ]);
                }

                $rates = $this->fetchRatesFromProvider($providerKey, $baseCurrency, $targetCurrencies);

                if ($rates && count($rates) > 0) {
                    $results[$providerKey] = [
                        'provider' => $providerConfig['name'],
                        'rates' => $rates,
                        'timestamp' => now(),
                        'success' => true,
                        'rate_count' => count($rates)
                    ];
                    $successfulProviders[] = $providerKey;

                    if ($callback) {
                        $callback([
                            'status' => 'success',
                            'provider' => $providerConfig['name'],
                            'message' => "Successfully fetched " . count($rates) . " rates from {$providerConfig['name']}",
                            'rate_count' => count($rates)
                        ]);
                    }
                } else {
                    $errors[$providerKey] = "No rates returned from {$providerConfig['name']}";

                    if ($callback) {
                        $callback([
                            'status' => 'error',
                            'provider' => $providerConfig['name'],
                            'message' => "No rates returned from {$providerConfig['name']}"
                        ]);
                    }
                }
            } catch (\Exception $e) {
                $errors[$providerKey] = $e->getMessage();
                Log::error("Error fetching from {$providerKey}: " . $e->getMessage());

                if ($callback) {
                    $callback([
                        'status' => 'error',
                        'provider' => $providerConfig['name'],
                        'message' => "Error: " . $e->getMessage()
                    ]);
                }
            }
        }

        // Combine results from multiple sources
        $combinedRates = $this->combineRatesFromSources($results, $targetCurrencies);

        return [
            'combined_rates' => $combinedRates,
            'source_results' => $results,
            'errors' => $errors,
            'successful_providers' => $successfulProviders,
            'total_providers_tried' => count($sortedProviders),
            'successful_providers_count' => count($successfulProviders)
        ];
    }

    /**
     * Combine rates from multiple sources using weighted average
     */
    protected function combineRatesFromSources($results, $targetCurrencies)
    {
        $combinedRates = [];

        foreach ($targetCurrencies as $currency) {
            $rates = [];
            $weights = [];

            foreach ($results as $providerKey => $result) {
                if (isset($result['rates'][$currency])) {
                    $rates[] = $result['rates'][$currency];
                    // Higher priority providers get higher weight
                    $weights[] = 1 / $this->providers[$providerKey]['priority'];
                }
            }

            if (!empty($rates)) {
                // Calculate weighted average
                $weightedSum = 0;
                $totalWeight = 0;

                for ($i = 0; $i < count($rates); $i++) {
                    $weightedSum += $rates[$i] * $weights[$i];
                    $totalWeight += $weights[$i];
                }

                $combinedRates[$currency] = $totalWeight > 0 ? $weightedSum / $totalWeight : $rates[0];
            }
        }

        return $combinedRates;
    }

    /**
     * Update rates from multiple sources
     */
    public function updateRatesFromMultipleSources($baseCurrency = 'USD', $callback = null)
    {
        try {
            if ($callback) {
                $callback([
                    'status' => 'starting',
                    'message' => 'Starting rate update from multiple sources...'
                ]);
            }

            $result = $this->fetchRatesFromMultipleSources($baseCurrency, null, $callback);

            if (empty($result['combined_rates'])) {
                if ($callback) {
                    $callback([
                        'status' => 'error',
                        'message' => 'No rates could be fetched from any source'
                    ]);
                }
                return false;
            }

            if ($callback) {
                $callback([
                    'status' => 'updating',
                    'message' => 'Updating database with combined rates...'
                ]);
            }

            $updatedCount = $this->updateDatabaseRates(
                $result['combined_rates'],
                $baseCurrency,
                'multi-source',
                $result
            );

            // Cache the results
            Cache::put('last_rate_update', now(), 3600);
            Cache::put('last_rate_sources', $result['successful_providers'], 3600);

            if ($callback) {
                $callback([
                    'status' => 'completed',
                    'message' => "Successfully updated {$updatedCount} rates from {$result['successful_providers_count']} sources",
                    'updated_count' => $updatedCount,
                    'sources_used' => $result['successful_providers_count']
                ]);
            }

            Log::info("Updated {$updatedCount} exchange rates from multiple sources", [
                'successful_providers' => $result['successful_providers'],
                'errors' => $result['errors']
            ]);

            return $updatedCount;
        } catch (\Exception $e) {
            Log::error("Error updating rates from multiple sources: " . $e->getMessage());

            if ($callback) {
                $callback([
                    'status' => 'error',
                    'message' => 'Error: ' . $e->getMessage()
                ]);
            }

            return false;
        }
    }

    public function updateRatesFromAPI($provider = 'exchangerate', $baseCurrency = 'USD')
    {
        try {
            $rates = $this->fetchRatesFromProvider($provider, $baseCurrency);

            if (!$rates) {
                Log::error("Failed to fetch rates from provider: {$provider}");
                return false;
            }

            $updatedCount = $this->updateDatabaseRates($rates, $baseCurrency, $provider);

            Log::info("Updated {$updatedCount} exchange rates from {$provider}");

            // Cache the last update time
            Cache::put('last_rate_update', now(), 3600);

            return $updatedCount;
        } catch (\Exception $e) {
            Log::error("Error updating rates from API: " . $e->getMessage());
            return false;
        }
    }

    protected function fetchRatesFromProvider($provider, $baseCurrency, $targetCurrencies = null)
    {
        $config = $this->providers[$provider] ?? null;

        if (!$config) {
            throw new \InvalidArgumentException("Unknown provider: {$provider}");
        }

        $url = $config['url'];
        $params = [];

        // Add API key if required
        if (isset($config['key_param'])) {
            $apiKey = config("services.{$provider}.api_key");
            if (!$apiKey) {
                throw new \Exception("API key not configured for {$provider}");
            }
            $params[$config['key_param']] = $apiKey;
        }

        // Add base currency to URL for exchangerate-api
        if ($provider === 'exchangerate') {
            $url = $url . '/' . $baseCurrency;
        } elseif (isset($config['base_param'])) {
            $params[$config['base_param']] = $baseCurrency;
        }

        // Get target currencies if not provided
        if (!$targetCurrencies) {
        $targetCurrencies = Currency::where('code', '!=', $baseCurrency)
            ->where('is_active', true)
            ->pluck('code')
            ->toArray();
        }

        // Add symbols/currencies parameter if supported
        if (isset($config['symbols_param'])) {
            $params[$config['symbols_param']] = implode(',', $targetCurrencies);
        } elseif (isset($config['currencies_param'])) {
            $params[$config['currencies_param']] = implode(',', $targetCurrencies);
        }

        $response = Http::timeout(30)->get($url, $params);

        if (!$response->successful()) {
            throw new \Exception("API request failed: " . $response->status() . " - " . $response->body());
        }

        $data = $response->json();

        // Parse response based on provider
        return $this->parseProviderResponse($data, $provider);
    }

    protected function parseProviderResponse($data, $provider)
    {
        switch ($provider) {
            case 'fixer':
                return $data['rates'] ?? null;

            case 'exchangerate':
                return $data['rates'] ?? null;

            case 'currencyapi':
                $rates = [];
                if (isset($data['data'])) {
                    foreach ($data['data'] as $currency => $info) {
                        $rates[$currency] = $info['value'];
                    }
                }
                return $rates;

            case 'openexchangerates':
                return $data['rates'] ?? null;

            case 'currencylayer':
                $rates = [];
                if (isset($data['quotes'])) {
                    foreach ($data['quotes'] as $pair => $rate) {
                        // Remove base currency prefix (e.g., USDEUR -> EUR)
                        $currency = substr($pair, 3);
                        $rates[$currency] = $rate;
                    }
                }
                return $rates;

            default:
                return null;
        }
    }

    protected function updateDatabaseRates($rates, $baseCurrency, $source, $sourceDetails = null)
    {
        $baseCurrencyModel = Currency::where('code', $baseCurrency)->first();

        if (!$baseCurrencyModel) {
            throw new \Exception("Base currency {$baseCurrency} not found");
        }

        $updatedCount = 0;
        $notes = "Updated from {$source}";

        if ($sourceDetails && isset($sourceDetails['successful_providers'])) {
            $notes .= " (Sources: " . implode(', ', $sourceDetails['successful_providers']) . ")";
        }

        foreach ($rates as $currencyCode => $rate) {
            $targetCurrency = Currency::where('code', $currencyCode)->first();

            if (!$targetCurrency || !$targetCurrency->is_active) {
                continue;
            }

            // Update or create exchange rate
            CurrencyExchangeRate::createOrUpdateRate(
                $baseCurrencyModel->id,
                $targetCurrency->id,
                $rate,
                'api',
                $notes,
                null
            );

            $updatedCount++;
        }

        return $updatedCount;
    }

    public function getLastUpdateTime()
    {
        return Cache::get('last_rate_update');
    }

    public function getRateUpdateStatus()
    {
        $lastUpdate = $this->getLastUpdateTime();
        $lastSources = Cache::get('last_rate_sources', []);
        $isStale = !$lastUpdate || $lastUpdate->diffInHours(now()) > 1;

        return [
            'last_update' => $lastUpdate,
            'last_sources' => $lastSources,
            'is_stale' => $isStale,
            'next_update' => $lastUpdate ? $lastUpdate->addHour() : now(),
        ];
    }

    public function getAvailableProviders()
    {
        return collect($this->providers)->map(function ($config, $key) {
            return [
                'key' => $key,
                'name' => $config['name'],
                'priority' => $config['priority']
            ];
        })->sortBy('priority')->values()->toArray();
    }

    /**
     * Test connection to all providers
     */
    public function testProviderConnections()
    {
        $results = [];

        foreach ($this->providers as $providerKey => $config) {
            try {
                $startTime = microtime(true);
                $this->fetchRatesFromProvider($providerKey, 'USD', ['EUR']);
                $endTime = microtime(true);

                $results[$providerKey] = [
                    'name' => $config['name'],
                    'status' => 'success',
                    'response_time' => round(($endTime - $startTime) * 1000, 2) . 'ms',
                    'error' => null
                ];
            } catch (\Exception $e) {
                $results[$providerKey] = [
                    'name' => $config['name'],
                    'status' => 'error',
                    'response_time' => null,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}
