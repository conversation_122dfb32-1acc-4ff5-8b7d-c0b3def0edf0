<template>
  <q-dialog v-model="showModal" persistent>
    <q-card style="min-width: 700px; max-width: 900px;">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          <q-icon name="swap_horiz" class="q-mr-sm" />
          Add New Exchange Rate
        </div>
        <q-space />
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          :disable="submitting"
        />
      </q-card-section>

      <q-card-section>
        <div class="text-body2 text-grey-7 q-mb-md">
          Create a new exchange rate between currencies
        </div>

        <q-form @submit="submitForm" class="q-gutter-md">
          <!-- Currency Pair Selection -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.from_currency_id"
                :options="currencyOptions"
                option-value="id"
                option-label="display_name"
                label="From Currency *"
                outlined
                :error="!!errors.from_currency_id"
                :error-message="errors.from_currency_id"
                hint="Select the source currency"
                @update:model-value="checkExistingRate"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <span class="text-lg">{{ scope.opt.flag_icon }}</span>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.name }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.to_currency_id"
                :options="filteredToCurrencies"
                option-value="id"
                option-label="display_name"
                label="To Currency *"
                outlined
                :error="!!errors.to_currency_id"
                :error-message="errors.to_currency_id"
                hint="Select the target currency"
                @update:model-value="checkExistingRate"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section avatar>
                      <span class="text-lg">{{ scope.opt.flag_icon }}</span>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ scope.opt.name }}</q-item-label>
                      <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>
          </div>

          <!-- Existing Rate Warning -->
          <q-banner
            v-if="existingRateWarning"
            class="bg-warning text-white q-mb-md"
            rounded
          >
            <template v-slot:avatar>
              <q-icon name="warning" />
            </template>
            An exchange rate already exists for this currency pair. Creating this rate will replace the existing one.
          </q-banner>

          <!-- Exchange Rate -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-8">
              <q-input
                v-model.number="form.rate"
                label="Exchange Rate *"
                outlined
                type="number"
                step="0.00000001"
                min="0.00000001"
                :error="!!errors.rate"
                :error-message="errors.rate"
                hint="1 unit of source currency = X units of target currency"
              >
                <template v-slot:prepend>
                  <q-icon name="swap_horiz" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-4">
              <q-btn
                @click="fetchCurrentRate"
                :loading="fetchingRate"
                icon="refresh"
                label="Fetch Rate"
                color="secondary"
                outline
                class="full-width"
                :disable="!canFetchRate"
              />
            </div>
          </div>

          <!-- Rate Preview -->
          <div v-if="ratePreview" class="rate-preview q-pa-md bg-blue-1 rounded-borders">
            <div class="text-subtitle2 q-mb-xs">Rate Preview</div>
            <div class="row items-center">
              <div class="col">
                <div class="text-body1">
                  1 {{ fromCurrency?.code }} = {{ formatRate(form.rate) }} {{ toCurrency?.code }}
                </div>
                <div class="text-caption text-grey-6">
                  {{ formatAmount(100, fromCurrency?.code) }} = {{ formatAmount(100 * form.rate, toCurrency?.code) }}
                </div>
              </div>
              <div class="col-auto">
                <q-icon name="trending_up" size="md" color="positive" />
              </div>
            </div>
          </div>

          <!-- Notes -->
          <q-input
            v-model="form.notes"
            label="Notes"
            outlined
            type="textarea"
            rows="3"
            :error="!!errors.notes"
            :error-message="errors.notes"
            hint="Optional notes about this exchange rate"
          />

          <!-- Status -->
          <div class="q-gutter-sm">
            <q-checkbox
              v-model="form.is_active"
              label="Active Exchange Rate"
              color="positive"
            />
            <div class="text-caption text-grey-6 q-ml-lg">
              Active rates can be used for currency conversions
            </div>
          </div>

          <!-- Rate Calculator -->
          <q-card v-if="form.rate && fromCurrency && toCurrency" class="calculator-card q-mt-lg" flat bordered>
            <q-card-section>
              <div class="text-subtitle2 q-mb-md">Rate Calculator</div>

              <div class="row q-gutter-md">
                <div class="col-12 col-sm-6">
                  <q-input
                    v-model.number="calculatorAmount"
                    label="Amount"
                    outlined
                    type="number"
                    step="0.01"
                    min="0"
                  >
                    <template v-slot:append>
                      <span class="text-caption">{{ fromCurrency.code }}</span>
                    </template>
                  </q-input>
                </div>

                <div class="col-12 col-sm-6">
                  <q-input
                    :model-value="convertedAmount"
                    label="Converted Amount"
                    outlined
                    readonly
                  >
                    <template v-slot:append>
                      <span class="text-caption">{{ toCurrency.code }}</span>
                    </template>
                  </q-input>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-form>
      </q-card-section>

      <q-card-actions align="right" class="q-pt-none">
        <q-btn
          flat
          label="Cancel"
          color="grey"
          v-close-popup
          :disable="submitting"
        />
        <q-btn
          @click="submitForm"
          :loading="submitting"
          label="Create Exchange Rate"
          color="primary"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'

const $q = useQuasar()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  currencies: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'created', 'error'])

// Reactive data
const showModal = ref(props.modelValue)
const submitting = ref(false)
const fetchingRate = ref(false)
const existingRateWarning = ref(false)
const calculatorAmount = ref(1)
const availableCurrencies = ref([])

const form = ref({
  from_currency_id: null,
  to_currency_id: null,
  rate: null,
  notes: '',
  is_active: true
})

const errors = ref({})

// Computed properties
const currencyOptions = computed(() => {
  const currencies = props.currencies.length > 0 ? props.currencies : availableCurrencies.value
  return currencies.map(currency => ({
    ...currency,
    display_name: `${currency.flag_icon} ${currency.name} (${currency.code})`
  }))
})

const filteredToCurrencies = computed(() => {
  return currencyOptions.value.filter(currency =>
    currency.id !== form.value.from_currency_id
  )
})

const fromCurrency = computed(() => {
  const currencies = props.currencies.length > 0 ? props.currencies : availableCurrencies.value
  return currencies.find(c => c.id === form.value.from_currency_id)
})

const toCurrency = computed(() => {
  const currencies = props.currencies.length > 0 ? props.currencies : availableCurrencies.value
  return currencies.find(c => c.id === form.value.to_currency_id)
})

const canFetchRate = computed(() => {
  return form.value.from_currency_id && form.value.to_currency_id
})

const ratePreview = computed(() => {
  return form.value.rate && fromCurrency.value && toCurrency.value
})

const convertedAmount = computed(() => {
  if (calculatorAmount.value && form.value.rate) {
    return (calculatorAmount.value * form.value.rate).toFixed(4)
  }
  return 0
})

// Watch for model value changes
watch(() => props.modelValue, (newVal) => {
  showModal.value = newVal
})

watch(showModal, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// Watch for currency pair changes
watch(() => [form.value.from_currency_id, form.value.to_currency_id], () => {
  if (form.value.from_currency_id === form.value.to_currency_id) {
    form.value.to_currency_id = null
  }
})

// Methods
const loadCurrencies = async () => {
  if (props.currencies.length > 0) return

  try {
    const response = await axios.get('/api/admin/currencies')
    if (response.data.success) {
      availableCurrencies.value = response.data.currencies
    }
  } catch (error) {
    console.error('Error loading currencies:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load currencies'
    })
  }
}

const resetForm = () => {
  form.value = {
    from_currency_id: null,
    to_currency_id: null,
    rate: null,
    notes: '',
    is_active: true
  }
  errors.value = {}
  existingRateWarning.value = false
  calculatorAmount.value = 1
}

const checkExistingRate = async () => {
  if (form.value.from_currency_id && form.value.to_currency_id) {
    try {
      const response = await axios.get('/api/admin/exchange-rates/check-existing', {
        params: {
          from_currency_id: form.value.from_currency_id,
          to_currency_id: form.value.to_currency_id
        }
      })

      if (response.data.exists) {
        existingRateWarning.value = true
      } else {
        existingRateWarning.value = false
      }
    } catch (error) {
      existingRateWarning.value = false
    }
  }
}

const fetchCurrentRate = async () => {
  if (!canFetchRate.value) return

  fetchingRate.value = true
  try {
    const response = await axios.get('/api/exchange/convert', {
      params: {
        from: fromCurrency.value?.code,
        to: toCurrency.value?.code,
        amount: 1
      }
    })

    if (response.data.success) {
      form.value.rate = parseFloat(response.data.data.rate)

      $q.notify({
        type: 'positive',
        message: 'Current rate fetched successfully',
        timeout: 3000
      })
    }
  } catch (error) {
    // Fallback to mock rate if API fails
    let mockRate = 1
    if (fromCurrency.value?.code === 'USD' && toCurrency.value?.code === 'IQD') {
      mockRate = 1310.50
    } else if (fromCurrency.value?.code === 'USD' && toCurrency.value?.code === 'EUR') {
      mockRate = 0.85
    } else if (fromCurrency.value?.code === 'EUR' && toCurrency.value?.code === 'USD') {
      mockRate = 1.18
    } else {
      mockRate = Math.random() * 10 + 0.1
    }

    form.value.rate = parseFloat(mockRate.toFixed(8))

    $q.notify({
      type: 'info',
      message: 'Using estimated rate (API unavailable)',
      timeout: 3000
    })
  } finally {
    fetchingRate.value = false
  }
}

const submitForm = async () => {
  submitting.value = true
  errors.value = {}

  try {
    const response = await axios.post('/api/admin/exchange-rates', form.value)

    if (response.data.success) {
      $q.notify({
        type: 'positive',
        message: 'Exchange rate created successfully',
        timeout: 3000
      })

      emit('created', {
        success: true,
        rate: response.data.rate
      })

      showModal.value = false
    }
  } catch (error) {
    console.error('Error creating exchange rate:', error)

    if (error.response?.data?.errors) {
      errors.value = error.response.data.errors
    }

    emit('error', {
      success: false,
      error: error.response?.data?.message || error.message
    })

    $q.notify({
      type: 'negative',
      message: error.response?.data?.message || 'Failed to create exchange rate',
      timeout: 5000
    })
  } finally {
    submitting.value = false
  }
}

const formatRate = (rate) => {
  return parseFloat(rate).toFixed(4)
}

const formatAmount = (amount, currencyCode) => {
  const currencies = props.currencies.length > 0 ? props.currencies : availableCurrencies.value
  const currency = currencies.find(c => c.code === currencyCode)
  const decimals = currency?.decimal_places || 2
  return parseFloat(amount).toFixed(decimals)
}

// Lifecycle
onMounted(() => {
  loadCurrencies()
})
</script>

<style scoped>
.q-card {
  border-radius: 12px;
}

.rate-preview {
  border: 1px solid #e3f2fd;
  border-radius: 8px;
}

.calculator-card {
  border-radius: 8px;
}
</style>
