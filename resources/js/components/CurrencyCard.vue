<script setup>
import { defineProps } from 'vue';
import CountryFlag from './CountryFlag.vue';

const props = defineProps({
  currency: {
    type: Object,
    required: true
  },
  t: {
    type: Object,
    required: true
  },
  onCalculate: {
    type: Function,
    required: true
  }
});

// Handle flag loading errors
const handleFlagError = (error) => {
  console.warn('Flag loading error for currency:', props.currency.code, error);
};
</script>

<template>
  <q-intersection transition="scale">
    <div
      class="bg-primary rounded-borders q-pa-md cursor-pointer shadow-1 text-white q-hover"
      style="transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);"
      @click="onCalculate(currency)"
    >
      <div class="row items-center q-mb-md">
        <div class="q-mr-md" :class="{'q-ml-md q-mr-none': $q.lang.rtl}">
          <CountryFlag
            :code="currency.currency_code || currency.code || 'USD'"
            :country="currency.name || currency.currency_code"
            :flag-icon="currency.flag_icon"
            :flag-country-code="currency.flag_country_code"
            size="medium"
            :shadow="true"
            :rounded="false"
            class="flag-img"
            @error="handleFlagError"
          />
        </div>
        <div class="text-subtitle1 text-weight-bold">{{ currency.name }}</div>
      </div>
      <div class="column q-gutter-sm">
        <div class="row justify-between items-center">
          <div class="text-caption text-white-9">{{ t.buy }}</div>
          <div class="text-weight-bold text-subtitle1 row items-center" :class="{
            'text-green': currency.buy_status === 'up',
            'text-red': currency.buy_status === 'down'
          }">
            {{ currency.buy.toLocaleString() }}
            <q-icon
              :name="currency.buy_status === 'up' ? 'arrow_upward' : 'arrow_downward'"
              size="xs"
              :class="{'q-ml-xs': !$q.lang.rtl, 'q-mr-xs': $q.lang.rtl, 'animate-bounce': currency.buy_status === 'up', 'animate-drop': currency.buy_status === 'down'}"
            />
          </div>
        </div>
        <div class="row justify-between items-center">
          <div class="text-caption text-white-9">{{ t.sale }}</div>
          <div class="text-weight-bold text-subtitle1 row items-center" :class="{
            'text-green': currency.sale_status === 'up',
            'text-red': currency.sale_status === 'down'
          }">
            {{ currency.sale.toLocaleString() }}
            <q-icon
              :name="currency.sale_status === 'up' ? 'arrow_upward' : 'arrow_downward'"
              size="xs"
              :class="{'q-ml-xs': !$q.lang.rtl, 'q-mr-xs': $q.lang.rtl, 'animate-bounce': currency.sale_status === 'up', 'animate-drop': currency.sale_status === 'down'}"
            />
          </div>
        </div>
      </div>

      <div class="card-shine"></div>
    </div>
  </q-intersection>
</template>

<style scoped>
.q-hover {
  position: relative;
  overflow: hidden;
  transform: perspective(1px) translateZ(0);
}

.q-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.q-hover:active {
  transform: translateY(1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.flag-img {
  transition: all 0.3s ease;
}

.q-hover:hover .flag-img {
  transform: scale(1.2) rotate(3deg);
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-drop {
  animation: drop 2s infinite;
}

.card-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.05) 40%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 200%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.q-hover:hover .card-shine {
  opacity: 1;
  animation: shine 1.5s infinite;
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes drop {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(2px);
  }
}
</style>
