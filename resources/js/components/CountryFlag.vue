<template>
  <div class="country-flag" :class="flagClasses">
    <!-- Special handling for Kurdistan flag -->
    <div v-if="isKurdistanFlag" class="kurdistan-flag" :style="flagStyle">
      <img
        :src="kurdistanFlagUrl"
        :alt="country || 'Kurdistan'"
        :title="country || 'Kurdistan'"
        class="flag-image kurdistan-flag-img"
        @error="handleImageError"
      />
    </div>
    <!-- Use vue-country-flag-next for standard country flags -->
    <CountryFlag
      v-else
      :country="countryCode"
      :size="flagSize"
      :title="country || countryCode"
      class="vue-flag"
      @error="handleFlagError"
    />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import CountryFlag from 'vue-country-flag-next'

const props = defineProps({
  country: {
    type: String,
    default: ''
  },
  code: {
    type: String,
    required: true
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large', 'xl'].includes(value)
  },
  flagIcon: {
    type: String,
    default: null
  },
  flagCountryCode: {
    type: String,
    default: null
  },
  rounded: {
    type: Boolean,
    default: false
  },
  shadow: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['error'])

const imageError = ref(false)

// Enhanced currency to country mapping
const currencyToCountryMap = {
  'usd': 'us',
  'eur': 'eu',
  'gbp': 'gb',
  'jpy': 'jp',
  'cad': 'ca',
  'aud': 'au',
  'chf': 'ch',
  'cny': 'cn',
  'inr': 'in',
  'try': 'tr',
  'irr': 'ir',
  'iqd': 'iq',
  'sar': 'sa',
  'aed': 'ae',
  'kwd': 'kw',
  'bhd': 'bh',
  'omr': 'om',
  'qar': 'qa',
  'jod': 'jo',
  'lbp': 'lb',
  'egp': 'eg',
  'mad': 'ma',
  'dzd': 'dz',
  'tnd': 'tn',
  'rub': 'ru',
  'brl': 'br',
  'mxn': 'mx',
  'krw': 'kr',
  'sgd': 'sg',
  'hkd': 'hk',
  'nzd': 'nz',
  'sek': 'se',
  'nok': 'no',
  'dkk': 'dk',
  'pln': 'pl',
  'czk': 'cz',
  'huf': 'hu',
  'ron': 'ro',
  'bgn': 'bg',
  'hrk': 'hr',
  'rsd': 'rs',
  'kurdistan': 'kurdistan' // Special case
}

// Check if this is Kurdistan flag
const isKurdistanFlag = computed(() => {
  const code = props.code.toLowerCase()
  return code === 'kurdistan' ||
         props.flagIcon === 'kurdistan' ||
         props.flagCountryCode === 'kurdistan' ||
         props.country?.toLowerCase().includes('kurdistan')
})

// Kurdistan flag URL
const kurdistanFlagUrl = computed(() => '/flags/kurdistan.png')

const countryCode = computed(() => {
  if (isKurdistanFlag.value) {
    return 'kurdistan'
  }

  // Use backend provided flag data first
  if (props.flagCountryCode && props.flagCountryCode !== 'kurdistan') {
    return props.flagCountryCode.toLowerCase()
  }

  if (props.flagIcon && props.flagIcon !== 'kurdistan') {
    return props.flagIcon.toLowerCase()
  }

  // Fallback to mapping
  const code = props.code.toLowerCase()
  const mapped = currencyToCountryMap[code]

  if (mapped && mapped !== 'kurdistan') {
    return mapped
  }

  // Final fallback to first 2 chars of currency code
  return code.slice(0, 2)
})

const flagSize = computed(() => {
  switch (props.size) {
    case 'small':
      return 'small'
    case 'large':
      return 'big'
    case 'xl':
      return 'big'
    default:
      return 'normal'
  }
})

// Computed classes for the flag container
const flagClasses = computed(() => ({
  'flag-small': props.size === 'small',
  'flag-medium': props.size === 'medium',
  'flag-large': props.size === 'large',
  'flag-xl': props.size === 'xl',
  'flag-rounded': props.rounded,
  'flag-shadow': props.shadow,
  'flag-error': imageError.value
}))

// Computed styles for Kurdistan flag
const flagStyle = computed(() => {
  const sizes = {
    small: { width: '20px', height: '15px' },
    medium: { width: '32px', height: '24px' },
    large: { width: '48px', height: '36px' },
    xl: { width: '64px', height: '48px' }
  }
  return sizes[props.size] || sizes.medium
})

// Error handlers
const handleImageError = () => {
  imageError.value = true
  emit('error', 'Failed to load Kurdistan flag')
}

const handleFlagError = () => {
  imageError.value = true
  emit('error', 'Failed to load country flag')
}
</script>

<style scoped>
.country-flag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.2s ease;
}

/* Base flag styles */
.vue-flag,
.kurdistan-flag {
  border-radius: 4px;
  transition: transform 0.2s ease;
  overflow: hidden;
}

.flag-shadow .vue-flag,
.flag-shadow .kurdistan-flag {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.flag-rounded .vue-flag,
.flag-rounded .kurdistan-flag {
  border-radius: 50%;
}

.vue-flag:hover,
.kurdistan-flag:hover {
  transform: scale(1.05);
}

/* Kurdistan flag specific styles */
.kurdistan-flag {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flag-image {
  object-fit: cover;
  display: block;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.kurdistan-flag-img {
  width: 100%;
  height: 100%;
}

/* Size variations */
.flag-small .vue-flag,
.flag-small .kurdistan-flag {
  width: 20px;
  height: 15px;
}

.flag-small .vue-flag :deep(img),
.flag-small .flag-image {
  width: 20px !important;
  height: 15px !important;
  border-radius: 3px;
}

.flag-medium .vue-flag,
.flag-medium .kurdistan-flag {
  width: 32px;
  height: 24px;
}

.flag-medium .vue-flag :deep(img),
.flag-medium .flag-image {
  width: 32px !important;
  height: 24px !important;
  border-radius: 4px;
}

.flag-large .vue-flag,
.flag-large .kurdistan-flag {
  width: 48px;
  height: 36px;
}

.flag-large .vue-flag :deep(img),
.flag-large .flag-image {
  width: 48px !important;
  height: 36px !important;
  border-radius: 6px;
}

.flag-xl .vue-flag,
.flag-xl .kurdistan-flag {
  width: 64px;
  height: 48px;
}

.flag-xl .vue-flag :deep(img),
.flag-xl .flag-image {
  width: 64px !important;
  height: 48px !important;
  border-radius: 8px;
}

/* Rounded flag overrides */
.flag-rounded .vue-flag :deep(img),
.flag-rounded .flag-image {
  border-radius: 50% !important;
}

/* Error state */
.flag-error {
  opacity: 0.5;
  filter: grayscale(100%);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .vue-flag :deep(img),
  .flag-image {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .flag-shadow .vue-flag,
  .flag-shadow .kurdistan-flag {
    box-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .vue-flag,
  .kurdistan-flag,
  .country-flag {
    transition: none !important;
  }

  .vue-flag:hover,
  .kurdistan-flag:hover {
    transform: none !important;
  }
}

/* Focus styles for accessibility */
.country-flag:focus-within {
  outline: 2px solid var(--q-primary);
  outline-offset: 2px;
}
</style>
