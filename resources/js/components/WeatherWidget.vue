<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  weatherData: {
    type: Object,
    required: true
  },
  t: {
    type: Object,
    required: true
  },
  refreshWeather: {
    type: Function,
    required: true
  },
  weatherIcons: {
    type: Object,
    required: true
  },
  weatherColors: {
    type: Object,
    required: true
  }
});
</script>

<template>
  <q-intersection once transition="scale">
    <div
      class="rounded-borders q-pa-md shadow-5 border relative-position flex justify-center items-center q-hover"
      :class="[weatherData.loading ? 'bg-blue-1' : weatherData.error ? 'bg-red-1' : 'bg-white-1']"
      style="border: 1px solid rgba(37, 99, 235, 0.15); min-height: 120px; transition: all 0.3s ease;"
    >
      <q-inner-loading :showing="weatherData.loading" color="primary">
        <q-spinner-dots size="3em" color="primary" />
      </q-inner-loading>

      <div v-if="weatherData.error" class="column items-center justify-center q-gutter-y-md full-width text-center">
        <q-icon name="error" color="negative" size="2em" class="q-mb-xs pulse-animation" />
        <div class="text-negative text-weight-medium">{{ weatherData.error }}</div>
        <q-btn flat dense label="Retry" icon-right="refresh" @click="refreshWeather" color="primary" />
      </div>

      <div v-else-if="!weatherData.loading" class="column items-center justify-center q-gutter-y-md full-width">
        <div class="row items-center justify-center q-gutter-md">
          <div class="weather-icon-container">
            <q-icon
              :name="weatherIcons[weatherData.condition]"
              :color="weatherColors[weatherData.condition]"
              size="3em"
              class="weather-icon"
            />
          </div>
          <div class="text-h3 text-weight-bold temperature-display">
            {{ weatherData.temperature }}<span class="text-h5 text-weight-regular" :class="{'q-ml-xs': !$q.lang.rtl, 'q-mr-xs': $q.lang.rtl}">°C</span>
          </div>
        </div>
        <div class="text-subtitle2 text-weight-medium text-center location-text">
          {{ weatherData.location }}
        </div>

        <q-btn flat dense class="absolute-bottom-right q-ma-xs refresh-btn" icon="refresh" size="sm" @click="refreshWeather" :class="{'absolute-bottom-left': $q.lang.rtl}">
          <q-tooltip anchor="center middle" self="center middle" transition-show="scale" transition-hide="scale">
            {{ t.weather.tap_refresh }}
          </q-tooltip>
        </q-btn>
      </div>
    </div>
  </q-intersection>
</template>

<style scoped>
.q-hover {
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.q-hover:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 30px rgba(37, 99, 235, 0.15);
  border-color: rgba(37, 99, 235, 0.25);
}

.weather-icon-container {
  position: relative;
  z-index: 1;
}

.weather-icon {
  transition: all 0.3s ease;
  transform-origin: center;
}

.q-hover:hover .weather-icon {
  transform: scale(1.2);
  animation: pulse 2s infinite;
}

.temperature-display {
  transition: all 0.3s ease;
}

.q-hover:hover .temperature-display {
  transform: translateY(-2px);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.location-text {
  transition: all 0.3s ease;
  opacity: 0.9;
}

.q-hover:hover .location-text {
  opacity: 1;
  transform: scale(1.05);
}

.refresh-btn {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.q-hover:hover .refresh-btn {
  opacity: 1;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Media queries */
@media (max-width: 599px) {
  .text-h3 {
    font-size: 2rem;
  }

  .text-h5 {
    font-size: 1.2rem;
  }
}
</style>
