<template>
    <nav v-if="links.length > 3" class="flex items-center justify-between">
        <div class="flex justify-between flex-1 sm:hidden">
            <Link
                v-if="links[0].url"
                :href="links[0].url"
                class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:text-gray-400 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:text-gray-200"
            >
                Previous
            </Link>
            <Link
                v-if="links[links.length - 1].url"
                :href="links[links.length - 1].url"
                class="relative ml-3 inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:text-gray-400 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:text-gray-200"
            >
                Next
            </Link>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    Showing
                    <span class="font-medium">{{ from }}</span>
                    to
                    <span class="font-medium">{{ to }}</span>
                    of
                    <span class="font-medium">{{ total }}</span>
                    results
                </p>
            </div>
            <div>
                <span class="relative z-0 inline-flex rounded-md shadow-sm">
                    <template v-for="(link, index) in links" :key="index">
                        <Link
                            v-if="link.url"
                            :href="link.url"
                            :class="[
                                'relative inline-flex items-center px-2 py-2 text-sm font-medium border',
                                link.active
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700',
                                index === 0 ? 'rounded-l-md' : '',
                                index === links.length - 1 ? 'rounded-r-md' : '',
                                index > 0 ? '-ml-px' : ''
                            ]"
                        >
                            <span v-html="link.label"></span>
                        </Link>
                        <span
                            v-else
                            :class="[
                                'relative inline-flex items-center px-2 py-2 text-sm font-medium border cursor-default',
                                link.active
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600 dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300'
                                    : 'bg-white border-gray-300 text-gray-500 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300',
                                index === 0 ? 'rounded-l-md' : '',
                                index === links.length - 1 ? 'rounded-r-md' : '',
                                index > 0 ? '-ml-px' : ''
                            ]"
                        >
                            <span v-html="link.label"></span>
                        </span>
                    </template>
                </span>
            </div>
        </div>
    </nav>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import { computed } from 'vue'

const props = defineProps({
    links: Array,
    from: Number,
    to: Number,
    total: Number,
})

// Extract pagination info from links if not provided directly
const from = computed(() => props.from || extractFromLinks())
const to = computed(() => props.to || extractFromLinks())
const total = computed(() => props.total || extractFromLinks())

function extractFromLinks() {
    // Try to extract pagination info from the links structure
    // This is a fallback for when pagination info isn't passed directly
    return 0
}
</script>
