<template>
  <q-dialog v-model="showModal" persistent>
    <q-card style="min-width: 600px; max-width: 800px;">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          <q-icon name="add_circle" class="q-mr-sm" />
          Add New Currency
        </div>
        <q-space />
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          :disable="submitting"
        />
      </q-card-section>

      <q-card-section>
        <div class="text-body2 text-grey-7 q-mb-md">
          Create a new currency for the exchange system
        </div>

        <q-form @submit="submitForm" class="q-gutter-md">
          <!-- Basic Information -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.name"
                label="Currency Name *"
                outlined
                :error="!!errors.name"
                :error-message="errors.name"
                hint="e.g., US Dollar, Iraqi Dinar"
              />
            </div>

            <div class="col-12 col-sm-5">
              <q-input
                v-model="form.code"
                label="Currency Code *"
                outlined
                maxlength="3"
                :error="!!errors.code"
                :error-message="errors.code"
                hint="3-letter ISO code (e.g., USD, IQD)"
                @input="form.code = form.code.toUpperCase()"
              />
            </div>
          </div>

          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.symbol"
                label="Currency Symbol"
                outlined
                :error="!!errors.symbol"
                :error-message="errors.symbol"
                hint="e.g., $, €, د.ع"
              />
            </div>

            <div class="col-12 col-sm-5">
              <q-input
                v-model="form.country"
                label="Country"
                outlined
                :error="!!errors.country"
                :error-message="errors.country"
                hint="e.g., United States, Iraq"
              />
            </div>
          </div>

          <!-- Flag Icon -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.flag_icon"
                label="Flag Icon"
                outlined
                :error="!!errors.flag_icon"
                :error-message="errors.flag_icon"
                hint="Unicode flag emoji (e.g., 🇺🇸, 🇮🇶)"
              >
                <template v-slot:append>
                  <div v-if="form.flag_icon" class="text-2xl">{{ form.flag_icon }}</div>
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-5">
              <q-input
                v-model.number="form.decimal_places"
                label="Decimal Places *"
                outlined
                type="number"
                min="0"
                max="8"
                :error="!!errors.decimal_places"
                :error-message="errors.decimal_places"
                hint="Number of decimal places (0-8)"
              />
            </div>
          </div>

          <!-- Amount Limits -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="form.min_amount"
                label="Minimum Amount *"
                outlined
                type="number"
                step="0.01"
                min="0"
                :error="!!errors.min_amount"
                :error-message="errors.min_amount"
                hint="Minimum transaction amount"
              />
            </div>

            <div class="col-12 col-sm-5">
              <q-input
                v-model.number="form.max_amount"
                label="Maximum Amount *"
                outlined
                type="number"
                step="0.01"
                min="0"
                :error="!!errors.max_amount"
                :error-message="errors.max_amount"
                hint="Maximum transaction amount"
              />
            </div>
          </div>

          <!-- Order -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="form.order"
                label="Display Order"
                outlined
                type="number"
                min="0"
                :error="!!errors.order"
                :error-message="errors.order"
                hint="Order for display (lower numbers first)"
              />
            </div>
          </div>

          <!-- Description -->
          <q-input
            v-model="form.description"
            label="Description"
            outlined
            type="textarea"
            rows="3"
            :error="!!errors.description"
            :error-message="errors.description"
            hint="Optional description of the currency"
          />

          <!-- Status Options -->
          <div class="q-gutter-sm">
            <q-checkbox
              v-model="form.is_active"
              label="Active Currency"
              color="positive"
            />
            <div class="text-caption text-grey-6 q-ml-lg">
              Active currencies can be used in exchange operations
            </div>
          </div>

          <div class="q-gutter-sm">
            <q-checkbox
              v-model="form.is_base_currency"
              label="Base Currency"
              color="accent"
            />
            <div class="text-caption text-grey-6 q-ml-lg">
              Only one currency can be set as base currency (used for conversions)
            </div>
          </div>

          <!-- Preview Card -->
          <q-card v-if="hasPreviewData" class="preview-card q-mt-lg" flat bordered>
            <q-card-section>
              <div class="text-subtitle2 q-mb-md">Preview</div>
              <div class="currency-preview">
                <div class="row items-center">
                  <div class="col-auto">
                    <div class="text-4xl q-mr-md">{{ form.flag_icon || '🏳️' }}</div>
                  </div>
                  <div class="col">
                    <div class="text-h6">{{ form.name || 'Currency Name' }}</div>
                    <div class="text-subtitle2 text-grey-6">
                      {{ form.country || 'Country' }}
                    </div>
                    <div class="q-mt-xs">
                      <q-chip
                        :color="form.is_base_currency ? 'accent' : 'primary'"
                        text-color="white"
                        size="sm"
                      >
                        {{ form.code || 'CODE' }}
                      </q-chip>
                      <q-chip
                        v-if="form.is_base_currency"
                        color="accent"
                        text-color="white"
                        size="xs"
                        class="q-ml-xs"
                      >
                        Base
                      </q-chip>
                    </div>
                  </div>
                  <div class="col-auto">
                    <div class="text-right">
                      <div class="text-caption text-grey-6">Symbol</div>
                      <div class="text-h6">{{ form.symbol || '$' }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-form>
      </q-card-section>

      <q-card-actions align="right" class="q-pt-none">
        <q-btn
          flat
          label="Cancel"
          color="grey"
          v-close-popup
          :disable="submitting"
        />
        <q-btn
          @click="submitForm"
          :loading="submitting"
          label="Create Currency"
          color="primary"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'

const $q = useQuasar()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'created', 'error'])

// Reactive data
const showModal = ref(props.modelValue)
const submitting = ref(false)

const form = ref({
  name: '',
  code: '',
  symbol: '',
  country: '',
  flag_icon: '',
  decimal_places: 2,
  min_amount: 0.01,
  max_amount: 1000000,
  order: 0,
  description: '',
  is_active: true,
  is_base_currency: false
})

const errors = ref({})

// Computed
const hasPreviewData = computed(() => {
  return form.value.name || form.value.code || form.value.symbol || form.value.country
})

// Watch for model value changes
watch(() => props.modelValue, (newVal) => {
  showModal.value = newVal
})

watch(showModal, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// Methods
const resetForm = () => {
  form.value = {
    name: '',
    code: '',
    symbol: '',
    country: '',
    flag_icon: '',
    decimal_places: 2,
    min_amount: 0.01,
    max_amount: 1000000,
    order: 0,
    description: '',
    is_active: true,
    is_base_currency: false
  }
  errors.value = {}
}

const submitForm = async () => {
  submitting.value = true
  errors.value = {}

  try {
    const response = await axios.post('/api/admin/currencies', form.value)

    if (response.data.success) {
      $q.notify({
        type: 'positive',
        message: 'Currency created successfully',
        timeout: 3000
      })

      emit('created', {
        success: true,
        currency: response.data.currency
      })

      showModal.value = false
    }
  } catch (error) {
    console.error('Error creating currency:', error)

    if (error.response?.data?.errors) {
      errors.value = error.response.data.errors
    }

    emit('error', {
      success: false,
      error: error.response?.data?.message || error.message
    })

    $q.notify({
      type: 'negative',
      message: error.response?.data?.message || 'Failed to create currency',
      timeout: 5000
    })
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.q-card {
  border-radius: 12px;
}

.currency-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 2px dashed #e0e0e0;
}

.preview-card {
  border-radius: 8px;
}
</style>
