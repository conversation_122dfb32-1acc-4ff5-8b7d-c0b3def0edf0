<script setup>
import { defineProps, ref, watch, onMounted } from 'vue';
import Clock from '../components/Clock.vue';

const props = defineProps({
  t: {
    type: Object,
    required: true
  },
  isRTL: {
    type: Boolean,
    required: true
  },
  lastUpdated: {
    type: String,
    required: true
  },
  darkMode: {
    type: Boolean,
    required: true
  },
  currentLang: {
    type: String,
    required: true
  },
  languages: {
    type: Array,
    required: true
  },
  digitalTime: {
    type: String,
    required: true
  },
  currentDate: {
    type: String,
    default: ''
  },
  toggleDarkMode: {
    type: Function,
    required: true
  },
  weatherData: {
    type: Object,
    required: true
  },
  weatherIcons: {
    type: Object,
    required: true
  },
  weatherColors: {
    type: Object,
    required: true
  },
  refreshWeather: {
    type: Function,
    required: true
  }
});

const emit = defineEmits(['update:currentLang', 'update:darkMode']);

// Internal model for the toggle
const internalDarkMode = ref(props.darkMode);

// Watch for external changes to darkMode
watch(() => props.darkMode, (newValue) => {
  internalDarkMode.value = newValue;
});

// Watch for internal changes
watch(internalDarkMode, (newValue) => {
  if (newValue !== props.darkMode) {
  // Emit the update event to the parent component
  emit('update:darkMode', newValue);

  // Call the toggleDarkMode function from props
  props.toggleDarkMode();
  }
});

const handleDarkModeToggle = () => {
  // Toggle the internal state
  internalDarkMode.value = !internalDarkMode.value;
};

// Ensure sync on mount
onMounted(() => {
  internalDarkMode.value = props.darkMode;
});

// Company name based on language
const getCompanyName = () => {
  switch(props.currentLang) {
    case 'en':
      return 'Darayay Bawar Company';
    case 'ar':
      return 'شركة دریای باور';
    case 'ku':
    default:
      return 'کۆمپانیای دەریای باوەڕ';
  }
};

// Check if current language is RTL
const isRtlLang = () => {
  return props.currentLang === 'ar' || props.currentLang === 'ku';
};

// Helper functions for language display
const getLangFlag = (code) => {
  // Custom flag mapping
  if (code === 'ku') {
    return 'kurdistan'; // Custom flag name
  } else if (code === 'ar') {
    return 'iq'; // Iraq flag for Arabic
  } else {
    const lang = props.languages.find(lang => lang.code === code);
    return lang ? lang.flag : 'gb'; // Default to GB flag if not found
  }
};

const getFlagUrl = (flagCode) => {
  // Special handling for Kurdistan flag
  if (flagCode === 'kurdistan') {
    return '/flags/kurdistan.png'; // Path to custom Kurdistan flag
  }
  return `https://flagcdn.com/w80/${flagCode}.png`;
};

const getLangName = (code) => {
  const lang = props.languages.find(lang => lang.code === code);
  return lang ? lang.name : 'English'; // Default to English if not found
};

// Native language names
const nativeNames = {
  en: 'English',
  ku: 'کوردی',
  ar: 'العربية'
};

const getNativeName = (code) => {
  return nativeNames[code] || code.toUpperCase();
};
</script>

<template>
  <header class="header" :class="{ 'dark-mode': darkMode }" role="banner">
    <div class="header-content">
      <!-- Top Section: Logo and Company Info -->
      <div class="header-top">
        <div class="brand-section" :class="{ 'rtl': isRTL }">
          <div class="logo-wrapper" role="img" aria-label="Company Logo">
            <img src="/logo.png" alt="Logo" class="logo" />
      </div>
          <h1 class="company-name" :class="{ 'rtl-text': isRtlLang() }">
                    {{ getCompanyName() }}
          </h1>
                </div>

        <!-- Quick Actions -->
        <div class="quick-actions" :dir="'ltr'">
          <!-- Weather Widget -->
          <div class="action-card weather-widget"
               :class="{ 'dark': internalDarkMode, 'loading': weatherData.loading }"
               role="button"
               tabindex="0"
               @keyup.enter="refreshWeather"
               @keyup.space="refreshWeather">
            <div class="action-card-content">
              <div v-if="weatherData.loading" class="loading">
                        <q-spinner-dots :color="darkMode ? 'blue-5' : 'primary'" size="sm" />
                      </div>
              <div v-else-if="weatherData.error" class="error">
                        <q-icon name="error" color="negative" size="sm" />
                        <q-btn flat dense round icon="refresh" @click="refreshWeather" :color="darkMode ? 'blue-5' : 'primary'" size="xs" />
                      </div>
              <div v-else class="weather-info" @click="refreshWeather">
                <div class="weather-icon-wrapper">
                        <q-icon
                          :name="weatherIcons[weatherData.condition]"
                          :color="weatherColors[weatherData.condition]"
                          size="md"
                    class="weather-icon"
                  />
                  <div class="weather-icon-glow"></div>
                </div>
                <div class="weather-details">
                  <span class="temperature">{{ weatherData.temperature }}°</span>
                  <span class="condition">{{ weatherData.condition }}</span>
                </div>
                      </div>
            </div>
            <div class="action-card-background"></div>
                    </div>

          <!-- Dark Mode Toggle -->
          <div class="action-card theme-toggle"
               :class="{ 'dark': internalDarkMode }"
               @click="handleDarkModeToggle()"
               role="button"
               tabindex="0"
               @keyup.enter="handleDarkModeToggle()"
               @keyup.space="handleDarkModeToggle()">
            <div class="action-card-content">
              <div class="toggle-content">
                <div class="toggle-wrapper">
                        <q-toggle
                    v-model="internalDarkMode"
                    :icon="internalDarkMode ? 'light_mode' : 'dark_mode'"
                    :color="internalDarkMode ? 'orange' : 'primary'"
                    size="xl"
                          @click.stop
                    aria-label="Toggle dark mode"
                  />
                  <div class="toggle-ripple"></div>
                </div>
                <div class="toggle-glow"></div>
              </div>
            </div>
            <div class="action-card-background"></div>
          </div>

          <!-- Language Selector -->
          <div class="action-card language-selector" :class="{ 'dark': internalDarkMode }">
            <q-btn-dropdown
              flat
              class="lang-btn"
              aria-label="Select language"
            >
              <template v-slot:label>
                <div class="current-lang">
                  <div class="flag-wrapper">
                    <q-img
                      :src="getFlagUrl(getLangFlag(currentLang))"
                      :ratio="4/3"
                      class="flag-img"
                      :alt="`${getLangName(currentLang)} flag`"
                    />
                    <div class="flag-glow"></div>
                          </div>
                  <span class="lang-code">{{ getNativeName(currentLang) }}</span>
                      </div>
              </template>

              <q-list :class="internalDarkMode ? 'dark-list' : ''">
                <q-item
                  v-for="lang in languages"
                  :key="lang.code"
                  clickable
                  v-close-popup
                  @click="emit('update:currentLang', lang.code)"
                  :active="currentLang === lang.code"
                  :class="{ 'rtl': lang.code === 'ku' || lang.code === 'ar' }"
                  :active-class="internalDarkMode ? 'active-dark' : 'active-light'"
                >
                  <q-item-section avatar>
                    <div class="flag-wrapper">
                      <q-img
                        :src="getFlagUrl(getLangFlag(lang.code))"
                        :ratio="4/3"
                        class="flag-img"
                        :alt="`${lang.name} flag`"
                      />
                      <div class="flag-glow"></div>
                    </div>
                            </q-item-section>
                            <q-item-section>
                    <q-item-label>{{ getNativeName(lang.code) }}</q-item-label>
                    <q-item-label caption>{{ lang.name }}</q-item-label>
                  </q-item-section>
                  <q-item-section side v-if="currentLang === lang.code">
                    <q-icon name="check" color="primary" />
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </q-btn-dropdown>
                    </div>
                  </div>
                </div>

      <!-- Bottom Section: Clock and Title -->
      <div class="header-bottom">


        <div class="info-row" :dir="'ltr'">
          <div class="clock-section">
            <Clock
              :digitalTime="digitalTime"
              :currentDate="currentDate"
              :currentLang="currentLang"
            />
          </div>

          <div class="last-update">
            <q-chip
              size="sm"
              icon="update"
              :color="darkMode ? 'blue-7' : 'primary'"
              text-color="white"
              class="update-chip"
            >
              {{ lastUpdated }}
            </q-chip>
          </div>
        </div>
      </div>
  </div>
  </header>
</template>

<style scoped>
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  z-index: 0;
  transition: opacity 0.3s ease;
}

.header.dark-mode {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.07);
}

.header.dark-mode::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(14, 165, 233, 0.01) 100%);
}

.header-content {
  padding: 24px;
  position: relative;
  z-index: 1;
}

/* Top Section Styles */
.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.brand-section.rtl {
  flex-direction: row-reverse;
}

.logo-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  padding: 4px;
  will-change: transform;
}

.logo-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.dark-mode .logo-wrapper {
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.dark-mode .logo-wrapper:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.logo-wrapper:hover .logo {
  transform: scale(1.02);
}

.company-name {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b 0%, #**********%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  transition: all 0.3s ease;
  line-height: 1.2;
  will-change: transform;
}

.dark-mode .company-name {
  background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(96, 165, 250, 0.2);
}

/* Quick Actions Styles */
.quick-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: nowrap;
  justify-content: flex-end;
  direction: ltr !important; /* Force LTR direction always */
}

/* Action Card Base Styles */
.action-card {
  position: relative;
  min-width: 120px;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  will-change: transform;
}

.action-card-content {
  position: relative;
  z-index: 1;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-2px);
}

.action-card:hover .action-card-background {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
}

/* Weather Widget Styles */
.weather-widget {
  background: rgba(255, 255, 255, 0.8);
}

.weather-widget.dark {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(96, 165, 250, 0.1);
}

.weather-widget.dark .action-card-background {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(14, 165, 233, 0.02) 100%);
  border-color: rgba(96, 165, 250, 0.1);
}

.weather-widget.dark:hover .action-card-background {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.08) 0%, rgba(14, 165, 233, 0.04) 100%);
  border-color: rgba(96, 165, 250, 0.15);
}

.weather-icon-wrapper {
  position: relative;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.dark-mode .weather-icon-wrapper {
  background: rgba(59, 130, 246, 0.1);
}

.weather-icon {
  position: relative;
  z-index: 1;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  animation: weatherIconFloat 3s ease-in-out infinite;
}

.dark-mode .weather-icon {
  filter: drop-shadow(0 0 3px rgba(96, 165, 250, 0.3));
}

.weather-icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: weatherGlowPulse 3s ease-in-out infinite;
}

.dark-mode .weather-icon-glow {
  background: radial-gradient(circle, rgba(96, 165, 250, 0.3) 0%, rgba(96, 165, 250, 0) 70%);
}

.weather-widget:hover .weather-icon {
  transform: scale(1.2) rotate(10deg);
}

.weather-widget:hover .weather-icon-glow {
  opacity: 1;
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.temperature {
  font-weight: 600;
  font-size: 1.2rem;
  background: linear-gradient(135deg, #1e293b 0%, #**********%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark-mode .temperature {
  background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 1px rgba(96, 165, 250, 0.5));
}

.condition {
  font-size: 0.8rem;
  opacity: 0.8;
  text-transform: capitalize;
}

.dark-mode .condition {
  opacity: 0.9;
  color: #94a3b8;
}

/* Theme Toggle Styles */
.theme-toggle {
  background: rgba(255, 255, 255, 0.8);
  min-width: auto;
  width: 70px;
  height: 70px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  user-select: none;
}

.theme-toggle.dark {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(96, 165, 250, 0.1);
}

.theme-toggle.dark .action-card-background {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(14, 165, 233, 0.02) 100%);
  border-color: rgba(96, 165, 250, 0.1);
}

.theme-toggle.dark:hover .action-card-background {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.08) 0%, rgba(14, 165, 233, 0.04) 100%);
  border-color: rgba(96, 165, 250, 0.15);
}

.toggle-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.toggle-wrapper {
  position: relative;
  z-index: 1;
}

.theme-toggle :deep(.q-toggle__inner) {
  font-size: 28px;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle :deep(.q-toggle__track) {
  opacity: 0.7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(0, 0, 0, 0.1);
  transform-origin: center;
  width: 60px;
  height: 30px;
}

.theme-toggle.dark :deep(.q-toggle__track) {
  background: rgba(96, 165, 250, 0.15);
  border: 1px solid rgba(96, 165, 250, 0.2);
}

.theme-toggle:hover :deep(.q-toggle__track) {
  opacity: 1;
  transform: scale(1.05);
}

.theme-toggle :deep(.q-toggle__thumb) {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle.dark :deep(.q-toggle__thumb) {
  box-shadow: 0 0 8px rgba(96, 165, 250, 0.5);
}

.theme-toggle:hover :deep(.q-toggle__thumb) {
  transform: scale(1.1);
}

.theme-toggle :deep(.q-toggle) {
  cursor: pointer;
}

.theme-toggle :deep(.q-toggle__inner--truthy) {
  color: var(--q-primary);
}

.theme-toggle :deep(.q-toggle__inner--falsy) {
  color: var(--q-orange);
}

.theme-toggle :deep(.q-toggle__icon) {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle.dark :deep(.q-toggle__icon) {
  filter: drop-shadow(0 0 3px rgba(247, 213, 148, 0.5));
}

.toggle-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 50%;
}

.theme-toggle.dark .toggle-ripple {
  background: radial-gradient(circle, rgba(96, 165, 250, 0.3) 0%, rgba(96, 165, 250, 0) 70%);
}

.theme-toggle:active .toggle-ripple {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.theme-toggle:hover .toggle-glow {
  opacity: 1;
}

.theme-toggle.dark .toggle-glow {
  background: radial-gradient(circle, rgba(96, 165, 250, 0.2) 0%, rgba(96, 165, 250, 0) 70%);
}

/* Language Selector Styles */
.language-selector {
  background: rgba(255, 255, 255, 0.8);
  min-width: auto;
  width: 80px;
  height: 70px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible;
}

.language-selector.dark {
  background: rgba(15, 23, 42, 0.7);
  border: 1px solid rgba(96, 165, 250, 0.1);
}

.language-selector.dark .action-card-background {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(14, 165, 233, 0.02) 100%);
  border-color: rgba(96, 165, 250, 0.1);
}

.language-selector.dark:hover .action-card-background {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.08) 0%, rgba(14, 165, 233, 0.04) 100%);
  border-color: rgba(96, 165, 250, 0.15);
}

.lang-btn {
  width: 100%;
  height: 100%;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.current-lang {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
}

.flag-wrapper {
  position: relative;
  width: 40px;
  height: 30px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.dark-mode .flag-wrapper {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(96, 165, 250, 0.1);
}

.flag-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flag-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark-mode .flag-glow {
  background: radial-gradient(circle at center, rgba(96, 165, 250, 0.2) 0%, rgba(96, 165, 250, 0) 70%);
}

.flag-wrapper:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.dark-mode .flag-wrapper:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(96, 165, 250, 0.2);
}

.flag-wrapper:hover .flag-glow {
  opacity: 1;
}

.lang-code {
  font-weight: 600;
  font-size: 0.8rem;
  background: linear-gradient(135deg, #1e293b 0%, #**********%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.dark-mode .lang-code {
  background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 1px rgba(96, 165, 250, 0.5));
}

.dark-list {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(96, 165, 250, 0.1);
  border-radius: 12px;
  overflow: hidden;
  padding: 8px;
  transform-origin: top center;
  animation: dropdownFade 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  min-width: 200px;
  margin-top: 8px;
}

.dark-list .q-item {
  border-radius: 8px;
  margin: 2px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 8px 12px;
}

.dark-list .q-item:hover {
  background: rgba(96, 165, 250, 0.08);
  transform: translateX(4px);
}

.dark-list .q-item:hover .flag-wrapper {
  transform: scale(1.1);
}

.dark-list .q-item:hover .flag-glow {
  opacity: 1;
}

.dark-list .q-item-label {
  color: #e2e8f0;
  font-weight: 500;
}

.dark-list .q-item-label__caption {
  color: #94a3b8;
}

.dark-list .q-item.active-dark {
  background: rgba(59, 130, 246, 0.15);
}

.active-light {
  background: rgba(227, 242, 253, 0.9) !important;
  color: #1976d2 !important;
}

.active-dark {
  background: rgba(37, 99, 235, 0.2) !important;
  color: white !important;
  border-left: 3px solid rgba(96, 165, 250, 0.8);
}

/* Clock section */
.header-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  padding: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  flex-wrap: nowrap;
  min-width: 240px;
  max-width: fit-content;
  margin-left: auto;
}

.dark-mode .info-row {
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(96, 165, 250, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.clock-section,
.last-update {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.clock-section {
  margin-right: 12px;
  position: relative;
  padding: 0;
  background: transparent;
  border-radius: 8px;
  backdrop-filter: blur(4px);
  border: none;
  transition: all 0.3s ease;
  transform: scale(1.05);
}

.clock-section:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: none;
}

.dark-mode .clock-section {
  background: transparent;
  border: none;
}

.dark-mode .clock-section:hover {
  box-shadow: none;
}

.update-chip {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 32px;
  padding: 0 12px;
  font-size: 0.95rem;
  border-radius: 12px;
  font-weight: 500;
  background: linear-gradient(to right, rgba(37, 99, 235, 0.9), rgba(37, 99, 235, 0.7)) !important;
}

.update-chip:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(to right, rgba(37, 99, 235, 1), rgba(37, 99, 235, 0.8)) !important;
}

.dark-mode .update-chip {
  background: linear-gradient(to right, rgba(59, 130, 246, 0.9), rgba(14, 165, 233, 0.7)) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(96, 165, 250, 0.2);
}

.dark-mode .update-chip:hover {
  background: linear-gradient(to right, rgba(59, 130, 246, 1), rgba(14, 165, 233, 0.8)) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(96, 165, 250, 0.3);
}

.update-chip :deep(.q-chip__icon) {
  font-size: 1.2rem;
  margin-right: 6px;
  color: white !important;
  animation: spinSlow 6s linear infinite;
}

@keyframes spinSlow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.dark-mode .page-title {
  color: #e2e8f0;
  text-shadow: 0 0 10px rgba(96, 165, 250, 0.1);
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes dropdownFade {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* RTL Support */
.rtl-text {
  direction: rtl;
  text-align: right;
  unicode-bidi: bidi-override;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 16px;
  }

  .header-top {
    flex-direction: column;
    gap: 16px;
  }

  .quick-actions {
    width: 100%;
    justify-content: space-between;
  }

  .company-name {
    font-size: 1.5rem;
  }

  .logo-wrapper {
    width: 60px;
    height: 60px;
  }

  .header-bottom {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .info-row {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 8px;
    padding: 8px 12px;
    flex-wrap: wrap;
  }

  .clock-section {
    margin-right: 0;
  }

  .page-title {
    font-size: 1.4rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .quick-actions {
    flex-wrap: wrap;
  }

  .action-card {
    min-width: 90px;
  }

  .action-card-content {
    padding: 8px 12px;
  }

  .weather-icon-wrapper {
    width: 32px;
    height: 32px;
  }

  .temperature {
    font-size: 1.1rem;
  }

  .condition {
    font-size: 0.7rem;
  }

  .theme-toggle :deep(.q-toggle__track) {
    width: 50px;
    height: 25px;
  }

  .language-selector {
    width: 60px;
    height: 60px;
  }

  .flag-wrapper {
    width: 36px;
    height: 27px;
  }

  .lang-code {
    font-size: 0.7rem;
  }

  .info-row {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: 8px;
    padding: 8px 12px;
    flex-wrap: nowrap;
    width: fit-content;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
  }

  .clock-section {
    margin-right: 4px;
  }
}

/* Touch Device Optimizations */
@media (hover: none) {
  .action-card:active {
    transform: scale(0.98);
  }

  .theme-toggle:active :deep(.q-toggle__thumb) {
    transform: scale(0.95);
  }

  .flag-wrapper:active {
    transform: scale(0.95);
  }

  .lang-btn:active .dropdown-icon {
    transform: translateY(2px);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .header,
  .logo-wrapper,
  .weather-widget,
  .theme-toggle,
  .language-selector,
  .weather-icon,
  .logo,
  .company-name,
  .page-title,
  .update-chip {
    transition: none !important;
    animation: none !important;
  }
}

@keyframes weatherIconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes weatherGlowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.2);
  }
}
</style>
