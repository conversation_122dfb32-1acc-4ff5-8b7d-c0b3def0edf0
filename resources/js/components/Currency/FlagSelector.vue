<template>
  <div class="flag-selector">
    <q-select
      v-model="selectedFlag"
      :options="flagOptions"
      :label="label"
      :placeholder="placeholder"
      :outlined="outlined"
      :filled="filled"
      :dense="dense"
      :clearable="clearable"
      :loading="loading"
      :disable="disable"
      :readonly="readonly"
      option-value="code"
      option-label="display_name"
      emit-value
      map-options
      use-input
      @filter="filterFlags"
      @update:model-value="onSelectionChange"
      class="flag-select"
    >
      <template v-slot:prepend v-if="selectedFlagData">
        <CountryFlag
          :code="selectedFlagData.code"
          :country="selectedFlagData.name"
          size="small"
          :shadow="false"
        />
      </template>

      <template v-slot:option="scope">
        <q-item v-bind="scope.itemProps" class="flag-option">
          <q-item-section avatar>
            <CountryFlag
              :code="scope.opt.code"
              :country="scope.opt.name"
              size="small"
              :shadow="false"
            />
          </q-item-section>
          
          <q-item-section>
            <q-item-label>{{ scope.opt.name }}</q-item-label>
            <q-item-label caption>{{ scope.opt.code.toUpperCase() }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:no-option>
        <q-item>
          <q-item-section class="text-grey">
            No flags found
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- Flag Preview -->
    <div v-if="selectedFlagData && showPreview" class="flag-preview q-mt-md">
      <q-card flat bordered class="q-pa-md">
        <div class="text-subtitle2 q-mb-sm">Preview</div>
        <div class="row q-gutter-md items-center">
          <div class="col-auto">
            <CountryFlag
              :code="selectedFlagData.code"
              :country="selectedFlagData.name"
              size="large"
              :shadow="true"
            />
          </div>
          <div class="col">
            <div class="text-weight-medium">{{ selectedFlagData.name }}</div>
            <div class="text-caption text-grey-6">{{ selectedFlagData.code.toUpperCase() }}</div>
          </div>
        </div>
      </q-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import CountryFlag from '@/components/CountryFlag.vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: 'Select Flag'
  },
  placeholder: {
    type: String,
    default: 'Choose a flag...'
  },
  outlined: {
    type: Boolean,
    default: true
  },
  filled: {
    type: Boolean,
    default: false
  },
  dense: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disable: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  showPreview: {
    type: Boolean,
    default: true
  },
  currencyCode: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// Reactive data
const loading = ref(false)
const selectedFlag = ref(props.modelValue)
const searchFilter = ref('')

// Flag data - comprehensive list of countries and their codes
const flagData = [
  // Major currencies
  { code: 'us', name: 'United States', currency: 'USD' },
  { code: 'eu', name: 'European Union', currency: 'EUR' },
  { code: 'gb', name: 'United Kingdom', currency: 'GBP' },
  { code: 'jp', name: 'Japan', currency: 'JPY' },
  { code: 'ca', name: 'Canada', currency: 'CAD' },
  { code: 'au', name: 'Australia', currency: 'AUD' },
  { code: 'ch', name: 'Switzerland', currency: 'CHF' },
  { code: 'cn', name: 'China', currency: 'CNY' },
  { code: 'in', name: 'India', currency: 'INR' },
  
  // Middle East & Regional
  { code: 'tr', name: 'Turkey', currency: 'TRY' },
  { code: 'ir', name: 'Iran', currency: 'IRR' },
  { code: 'iq', name: 'Iraq', currency: 'IQD' },
  { code: 'sa', name: 'Saudi Arabia', currency: 'SAR' },
  { code: 'ae', name: 'United Arab Emirates', currency: 'AED' },
  { code: 'kw', name: 'Kuwait', currency: 'KWD' },
  { code: 'bh', name: 'Bahrain', currency: 'BHD' },
  { code: 'om', name: 'Oman', currency: 'OMR' },
  { code: 'qa', name: 'Qatar', currency: 'QAR' },
  { code: 'jo', name: 'Jordan', currency: 'JOD' },
  { code: 'lb', name: 'Lebanon', currency: 'LBP' },
  { code: 'eg', name: 'Egypt', currency: 'EGP' },
  
  // Special case
  { code: 'kurdistan', name: 'Kurdistan', currency: 'KURDISTAN' },
  
  // Additional countries
  { code: 'ma', name: 'Morocco', currency: 'MAD' },
  { code: 'dz', name: 'Algeria', currency: 'DZD' },
  { code: 'tn', name: 'Tunisia', currency: 'TND' },
  { code: 'ru', name: 'Russia', currency: 'RUB' },
  { code: 'br', name: 'Brazil', currency: 'BRL' },
  { code: 'mx', name: 'Mexico', currency: 'MXN' },
  { code: 'kr', name: 'South Korea', currency: 'KRW' },
  { code: 'sg', name: 'Singapore', currency: 'SGD' },
  { code: 'hk', name: 'Hong Kong', currency: 'HKD' },
  { code: 'nz', name: 'New Zealand', currency: 'NZD' },
  { code: 'se', name: 'Sweden', currency: 'SEK' },
  { code: 'no', name: 'Norway', currency: 'NOK' },
  { code: 'dk', name: 'Denmark', currency: 'DKK' },
  { code: 'pl', name: 'Poland', currency: 'PLN' },
  { code: 'cz', name: 'Czech Republic', currency: 'CZK' },
  { code: 'hu', name: 'Hungary', currency: 'HUF' },
  { code: 'ro', name: 'Romania', currency: 'RON' },
  { code: 'bg', name: 'Bulgaria', currency: 'BGN' },
  { code: 'hr', name: 'Croatia', currency: 'HRK' },
  { code: 'rs', name: 'Serbia', currency: 'RSD' }
]

// Computed properties
const flagOptions = computed(() => {
  let options = flagData.map(flag => ({
    ...flag,
    display_name: `${flag.name} (${flag.code.toUpperCase()})`
  }))

  // Filter based on search
  if (searchFilter.value) {
    const search = searchFilter.value.toLowerCase()
    options = options.filter(opt => 
      opt.name.toLowerCase().includes(search) ||
      opt.code.toLowerCase().includes(search) ||
      opt.currency.toLowerCase().includes(search)
    )
  }

  // Sort alphabetically
  return options.sort((a, b) => a.name.localeCompare(b.name))
})

const selectedFlagData = computed(() => {
  if (!selectedFlag.value) return null
  return flagData.find(flag => flag.code === selectedFlag.value)
})

// Methods
const filterFlags = (val, update) => {
  update(() => {
    searchFilter.value = val
  })
}

const onSelectionChange = (value) => {
  selectedFlag.value = value
  emit('update:modelValue', value)
  emit('change', value, selectedFlagData.value)
}

const autoSelectFromCurrency = () => {
  if (props.currencyCode && !selectedFlag.value) {
    const matchingFlag = flagData.find(flag => 
      flag.currency === props.currencyCode.toUpperCase()
    )
    if (matchingFlag) {
      onSelectionChange(matchingFlag.code)
    }
  }
}

// Watchers
watch(() => props.modelValue, (newValue) => {
  selectedFlag.value = newValue
})

watch(() => props.currencyCode, () => {
  autoSelectFromCurrency()
})

// Lifecycle
onMounted(() => {
  autoSelectFromCurrency()
})

// Expose methods for parent components
defineExpose({
  selectedFlagData,
  flagData
})
</script>

<style scoped>
.flag-selector {
  width: 100%;
}

.flag-select {
  min-width: 200px;
}

.flag-option {
  padding: 8px 16px;
}

.flag-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.dark .flag-option:hover {
  background-color: rgba(255, 255, 255, 0.04);
}

.flag-option .q-item__section--avatar {
  min-width: 40px;
}

.flag-preview {
  max-width: 300px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .flag-select {
    min-width: 150px;
  }
  
  .flag-option {
    padding: 6px 12px;
  }
  
  .flag-preview {
    max-width: 100%;
  }
}
</style>
