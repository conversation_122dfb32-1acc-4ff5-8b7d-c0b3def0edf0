<template>
  <q-select
    v-model="selectedCurrency"
    :options="currencyOptions"
    :label="label"
    :placeholder="placeholder"
    :outlined="outlined"
    :filled="filled"
    :dense="dense"
    :multiple="multiple"
    :clearable="clearable"
    :loading="loading"
    :disable="disable"
    :readonly="readonly"
    option-value="id"
    option-label="display_name"
    emit-value
    map-options
    use-input
    @filter="filterCurrencies"
    @update:model-value="onSelectionChange"
    class="currency-selector"
  >
    <template v-slot:prepend v-if="showFlag && selectedCurrencyData">
      <CountryFlag
        :code="selectedCurrencyData.code"
        :country="selectedCurrencyData.country"
        :flag-icon="selectedCurrencyData.flag_icon"
        size="small"
        :shadow="false"
      />
    </template>

    <template v-slot:option="scope">
      <q-item v-bind="scope.itemProps" class="currency-option">
        <q-item-section avatar v-if="showFlag">
          <CountryFlag
            :code="scope.opt.code"
            :country="scope.opt.country"
            :flag-icon="scope.opt.flag_icon"
            size="small"
            :shadow="false"
          />
        </q-item-section>
        
        <q-item-section>
          <q-item-label>{{ scope.opt.name }}</q-item-label>
          <q-item-label caption>
            {{ scope.opt.code }} • {{ scope.opt.country }}
          </q-item-label>
        </q-item-section>
        
        <q-item-section side v-if="showStatus">
          <q-chip
            :color="scope.opt.is_active ? 'positive' : 'negative'"
            text-color="white"
            size="xs"
          >
            {{ scope.opt.is_active ? 'Active' : 'Inactive' }}
          </q-chip>
        </q-item-section>
        
        <q-item-section side v-if="scope.opt.is_base_currency">
          <q-chip color="accent" text-color="white" size="xs">
            Base
          </q-chip>
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:no-option>
      <q-item>
        <q-item-section class="text-grey">
          No currencies found
        </q-item-section>
      </q-item>
    </template>

    <template v-slot:loading>
      <q-item>
        <q-item-section>
          <q-item-label>
            <q-skeleton type="text" />
          </q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import CountryFlag from '@/components/CountryFlag.vue'
import axios from 'axios'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: null
  },
  label: {
    type: String,
    default: 'Select Currency'
  },
  placeholder: {
    type: String,
    default: 'Choose a currency...'
  },
  outlined: {
    type: Boolean,
    default: true
  },
  filled: {
    type: Boolean,
    default: false
  },
  dense: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  disable: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  showFlag: {
    type: Boolean,
    default: true
  },
  showStatus: {
    type: Boolean,
    default: false
  },
  activeOnly: {
    type: Boolean,
    default: false
  },
  excludeIds: {
    type: Array,
    default: () => []
  },
  apiEndpoint: {
    type: String,
    default: '/api/admin/currencies'
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'error'])

const $q = useQuasar()

// Reactive data
const currencies = ref([])
const loading = ref(false)
const selectedCurrency = ref(props.modelValue)

// Computed properties
const currencyOptions = computed(() => {
  let options = currencies.value.map(currency => ({
    id: currency.id,
    code: currency.code,
    name: currency.name?.en || currency.name,
    country: currency.country,
    flag_icon: currency.flag_icon,
    is_active: currency.is_active,
    is_base_currency: currency.is_base_currency,
    display_name: `${currency.name?.en || currency.name} (${currency.code})`
  }))

  // Filter active only if requested
  if (props.activeOnly) {
    options = options.filter(opt => opt.is_active)
  }

  // Exclude specific IDs if provided
  if (props.excludeIds.length > 0) {
    options = options.filter(opt => !props.excludeIds.includes(opt.id))
  }

  return options
})

const selectedCurrencyData = computed(() => {
  if (!selectedCurrency.value) return null
  return currencyOptions.value.find(opt => opt.id === selectedCurrency.value)
})

// Methods
const loadCurrencies = async () => {
  loading.value = true
  
  try {
    const response = await axios.get(props.apiEndpoint, {
      params: {
        per_page: 100, // Get all currencies for selector
        sort_by: 'order',
        sort_direction: 'asc'
      }
    })
    
    currencies.value = response.data.currencies?.data || response.data.currencies || []
  } catch (error) {
    console.error('Failed to load currencies:', error)
    emit('error', error)
    
    $q.notify({
      type: 'negative',
      message: 'Failed to load currencies',
      position: 'top'
    })
  } finally {
    loading.value = false
  }
}

const filterCurrencies = (val, update) => {
  update(() => {
    // The filtering is handled by the computed property
    // This is just to satisfy the q-select filter requirement
  })
}

const onSelectionChange = (value) => {
  selectedCurrency.value = value
  emit('update:modelValue', value)
  emit('change', value, selectedCurrencyData.value)
}

// Watchers
watch(() => props.modelValue, (newValue) => {
  selectedCurrency.value = newValue
})

// Lifecycle
onMounted(() => {
  loadCurrencies()
})

// Expose methods for parent components
defineExpose({
  loadCurrencies,
  selectedCurrencyData
})
</script>

<style scoped>
.currency-selector {
  min-width: 200px;
}

.currency-option {
  padding: 8px 16px;
}

.currency-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.dark .currency-option:hover {
  background-color: rgba(255, 255, 255, 0.04);
}

.currency-option .q-item__section--avatar {
  min-width: 40px;
}

.currency-option .q-item__section--side {
  padding-left: 8px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .currency-selector {
    min-width: 150px;
  }
  
  .currency-option {
    padding: 6px 12px;
  }
}
</style>
