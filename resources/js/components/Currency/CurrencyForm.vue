<template>
  <q-form @submit="onSubmit" @reset="onReset" class="currency-form">
    <div class="row q-gutter-md">
      <!-- Basic Information -->
      <div class="col-12">
        <div class="text-h6 q-mb-md">Basic Information</div>
      </div>

      <!-- Currency Name (English) -->
      <div class="col-12 col-md-6">
        <q-input
          v-model="form.name.en"
          label="Currency Name (English) *"
          outlined
          :rules="[val => !!val || 'English name is required']"
          :error="hasError('name.en')"
          :error-message="getError('name.en')"
        />
      </div>

      <!-- Currency Name (Kurdish) -->
      <div class="col-12 col-md-6">
        <q-input
          v-model="form.name.ku"
          label="Currency Name (Kurdish)"
          outlined
          :error="hasError('name.ku')"
          :error-message="getError('name.ku')"
        />
      </div>

      <!-- Currency Code -->
      <div class="col-12 col-md-4">
        <q-input
          v-model="form.code"
          label="Currency Code *"
          outlined
          maxlength="3"
          counter
          :rules="[
            val => !!val || 'Currency code is required',
            val => val.length === 3 || 'Currency code must be 3 characters',
            val => /^[A-Z]{3}$/.test(val) || 'Currency code must be 3 uppercase letters'
          ]"
          :error="hasError('code')"
          :error-message="getError('code')"
          @update:model-value="onCodeChange"
        />
      </div>

      <!-- Currency Symbol -->
      <div class="col-12 col-md-4">
        <q-input
          v-model="form.symbol"
          label="Currency Symbol"
          outlined
          maxlength="10"
          :error="hasError('symbol')"
          :error-message="getError('symbol')"
        />
      </div>

      <!-- Country -->
      <div class="col-12 col-md-4">
        <q-input
          v-model="form.country"
          label="Country"
          outlined
          :error="hasError('country')"
          :error-message="getError('country')"
        />
      </div>

      <!-- Flag Selection -->
      <div class="col-12">
        <div class="text-subtitle1 q-mb-sm">Flag Selection</div>
        <FlagSelector
          v-model="form.flag_icon"
          label="Select Flag"
          :currency-code="form.code"
          :show-preview="true"
          @change="onFlagChange"
        />
      </div>

      <!-- Settings -->
      <div class="col-12">
        <div class="text-h6 q-mb-md q-mt-lg">Settings</div>
      </div>

      <!-- Decimal Places -->
      <div class="col-12 col-md-4">
        <q-input
          v-model.number="form.decimal_places"
          label="Decimal Places *"
          type="number"
          outlined
          min="0"
          max="8"
          :rules="[
            val => val !== null && val !== undefined || 'Decimal places is required',
            val => val >= 0 && val <= 8 || 'Decimal places must be between 0 and 8'
          ]"
          :error="hasError('decimal_places')"
          :error-message="getError('decimal_places')"
        />
      </div>

      <!-- Order -->
      <div class="col-12 col-md-4">
        <q-input
          v-model.number="form.order"
          label="Display Order"
          type="number"
          outlined
          min="0"
          :error="hasError('order')"
          :error-message="getError('order')"
        />
      </div>

      <!-- Status Toggles -->
      <div class="col-12 col-md-4">
        <div class="column q-gutter-sm">
          <q-toggle
            v-model="form.is_active"
            label="Active"
            color="positive"
            :disable="isBaseCurrency && !form.is_active"
          />
          <q-toggle
            v-model="form.is_base_currency"
            label="Base Currency"
            color="accent"
            @update:model-value="onBaseCurrencyChange"
          />
        </div>
      </div>

      <!-- Amount Limits -->
      <div class="col-12">
        <div class="text-subtitle1 q-mb-sm">Amount Limits</div>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model.number="form.min_amount"
          label="Minimum Amount *"
          type="number"
          outlined
          min="0"
          step="0.01"
          :rules="[
            val => val !== null && val !== undefined || 'Minimum amount is required',
            val => val >= 0 || 'Minimum amount must be positive'
          ]"
          :error="hasError('min_amount')"
          :error-message="getError('min_amount')"
        />
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model.number="form.max_amount"
          label="Maximum Amount *"
          type="number"
          outlined
          min="0"
          step="0.01"
          :rules="[
            val => val !== null && val !== undefined || 'Maximum amount is required',
            val => val >= 0 || 'Maximum amount must be positive',
            val => !form.min_amount || val > form.min_amount || 'Maximum amount must be greater than minimum amount'
          ]"
          :error="hasError('max_amount')"
          :error-message="getError('max_amount')"
        />
      </div>

      <!-- Description -->
      <div class="col-12">
        <div class="text-subtitle1 q-mb-sm">Description (Optional)</div>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.description.en"
          label="Description (English)"
          type="textarea"
          outlined
          rows="3"
          maxlength="1000"
          counter
          :error="hasError('description.en')"
          :error-message="getError('description.en')"
        />
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.description.ku"
          label="Description (Kurdish)"
          type="textarea"
          outlined
          rows="3"
          maxlength="1000"
          counter
          :error="hasError('description.ku')"
          :error-message="getError('description.ku')"
        />
      </div>

      <!-- Form Actions -->
      <div class="col-12 q-mt-lg">
        <div class="row q-gutter-sm justify-end">
          <q-btn
            type="reset"
            color="grey"
            outline
            :disable="loading"
          >
            Reset
          </q-btn>
          <q-btn
            type="submit"
            color="primary"
            :loading="loading"
            :disable="!isFormValid"
          >
            {{ submitLabel }}
          </q-btn>
        </div>
      </div>
    </div>
  </q-form>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import FlagSelector from './FlagSelector.vue'

// CurrencyForm component for admin panel

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  submitLabel: {
    type: String,
    default: 'Save Currency'
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'reset'])

const $q = useQuasar()

// Form data
const form = ref({
  name: {
    en: '',
    ku: ''
  },
  code: '',
  symbol: '',
  country: '',
  flag_icon: '',
  decimal_places: 2,
  order: 0,
  is_active: true,
  is_base_currency: false,
  min_amount: 0,
  max_amount: 1000000,
  description: {
    en: '',
    ku: ''
  },
  ...props.modelValue
})

// Computed properties
const isBaseCurrency = computed(() => form.value.is_base_currency)

const isFormValid = computed(() => {
  return form.value.name.en &&
         form.value.code &&
         form.value.code.length === 3 &&
         /^[A-Z]{3}$/.test(form.value.code) &&
         form.value.decimal_places !== null &&
         form.value.decimal_places >= 0 &&
         form.value.decimal_places <= 8 &&
         form.value.min_amount !== null &&
         form.value.min_amount >= 0 &&
         form.value.max_amount !== null &&
         form.value.max_amount > form.value.min_amount
})

// Methods
const hasError = (field) => {
  return !!props.errors[field]
}

const getError = (field) => {
  return props.errors[field]?.[0] || props.errors[field]
}

const onCodeChange = (value) => {
  form.value.code = value.toUpperCase()
  emit('update:modelValue', form.value)
}

const onFlagChange = (flagCode, flagData) => {
  if (flagData && !form.value.country) {
    form.value.country = flagData.name
  }
  emit('update:modelValue', form.value)
}

const onBaseCurrencyChange = (value) => {
  if (value) {
    // If setting as base currency, ensure it's active
    form.value.is_active = true

    $q.notify({
      type: 'info',
      message: 'Base currency has been set to active automatically',
      position: 'top'
    })
  }
  emit('update:modelValue', form.value)
}

const onSubmit = () => {
  emit('submit', form.value)
}

const onReset = () => {
  // Reset to initial values
  Object.assign(form.value, {
    name: { en: '', ku: '' },
    code: '',
    symbol: '',
    country: '',
    flag_icon: '',
    decimal_places: 2,
    order: 0,
    is_active: true,
    is_base_currency: false,
    min_amount: 0,
    max_amount: 1000000,
    description: { en: '', ku: '' }
  })
  emit('reset')
  emit('update:modelValue', form.value)
}

// Watchers
watch(() => props.modelValue, (newValue) => {
  Object.assign(form.value, newValue)
}, { deep: true })

watch(form, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })
</script>

<style scoped>
.currency-form {
  max-width: 800px;
}

.currency-form .q-field {
  margin-bottom: 8px;
}

.currency-form .q-toggle {
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .currency-form .row > div {
    padding: 4px;
  }
}
</style>
