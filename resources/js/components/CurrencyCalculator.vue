<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import CountryFlag from './CountryFlag.vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  currency: {
    type: Object,
    required: true
  },
  currencies: {
    type: Array,
    required: true
  },
  darkMode: {
    type: Boolean,
    required: true
  },
  t: {
    type: Object,
    required: true
  },
  formatCurrency: {
    type: Function,
    required: true
  }
});

const emit = defineEmits(['update:show', 'close', 'currency-changed']);
const $q = useQuasar();

// Haptic feedback for mobile devices
const triggerHapticFeedback = (type = 'light') => {
  if (typeof window !== 'undefined' && window.navigator && window.navigator.vibrate) {
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
      error: [50, 50, 50]
    };
    window.navigator.vibrate(patterns[type] || patterns.light);
  }
};

// Enhanced data structure
const calculationHistory = ref([]);
const currentInput = ref('');
const lastResult = ref(null);
const hasError = ref(false);
const isCalculating = ref(false);
const intermediateResult = ref(null);
const isNewCalculation = ref(true);
const calculatorMode = ref('toIQD'); // 'toIQD' or 'fromIQD'

const calculatorTitle = computed(() => {
  if (!selectedCurrency.value?.currency_code) return '';
  return calculatorMode.value === 'toIQD'
    ? `${props.t.calculate} ${selectedCurrency.value.currency_code} ${props.t.to} IQD`
    : `${props.t.calculate} IQD ${props.t.to} ${selectedCurrency.value.currency_code}`;
});

// Computed property for the current calculation expression
const calculationExpression = computed(() => {
  if (calculationHistory.value.length === 0) return '';

  let expression = calculationHistory.value.map(item => {
    if (typeof item === 'number') {
      return item.toString();
    }
    return {
      'add': '+',
      'subtract': '-',
      'multiply': '×',
      'divide': '÷'
    }[item];
  }).join(' ');

  if (currentInput.value) {
    expression += ' ' + currentInput.value;
  }

  return expression;
});

// Computed property for the current result
const calculatorResult = computed(() => {
  if (hasError.value) return 'Error';
  if (calculationHistory.value.length === 0 && !currentInput.value) return '0.00';

  try {
    let result;
    if (intermediateResult.value !== null) {
      result = intermediateResult.value;
    } else if (currentInput.value) {
      const numbers = [...calculationHistory.value, parseFloat(unformatNumber(currentInput.value))];
      result = calculateResult(numbers);
    } else {
      result = calculateResult(calculationHistory.value);
    }

    if (calculatorMode.value === 'toIQD') {
      result = result * (props.currency?.sale || 0);
    } else {
      result = result / (props.currency?.buy || 1);
    }
    return props.formatCurrency(result);
  } catch (error) {
    hasError.value = true;
    return 'Error';
  }
});

// Add new helper function for number validation
const isValidNumber = (value) => {
  if (value === undefined || value === null) return false;
  const num = parseFloat(value);
  return !isNaN(num) && isFinite(num);
};

// Update the calculateResult function to properly handle order of operations
const calculateResult = (numbers) => {
  if (!Array.isArray(numbers) || numbers.length === 0) return 0;
  if (numbers.length === 1) return numbers[0];

  try {
    // First pass: handle multiplication and division
    let result = [...numbers];
    let i = 1;
    while (i < result.length - 1) {
      if (result[i] === 'multiply' || result[i] === 'divide') {
        const left = result[i - 1];
        const right = result[i + 1];

        if (typeof left !== 'number' || typeof right !== 'number') {
          throw new Error('Invalid number in calculation');
        }

        if (result[i] === 'divide' && Math.abs(right) < 0.00000001) {
          throw new Error('Division by zero');
        }

        const operationResult = result[i] === 'multiply' ? left * right : left / right;
        result.splice(i - 1, 3, Math.round(operationResult * 1e8) / 1e8);
        i = 1; // Reset index to start from beginning after modification
      } else {
        i += 2;
      }
    }

    // Second pass: handle addition and subtraction
    let finalResult = result[0];
    for (let i = 1; i < result.length; i += 2) {
      const operator = result[i];
      const value = result[i + 1];

      if (typeof value !== 'number') {
        throw new Error('Invalid number in calculation');
      }

      if (operator === 'add') {
        finalResult += value;
      } else if (operator === 'subtract') {
        finalResult -= value;
      }
    }

    return Math.round(finalResult * 1e8) / 1e8;
  } catch (error) {
    console.error('Calculation error:', error);
    throw error;
  }
};

// Format number for display
const formatNumber = (num) => {
  if (typeof num !== 'number') return '0';
  return num.toLocaleString('en-US', {
    maximumFractionDigits: 8,
    minimumFractionDigits: 0
  });
};

// Handle percentage calculation
const handlePercentage = () => {
  triggerHapticFeedback('medium');
  if (!currentInput.value && calculationHistory.value.length === 0) return;

  try {
    let value = currentInput.value ? parseFloat(currentInput.value) : calculateResult(calculationHistory.value);
    value = value / 100;

    if (currentInput.value) {
      currentInput.value = value.toString();
    } else {
      calculationHistory.value = [value];
    }

    // Recalculate intermediate result
    if (calculationHistory.value.length > 0) {
      intermediateResult.value = calculateResult(calculationHistory.value);
    }
  } catch (error) {
    hasError.value = true;
    intermediateResult.value = null;
  }
};

// Add these new functions for better number handling
const formatInputNumber = (value) => {
  if (!value) return '';

  // Remove any non-digit characters except decimal point
  let cleanValue = value.replace(/[^\d.]/g, '');

  // Handle decimal point
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('');
  }

  // Format the whole number part with commas
  if (parts[0]) {
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  return parts.join('.');
};

const unformatNumber = (value) => {
  return value.replace(/,/g, '');
};

// Update the handleKeypadInput function for better number handling
const handleKeypadInput = (value) => {
  triggerHapticFeedback('light');
  hasError.value = false;

  if (lastResult.value !== null) {
    // Start new calculation if there's a last result
    calculationHistory.value = [];
    lastResult.value = null;
    isNewCalculation.value = true;
  }

  try {
    // Handle decimal point
    if (value === '.') {
      if (currentInput.value.includes('.')) return;
      if (currentInput.value === '') {
        currentInput.value = '0.';
        return;
      }
    }

    // Handle leading zeros
    if (currentInput.value === '0' && value !== '.') {
      currentInput.value = value;
    } else {
      // Limit decimal places to 8
      if (currentInput.value.includes('.')) {
        const decimalPlaces = currentInput.value.split('.')[1]?.length || 0;
        if (decimalPlaces >= 8) return;
      }

      // Get the unformatted value for length check
      const unformattedValue = unformatNumber(currentInput.value);
      // Limit total digits to 12
      if (unformattedValue.replace('.', '').length >= 12) return;

      // Add the new digit and format
      const newValue = unformattedValue + value;
      currentInput.value = formatInputNumber(newValue);
    }

    // Auto-calculate if we have a complete expression
    if (calculationHistory.value.length > 0 && currentInput.value) {
      const numbers = [...calculationHistory.value, parseFloat(unformatNumber(currentInput.value))];
      intermediateResult.value = calculateResult(numbers);
    }
  } catch (error) {
    console.error('Input handling error:', error);
    hasError.value = true;
    intermediateResult.value = null;
  }
};

// Update the setOperation function for better operator handling
const setOperation = (op) => {
  triggerHapticFeedback('medium');
  hasError.value = false;

  try {
    // Handle current input first
    if (currentInput.value) {
      const value = parseFloat(unformatNumber(currentInput.value));
      if (isNaN(value)) {
        throw new Error('Invalid number');
      }
      calculationHistory.value.push(value);
      currentInput.value = '';
    } else if (lastResult.value !== null) {
      // Use last result if available
      calculationHistory.value = [lastResult.value];
      lastResult.value = null;
    } else if (calculationHistory.value.length === 0) {
      // If no numbers yet, use 0 as first number
      calculationHistory.value = [0];
    }

    // Handle operation chaining
    if (calculationHistory.value.length > 0) {
      const lastItem = calculationHistory.value[calculationHistory.value.length - 1];
      if (typeof lastItem === 'string') {
        // Replace the last operation
        calculationHistory.value[calculationHistory.value.length - 1] = op;
      } else {
        // Add new operation
        calculationHistory.value.push(op);
      }
    }

    // Calculate intermediate result
    if (calculationHistory.value.length > 0) {
      const lastItem = calculationHistory.value[calculationHistory.value.length - 1];
      if (typeof lastItem === 'number') {
        // If last item is a number, calculate complete expression
        intermediateResult.value = calculateResult(calculationHistory.value);
      } else if (calculationHistory.value.length > 1) {
        // If last item is operator, show previous result
        const previousNumbers = calculationHistory.value.slice(0, -1);
        intermediateResult.value = calculateResult(previousNumbers);
      }
    }
  } catch (error) {
    console.error('Operation error:', error);
    hasError.value = true;
    intermediateResult.value = null;
  }
};

// Update the calculateExchange function for better final calculation
const calculateExchange = async () => {
  triggerHapticFeedback('heavy');
  hasError.value = false;
  isCalculating.value = true;
  intermediateResult.value = null;

  try {
    // Add a small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));

    if (currentInput.value) {
      // Add current input to calculation
      const value = parseFloat(unformatNumber(currentInput.value));
      if (isNaN(value)) {
        throw new Error('Invalid number');
      }
      calculationHistory.value.push(value);
      currentInput.value = '';
    }

    if (calculationHistory.value.length > 0) {
      // Calculate final result
      const result = calculateResult(calculationHistory.value);
      lastResult.value = result;
      calculationHistory.value = [result];

      // Show success notification
      if ($q.notify) {
        $q.notify({
          message: 'Calculation complete',
          color: 'positive',
          position: 'top',
          timeout: 1000,
          icon: 'check_circle'
        });
      }
    }
  } catch (error) {
    triggerHapticFeedback('error');
    hasError.value = true;
    calculationHistory.value = [];
    lastResult.value = null;

    // Show error notification
    if ($q.notify) {
      $q.notify({
        message: 'Calculation error',
        color: 'negative',
        position: 'top',
        timeout: 2000,
        icon: 'error'
      });
    }
  } finally {
    isCalculating.value = false;
  }
};

// Update the clearCalculator function for better reset
const clearCalculator = () => {
  triggerHapticFeedback('medium');
  calculationHistory.value = [];
  currentInput.value = '';
  lastResult.value = null;
  hasError.value = false;
  intermediateResult.value = null;
  isNewCalculation.value = true;

  // Show clear notification
  if ($q.notify) {
    $q.notify({
      message: 'Calculator cleared',
      color: 'info',
      position: 'top',
      timeout: 1000,
      icon: 'clear_all'
    });
  }
};

// Add a function to clear just the current input
const clearCurrentInput = () => {
  if (currentInput.value) {
    triggerHapticFeedback('light');
    currentInput.value = '';
  } else if (calculationHistory.value.length > 0) {
    // If no current input, clear the last operation
    handleBackspace();
  }
};

// Update the handleBackspace function for better deletion
const handleBackspace = () => {
  if (currentInput.value) {
    // Remove last digit from current input
    const unformattedValue = unformatNumber(currentInput.value);
    const newValue = unformattedValue.slice(0, -1);
    currentInput.value = formatInputNumber(newValue);

    // Recalculate if needed
    if (calculationHistory.value.length > 0) {
      try {
        const numbers = [...calculationHistory.value, parseFloat(unformatNumber(currentInput.value) || '0')];
        intermediateResult.value = calculateResult(numbers);
      } catch (error) {
        hasError.value = true;
        intermediateResult.value = null;
      }
    }
  } else if (calculationHistory.value.length > 0) {
    // Remove last operation or number
    calculationHistory.value.pop();
    try {
      if (calculationHistory.value.length > 0) {
        intermediateResult.value = calculateResult(calculationHistory.value);
      } else {
        intermediateResult.value = null;
      }
    } catch (error) {
      hasError.value = true;
      intermediateResult.value = null;
    }
  }
};

// Toggle calculator mode with state preservation
const toggleCalculatorMode = () => {
  triggerHapticFeedback('medium');

  const previousMode = calculatorMode.value;
  calculatorMode.value = calculatorMode.value === 'toIQD' ? 'fromIQD' : 'toIQD';

  // Clear any errors
  hasError.value = false;

  // Show mode change notification
  if ($q.notify) {
    const modeText = calculatorMode.value === 'toIQD'
      ? `${selectedCurrency.value?.currency_code} → IQD`
      : `IQD → ${selectedCurrency.value?.currency_code}`;

    $q.notify({
      message: `Mode: ${modeText}`,
      color: 'info',
      position: 'top',
      timeout: 1500,
      icon: 'swap_horiz'
    });
  }

  // Preserve current calculation and recalculate with new mode
  if (currentInput.value || calculationHistory.value.length > 0) {
    try {
      const currentValue = currentInput.value ? parseFloat(unformatNumber(currentInput.value)) : calculateResult(calculationHistory.value);
      if (isValidNumber(currentValue) && selectedCurrency.value) {
        if (calculatorMode.value === 'toIQD') {
          intermediateResult.value = currentValue * (selectedCurrency.value.sale || 0);
        } else {
          intermediateResult.value = currentValue / (selectedCurrency.value.buy || 1);
        }
      }
    } catch (error) {
      console.error('Mode toggle calculation error:', error);
      hasError.value = true;
      intermediateResult.value = null;
    }
  }
};

// Add these new computed properties after the existing ones
const modeSwitchLabel = computed(() => {
  if (!selectedCurrency.value?.currency_code) return '';
  return calculatorMode.value === 'toIQD'
    ? `${selectedCurrency.value.currency_code} → IQD`
    : `IQD → ${selectedCurrency.value.currency_code}`;
});

const modeSwitchIcon = computed(() => {
  return calculatorMode.value === 'toIQD' ? 'arrow_forward' : 'arrow_back';
});

const closeCalculator = () => {
  emit('update:show', false);
  emit('close');
};

// Copy result to clipboard
const copyResult = async () => {
  if (!exchangeValue.value || hasError.value || exchangeValue.value === '0.00') {
    triggerHapticFeedback('error');
    if ($q.notify) {
      $q.notify({
        message: 'No result to copy',
        color: 'warning',
        position: 'top',
        timeout: 1500,
        icon: 'warning'
      });
    }
    return;
  }

  // Clean the value for copying (remove formatting)
  const cleanValue = exchangeValue.value.replace(/,/g, '');

  try {
    await navigator.clipboard.writeText(cleanValue);
    triggerHapticFeedback('light');

    // Show success notification
    if ($q.notify) {
      $q.notify({
        message: `Copied: ${cleanValue}`,
        color: 'positive',
        position: 'top',
        timeout: 2000,
        icon: 'content_copy'
      });
    }
  } catch (error) {
    console.error('Failed to copy result:', error);

    // Fallback for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = cleanValue;
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';
      document.body.appendChild(textArea);
      textArea.select();
      textArea.setSelectionRange(0, 99999); // For mobile devices

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        triggerHapticFeedback('light');
        if ($q.notify) {
          $q.notify({
            message: `Copied: ${cleanValue}`,
            color: 'positive',
            position: 'top',
            timeout: 2000,
            icon: 'content_copy'
          });
        }
      } else {
        throw new Error('Copy command failed');
      }
    } catch (fallbackError) {
      triggerHapticFeedback('error');
      if ($q.notify) {
        $q.notify({
          message: 'Copy failed. Please copy manually.',
          color: 'negative',
          position: 'top',
          timeout: 2000,
          icon: 'error'
        });
      }
    }
  }
};

// Keyboard event handler
const handleKeyPress = (event) => {
  // Check if device is mobile
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  if (isMobile || !props.show) return;

  // Prevent default behavior for calculator keys
  if (/^[0-9.]$/.test(event.key) ||
      ['+', '-', '*', '/', 'Enter', 'Escape', 'Backspace'].includes(event.key)) {
    event.preventDefault();
  }

  if (/^[0-9.]$/.test(event.key)) {
    handleKeypadInput(event.key);
  } else {
    switch (event.key) {
      case '+':
        setOperation('add');
        break;
      case '-':
        setOperation('subtract');
        break;
      case '*':
        setOperation('multiply');
        break;
      case '/':
        setOperation('divide');
        break;
      case 'Enter':
        calculateExchange();
        break;
      case 'Escape':
        clearCalculator();
        break;
      case 'Backspace':
        handleBackspace();
        break;
    }
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeyPress);
});

// Watch for currency prop changes
watch(() => props.currency, (newCurrency) => {
  if (newCurrency) {
    selectedCurrency.value = newCurrency;
  }
}, { immediate: true });

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyPress);
});

// Add these new refs and computed properties
const selectedCurrency = ref(props.currency);

// Helper function to get flag path
const getFlagPath = (flag) => {
  // For Kurdistan, we use the PNG format
  if (flag === 'kurdistan') {
    return `/flags/${flag}.png`;
  }
  // For other flags, use SVG format
  return `/flags/${flag}.svg`;
};

const currencyOptions = computed(() => {
  if (!props.currencies || !Array.isArray(props.currencies)) {
    return [];
  }
  return props.currencies.map(curr => ({
    label: curr?.currency_code || '',
    value: curr || {},
    icon: 'attach_money',
    name: curr?.name || curr?.currency_name || curr?.currency_code || '',
    flag: curr?.code || 'us', // Use the country code for flag
    flagPath: getFlagPath(curr?.code || 'us')
  }));
});



const changeCurrency = (newCurrency) => {
  if (!newCurrency || !newCurrency.currency_code) return;

  // Don't change if it's the same currency
  if (selectedCurrency.value?.currency_code === newCurrency.currency_code) return;

  selectedCurrency.value = newCurrency;

  // Emit the currency change to parent component
  emit('currency-changed', newCurrency);

  // Trigger haptic feedback
  triggerHapticFeedback('medium');

  // Clear any errors
  hasError.value = false;

  // Show currency change notification
  if ($q.notify) {
    $q.notify({
      message: `Currency changed to ${newCurrency.currency_code}`,
      color: 'primary',
      position: 'top',
      timeout: 1500,
      icon: 'currency_exchange'
    });
  }

  // Recalculate if there's a current value
  if (currentInput.value || calculationHistory.value.length > 0) {
    try {
      const currentValue = currentInput.value ? parseFloat(unformatNumber(currentInput.value)) : calculateResult(calculationHistory.value);
      if (isValidNumber(currentValue)) {
        if (calculatorMode.value === 'toIQD') {
          intermediateResult.value = currentValue * (newCurrency.sale || 0);
        } else {
          intermediateResult.value = currentValue / (newCurrency.buy || 1);
        }
      }
    } catch (error) {
      console.error('Currency change calculation error:', error);
      hasError.value = true;
      intermediateResult.value = null;
    }
  }
};

// Update the displayExpression computed property
const displayExpression = computed(() => {
  if (calculationHistory.value.length === 0) {
    return currentInput.value || '0';
  }

  let expression = calculationHistory.value.map(item => {
    if (typeof item === 'number') {
      // Format numbers with commas and up to 8 decimal places
      const formattedNumber = formatInputNumber(item.toFixed(8).replace(/\.?0+$/, ''));
      return formattedNumber;
    }
    return {
      'add': ' + ',
      'subtract': ' - ',
      'multiply': ' × ',
      'divide': ' ÷ '
    }[item];
  }).join('');

  if (currentInput.value) {
    expression += ' ' + currentInput.value;
  }

  return expression;
});

// Update the exchangeValue computed property to handle operators correctly
const exchangeValue = computed(() => {
  if (hasError.value) return '0.00';
  if (!currentInput.value && calculationHistory.value.length === 0) return '0.00';

  try {
    let value;
    // Check if the last item is an operator
    const lastItem = calculationHistory.value[calculationHistory.value.length - 1];
    const isLastItemOperator = typeof lastItem === 'string';

    if (currentInput.value) {
      // If there's current input, calculate with it
      const numbers = [...calculationHistory.value, parseFloat(unformatNumber(currentInput.value))];
      value = calculateResult(numbers);
    } else if (calculationHistory.value.length > 0) {
      if (isLastItemOperator) {
        // If last item is operator, use the previous result
        const previousNumbers = calculationHistory.value.slice(0, -1);
        value = calculateResult(previousNumbers);
      } else {
        // If no operator at end, use complete calculation
        value = calculateResult(calculationHistory.value);
      }
    } else {
      return '0.00';
    }

    if (isNaN(value)) {
      throw new Error('Invalid calculation result');
    }

    // Calculate exchange value with proper rounding
    let exchangeResult;
    if (calculatorMode.value === 'toIQD') {
      exchangeResult = value * (selectedCurrency.value?.sale || 0);
    } else {
      if (!selectedCurrency.value?.buy || selectedCurrency.value.buy === 0) {
        throw new Error('Invalid exchange rate');
      }
      exchangeResult = value / selectedCurrency.value.buy;
    }

    // Format with proper decimal places and handle very small numbers
    if (Math.abs(exchangeResult) < 0.00000001) {
      return '0.00';
    }

    // Round to 2 decimal places for currency display
    return props.formatCurrency(Math.round(exchangeResult * 100) / 100);
  } catch (error) {
    console.error('Exchange calculation error:', error);
    return '0.00';
  }
});

// Add new computed property for calculation result
const calculationResult = computed(() => {
  if (hasError.value) return '0.00';
  if (!currentInput.value && calculationHistory.value.length === 0) return '0.00';

  try {
    let value;
    // Check if the last item is an operator
    const lastItem = calculationHistory.value[calculationHistory.value.length - 1];
    const isLastItemOperator = typeof lastItem === 'string';

    if (currentInput.value) {
      value = parseFloat(unformatNumber(currentInput.value));
      if (isNaN(value)) {
        throw new Error('Invalid input value');
      }
    } else if (calculationHistory.value.length > 0 && !isLastItemOperator) {
      value = calculateResult(calculationHistory.value);
    } else {
      // If last item is operator, show previous result
      if (isLastItemOperator && calculationHistory.value.length > 1) {
        const previousNumbers = calculationHistory.value.slice(0, -1);
        value = calculateResult(previousNumbers);
      } else {
        return '0.00';
      }
    }

    return formatInputNumber(value.toFixed(8).replace(/\.?0+$/, ''));
  } catch (error) {
    console.error('Calculation result error:', error);
    return '0.00';
  }
});
</script>

<template>
  <q-dialog
    :model-value="show"
    @update:model-value="$emit('update:show', $event)"
    persistent
    :transition-show="$q.screen.lt.sm ? 'slide-up' : 'scale'"
    :transition-hide="$q.screen.lt.sm ? 'slide-down' : 'scale'"
    :maximized="$q.screen.lt.sm"
    :full-width="$q.screen.lt.sm"
    :full-height="$q.screen.lt.sm"
  >
    <q-card
      :style="$q.screen.lt.sm ? 'min-width: 100vw; max-width: 100vw; height: 100vh; border-radius: 0;' : 'min-width: 350px; max-width: 90vw; border-radius: 16px;'"
      :class="{ 'dark-theme': darkMode, 'mobile-fullscreen': $q.screen.lt.sm }"
      class="calculator-card"
    >
      <!-- Header -->
      <q-card-section class="header-section">
        <div class="header-top">
          <div class="calculator-title">
            <q-icon name="calculate" size="24px" class="q-mr-sm" />
            <span class="text-h6">{{ t.calculate }}</span>
          </div>
          <div class="header-actions">
            <q-btn
              flat
              round
              dense
              icon="clear_all"
              @click="clearCalculator"
              class="clear-all-btn"
              :disable="!currentInput && calculationHistory.length === 0"
            >
              <q-tooltip>{{ t.clearAll || 'Clear All' }}</q-tooltip>
            </q-btn>
            <q-btn
              flat
              round
              dense
              icon="swap_horiz"
              @click="toggleCalculatorMode"
              class="mode-toggle-btn"
              :color="calculatorMode === 'toIQD' ? 'primary' : 'secondary'"
            >
              <q-tooltip>{{ t.toggleMode }}</q-tooltip>
            </q-btn>
            <q-btn flat round dense icon="close" @click="closeCalculator" />
          </div>
        </div>

        <!-- Currency Selection Row -->
        <div class="currency-selection-row">
          <div class="currency-chips-container">
            <q-chip
              v-for="currency in currencyOptions"
              :key="currency.value.currency_code"
              :selected="selectedCurrency.value?.currency_code === currency.value.currency_code"
              clickable
              @click="changeCurrency(currency.value)"
              :color="selectedCurrency.value?.currency_code === currency.value.currency_code ? 'primary' : 'grey-4'"
              :text-color="selectedCurrency.value?.currency_code === currency.value.currency_code ? 'white' : 'dark'"
              class="currency-chip"
            >
              <q-avatar size="20px" class="flag-avatar-small">
                <img :src="currency.flagPath" :alt="currency.flag" />
              </q-avatar>
              <span class="currency-chip-text">{{ currency.label }}</span>

              <q-tooltip class="currency-tooltip" :delay="500">
                <div class="tooltip-content">
                  <div class="tooltip-title">{{ currency.name }}</div>
                  <div class="tooltip-rates">
                    <div>{{ t.buy }}: {{ props.formatCurrency(currency.value?.buy || 0) }}</div>
                    <div>{{ t.sale }}: {{ props.formatCurrency(currency.value?.sale || 0) }}</div>
                  </div>
                </div>
              </q-tooltip>
            </q-chip>
          </div>
        </div>

        <!-- Exchange Rate Info -->
        <div class="exchange-rate-info">
          <div class="rate-display">
            <span class="rate-text">
              {{ calculatorMode === 'toIQD' ?
                `1 ${selectedCurrency.value?.currency_code || ''} = ${props.formatCurrency(selectedCurrency.value?.sale || 0)} IQD` :
                `1 IQD = ${props.formatCurrency(selectedCurrency.value?.buy ? 1/selectedCurrency.value.buy : 0)} ${selectedCurrency.value?.currency_code || ''}`
              }}
            </span>
            <q-icon
              :name="calculatorMode === 'toIQD' ? 'trending_up' : 'trending_down'"
              size="16px"
              class="q-ml-xs"
              :color="calculatorMode === 'toIQD' ? 'positive' : 'info'"
            />
          </div>

          <!-- Keyboard shortcuts info for desktop -->
          <div class="keyboard-shortcuts" v-if="$q.screen.gt.sm">
            <q-icon name="keyboard" size="12px" class="q-mr-xs" />
            <span class="shortcuts-text">Use keyboard: 0-9, +, -, *, /, Enter, Esc</span>
          </div>
        </div>
      </q-card-section>

      <q-separator :dark="darkMode" />

      <!-- Main Content -->
      <q-card-section class="calculator-section">
        <!-- Input Section -->
        <div class="input-section">
          <div class="input-header">
            <div class="input-label">
              <q-icon name="input" size="18px" class="q-mr-xs" />
              <span>{{ t.amount }} ({{ calculatorMode === 'toIQD' ? selectedCurrency.value?.currency_code : 'IQD' }})</span>
            </div>
            <q-btn
              flat
              dense
              size="sm"
              icon="backspace"
              @click="handleBackspace"
              class="backspace-btn"
              :disable="!currentInput && calculationHistory.length === 0"
            >
              <q-tooltip>{{ t.clear }}</q-tooltip>
            </q-btn>
          </div>
          <div class="input-display">
            <q-card flat class="input-card" :class="{ 'has-error': hasError }">
              <q-card-section class="input-content">
                <div class="expression-display">
                  {{ displayExpression || '0' }}
                </div>
                <div class="currency-badge">
                  <div v-if="selectedCurrency.value?.code" class="flag-avatar-tiny">
                    <CountryFlag
                      :code="calculatorMode === 'toIQD' ? selectedCurrency.value?.currency_code || selectedCurrency.value?.code : 'IQD'"
                      :country="calculatorMode === 'toIQD' ? selectedCurrency.value?.name : 'Iraq'"
                      :flag-icon="calculatorMode === 'toIQD' ? selectedCurrency.value?.flag_icon : 'iq'"
                      :flag-country-code="calculatorMode === 'toIQD' ? selectedCurrency.value?.flag_country_code : 'iq'"
                      size="small"
                    />
                  </div>
                  <span class="currency-text">
                    {{ calculatorMode === 'toIQD' ? selectedCurrency.value?.currency_code : 'IQD' }}
                  </span>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Result Section -->
        <div class="result-section" :class="{ 'has-error': hasError }">
          <div class="result-header">
            <div class="result-label">
              <q-icon name="trending_up" size="18px" class="q-mr-xs" />
              <span>{{ t.result }} ({{ calculatorMode === 'toIQD' ? 'IQD' : selectedCurrency.value?.currency_code }})</span>
            </div>
            <q-btn
              flat
              dense
              size="sm"
              icon="content_copy"
              @click="copyResult"
              class="copy-btn"
              :disable="!exchangeValue || hasError"
            >
              <q-tooltip>Copy Result</q-tooltip>
            </q-btn>
          </div>
          <div class="result-display">
            <q-card flat class="result-card" :class="{ 'has-error': hasError, 'calculating': isCalculating }">
              <q-card-section class="result-content">
                <div class="result-value" :class="{
                  'has-result': exchangeValue && !hasError,
                  'intermediate': intermediateResult !== null,
                  'final': lastResult !== null,
                  'calculating': isCalculating
                }">
                  {{ isCalculating ? 'Calculating...' : (exchangeValue || '0.00') }}
                </div>
                <div class="result-currency-badge" v-if="exchangeValue && !hasError">
                  <div class="flag-avatar-tiny">
                    <CountryFlag
                      :code="calculatorMode === 'toIQD' ? 'IQD' : selectedCurrency.value?.currency_code || selectedCurrency.value?.code"
                      :country="calculatorMode === 'toIQD' ? 'Iraq' : selectedCurrency.value?.name"
                      :flag-icon="calculatorMode === 'toIQD' ? 'iq' : selectedCurrency.value?.flag_icon"
                      :flag-country-code="calculatorMode === 'toIQD' ? 'iq' : selectedCurrency.value?.flag_country_code"
                      size="small"
                    />
                  </div>
                  <span class="currency-text">
                    {{ calculatorMode === 'toIQD' ? 'IQD' : selectedCurrency.value?.currency_code }}
                  </span>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- Calculator Keypad -->
        <div class="calculator-keypad">
          <!-- Number Rows -->
          <div class="keypad-row">
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('7')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">7</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('8')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">8</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('9')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">9</span>
            </q-btn>
            <q-btn
              :class="['keypad-btn', 'operation-btn']"
              @click="setOperation('divide')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">÷</span>
            </q-btn>
          </div>
          <div class="keypad-row">
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('4')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">4</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('5')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">5</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('6')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">6</span>
            </q-btn>
            <q-btn
              :class="['keypad-btn', 'operation-btn']"
              @click="setOperation('multiply')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">
                <q-icon name="close" />
              </span>
            </q-btn>
          </div>
          <div class="keypad-row">
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('1')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">1</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('2')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">2</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('3')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">3</span>
            </q-btn>
            <q-btn
              :class="['keypad-btn', 'operation-btn']"
              @click="setOperation('subtract')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">
                <q-icon name="remove" />
              </span>
            </q-btn>
          </div>
          <div class="keypad-row">
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('0')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">0</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handleKeypadInput('.')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">.</span>
            </q-btn>
            <q-btn
              class="keypad-btn"
              @click="handlePercentage"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">%</span>
            </q-btn>
            <q-btn
              :class="['keypad-btn', 'operation-btn']"
              @click="setOperation('add')"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">
                <q-icon name="add" />
              </span>
            </q-btn>
          </div>
          <div class="keypad-row">
            <q-btn
              :class="['keypad-btn', 'mode-btn']"
              @click="toggleCalculatorMode"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content mode-switch-content">
                <q-icon :name="modeSwitchIcon" class="mode-icon" />
                <span class="mode-label">{{ modeSwitchLabel }}</span>
              </span>
            </q-btn>
            <q-btn
              :class="['keypad-btn', 'clear-btn']"
              @click="clearCalculator"
              :ripple="$q.screen.gt.xs"
              no-caps
              unelevated
            >
              <span class="btn-content">
                <q-icon name="clear_all" />
                <span class="clear-label">{{ t.clear }}</span>
              </span>
            </q-btn>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<style scoped>
.calculator-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.dark-theme.calculator-card {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.header-section {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.dark-theme .header-section {
  border-bottom-color: rgba(255, 255, 255, 0.08);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.calculator-title {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 6px;
  align-items: center;
}

.mode-toggle-btn, .clear-all-btn {
  transition: all 0.2s ease;
}

.clear-all-btn {
  opacity: 0.7;
}

.clear-all-btn:not(.disabled):hover {
  opacity: 1;
  color: var(--negative);
}

.clear-all-btn.disabled {
  opacity: 0.3;
}

.currency-selection-row {
  margin-bottom: 12px;
}

.currency-chips-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.currency-chip {
  transition: all 0.2s ease;
  border-radius: 20px !important;
  font-weight: 500;
}

.currency-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-theme .currency-chip:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.currency-chip-text {
  margin-left: 6px;
  font-size: 0.85rem;
  font-weight: 600;
}

.flag-avatar-small {
  border-radius: 4px;
  overflow: hidden;
}

.flag-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.exchange-rate-info {
  text-align: center;
}

.rate-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-theme .rate-display {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.rate-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.keyboard-shortcuts {
  margin-top: 8px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.keyboard-shortcuts:hover {
  opacity: 1;
}

.shortcuts-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.calculator-section {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.input-section, .result-section {
  margin-bottom: 20px;
}

.input-header, .result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.input-label, .result-label {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.backspace-btn, .copy-btn {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.backspace-btn:hover, .copy-btn:hover {
  opacity: 1;
}

.input-card, .result-card {
  border-radius: 16px;
  border: 2px solid rgba(0, 0, 0, 0.08);
  background: var(--bg-secondary);
  transition: all 0.2s ease;
}

.dark-theme .input-card,
.dark-theme .result-card {
  border-color: rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.03);
}

.input-card.has-error,
.result-card.has-error {
  border-color: var(--negative);
  background: rgba(var(--negative-rgb), 0.05);
}

.input-content, .result-content {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.expression-display {
  font-size: 1.8rem;
  font-weight: 600;
  font-family: 'SF Mono', 'Roboto Mono', monospace;
  color: var(--text-primary);
  letter-spacing: -0.5px;
  min-height: 2.2rem;
  display: flex;
  align-items: center;
}

.result-value {
  font-size: 2rem;
  font-weight: 700;
  font-family: 'SF Mono', 'Roboto Mono', monospace;
  color: var(--primary-color);
  letter-spacing: -0.5px;
  min-height: 2.4rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.result-value.has-result {
  color: var(--primary-color);
  animation: resultPulse 0.4s ease;
}

.result-value.intermediate {
  color: #3498db;
  opacity: 0.9;
}

.result-value.final {
  color: var(--primary-color);
  animation: resultPop 0.4s ease;
}

.currency-badge, .result-currency-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.dark-theme .currency-badge,
.dark-theme .result-currency-badge {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
}

.flag-avatar-tiny {
  border-radius: 3px;
  overflow: hidden;
}

.flag-avatar-tiny img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.currency-text {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.currency-tooltip {
  max-width: 200px;
}

.tooltip-content {
  text-align: center;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 0.9rem;
}

.tooltip-rates {
  font-size: 0.8rem;
  opacity: 0.9;
}

.tooltip-rates div {
  margin: 2px 0;
}

/* Enhanced currency chip animations */
.currency-chip {
  position: relative;
  overflow: hidden;
}

.currency-chip::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.currency-chip:hover::before {
  left: 100%;
}

/* Improved error states */
.has-error {
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* Loading state for calculations */
.calculating {
  position: relative;
}

.calculating::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 10px;
  width: 16px;
  height: 16px;
  border: 2px solid var(--primary-color);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translateY(-50%);
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

@keyframes resultPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes resultPop {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}



.calculator-keypad {
  margin-top: 24px;
}

.keypad-row {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.keypad-btn {
  flex: 1;
  height: 60px;
  border-radius: 12px !important;
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease !important;
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow;
}

.keypad-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.keypad-btn:active::after {
  opacity: 1;
}

.dark-theme .keypad-btn {
  background-color: rgba(255, 255, 255, 0.07) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.keypad-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) !important;
}

.dark-theme .keypad-btn:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.keypad-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.operation-btn {
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-light)) !important;
  color: white !important;
}

.dark-theme .operation-btn {
  background: linear-gradient(to bottom, var(--primary-light), var(--primary-color)) !important;
}

.clear-btn, .mode-btn {
  flex: 1;
  height: 50px;
  margin-top: 12px;
  background-color: rgba(0, 0, 0, 0.05) !important;
}

.dark-theme .clear-btn,
.dark-theme .mode-btn {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.mode-switch-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-icon {
  font-size: 1.2rem;
}

.mode-label {
  font-size: 0.85rem;
}

.clear-label {
  margin-left: 6px;
  font-size: 0.9rem;
}



/* Mobile Optimizations */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
  .calculator-card {
    max-width: 95vw;
    margin: 10px;
  }

  .header-section {
    padding: 16px 20px;
  }

  .calculator-section {
    padding: 20px;
  }
}

/* Tablets */
@media (max-width: 768px) {
  .calculator-card {
    max-width: 98vw;
    margin: 5px;
    border-radius: 12px;
  }

  .header-section {
    padding: 14px 18px;
  }

  .calculator-section {
    padding: 18px;
  }

  .currency-input :deep(.q-field__control) {
    height: 68px;
    padding: 0 18px;
    border-radius: 16px;
  }

  .currency-input :deep(.q-field__native) {
    font-size: 26px;
  }

  .result-value {
    font-size: 30px;
  }

  .keypad-btn {
    height: 58px;
    font-size: 1.15rem;
  }

  .currency-selector {
    max-height: 280px;
  }
}

/* Mobile phones - landscape and large screens */
@media (max-width: 600px) {
  .calculator-card {
    min-width: 100vw;
    max-width: 100vw;
    max-height: 100vh;
    margin: 0;
    border-radius: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .header-section {
    padding: 12px 16px;
    position: sticky;
    top: 0;
    z-index: 10;
    background: inherit;
    backdrop-filter: blur(10px);
  }

  .header-top {
    margin-bottom: 12px;
  }

  .currency-selection-row {
    margin-bottom: 8px;
  }

  .currency-chips-container {
    gap: 4px;
    justify-content: flex-start;
    overflow-x: auto;
    padding: 2px 0;
    -webkit-overflow-scrolling: touch;
  }

  .currency-chip {
    font-size: 0.75rem;
    flex-shrink: 0;
    min-width: fit-content;
  }

  .currency-chip-text {
    font-size: 0.7rem;
    margin-left: 4px;
  }

  .flag-avatar-small {
    width: 16px;
    height: 16px;
  }

  .header-actions {
    gap: 4px;
  }

  .header-actions .q-btn {
    min-width: 36px;
    min-height: 36px;
  }

  .rate-display {
    padding: 6px 10px;
  }

  .rate-text {
    font-size: 0.8rem;
  }

  .calculator-section {
    padding: 12px 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .input-section, .result-section {
    margin-bottom: 16px;
  }

  .expression-display {
    font-size: 1.5rem;
  }

  .result-value {
    font-size: 1.7rem;
  }

  .input-content, .result-content {
    padding: 12px 16px;
  }



  .calculator-keypad {
    margin-top: auto;
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  .keypad-row {
    gap: 8px;
    margin-bottom: 8px;
  }

  .keypad-btn {
    height: 56px;
    border-radius: 12px !important;
    font-size: 1.1rem;
    font-weight: 600;
    min-height: 56px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .clear-btn, .mode-btn {
    height: 48px;
    margin-top: 8px;
  }



  /* Better tap feedback on mobile */
  @media (hover: none) {
    .keypad-btn:hover {
      transform: none;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
    }

    .dark-theme .keypad-btn:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    }

    .keypad-btn:active {
      transform: scale(0.96);
      transition: transform 0.1s ease;
    }

    .currency-item:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }
}

/* Small mobile phones - portrait */
@media (max-width: 400px) {
  .header-section {
    padding: 10px 14px;
  }

  .calculator-section {
    padding: 14px;
  }

  .currency-input :deep(.q-field__control) {
    height: 60px;
    padding: 0 14px;
    border-radius: 12px;
  }

  .currency-input :deep(.q-field__native) {
    font-size: 22px;
  }

  .result-section {
    padding: 12px;
    margin: 14px 0 16px 0;
  }

  .result-value {
    font-size: 24px;
  }

  .result-value.final {
    font-size: 28px;
  }

  .keypad-row {
    gap: 6px;
    margin-bottom: 6px;
  }

  .keypad-btn {
    height: 52px;
    border-radius: 10px !important;
    font-size: 1rem;
    min-height: 52px;
  }

  .clear-btn, .mode-btn {
    height: 44px;
    margin-top: 6px;
  }

  .currency-label {
    font-size: 12px;
  }

  .exchange-rate {
    font-size: 0.7rem;
  }

  .clear-label, .mode-label {
    font-size: 0.75rem;
  }

  .mode-icon {
    font-size: 1rem;
  }

  .currency-selector {
    max-height: 220px;
    padding: 10px 14px;
  }

  .currency-item {
    padding: 10px 6px;
  }

  .currency-code {
    font-size: 0.9rem;
  }

  .currency-name {
    font-size: 0.8rem;
  }

  .currency-rates {
    font-size: 0.7rem;
  }

  .calculation-result {
    padding: 2px 8px;
    border-radius: 10px;
  }

  .calculation-result .result-value {
    font-size: 12px;
  }
}

/* Extra small screens */
@media (max-width: 320px) {
  .header-section {
    padding: 8px 12px;
  }

  .calculator-section {
    padding: 12px;
  }

  .currency-input :deep(.q-field__control) {
    height: 56px;
    padding: 0 12px;
  }

  .currency-input :deep(.q-field__native) {
    font-size: 20px;
  }

  .result-value {
    font-size: 22px;
  }

  .result-value.final {
    font-size: 26px;
  }

  .keypad-btn {
    height: 48px;
    font-size: 0.95rem;
    min-height: 48px;
  }

  .clear-btn, .mode-btn {
    height: 40px;
  }

  .text-h6 {
    font-size: 1.1rem !important;
  }

  .currency-selector {
    max-height: 200px;
  }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .calculator-card {
    max-height: 100vh;
  }

  .header-section {
    padding: 8px 16px;
  }

  .calculator-section {
    padding: 12px 16px;
  }

  .amount-section {
    margin-bottom: 12px;
  }

  .result-section {
    margin: 12px 0;
    padding: 10px;
  }

  .calculator-keypad {
    margin-top: 8px;
  }

  .keypad-row {
    margin-bottom: 6px;
  }

  .keypad-btn {
    height: 44px;
    font-size: 1rem;
  }

  .clear-btn, .mode-btn {
    height: 38px;
    margin-top: 4px;
  }

  .currency-selector {
    max-height: 180px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .keypad-btn {
    border: 0.5px solid rgba(0, 0, 0, 0.05);
  }

  .dark-theme .keypad-btn {
    border-color: rgba(255, 255, 255, 0.05);
  }
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
  .keypad-btn,
  .currency-item,
  .result-value {
    transition: none !important;
    animation: none !important;
  }
}

/* Focus improvements for keyboard navigation on mobile */
.keypad-btn:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Improve scrolling on mobile */
.calculator-section {
  scroll-behavior: smooth;
}

.currency-selector {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* Mobile-specific enhancements */
.mobile-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

/* Prevent text selection on mobile */
@media (max-width: 600px) {
  .calculator-card * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Allow text selection only for input fields */
  .q-field__native,
  .q-field__input {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Improve button press feedback */
  .keypad-btn {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  .dark-theme .keypad-btn {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0.1);
    tap-highlight-color: rgba(255, 255, 255, 0.1);
  }

  /* Prevent zoom on input focus */
  .q-field__native {
    font-size: 16px !important;
  }

  /* Improve scrolling performance */
  .calculator-section,
  .currency-selector {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: scroll-position;
  }

  /* Safe area handling for newer iPhones */
  .calculator-card {
    padding-top: env(safe-area-inset-top, 0);
    padding-bottom: env(safe-area-inset-bottom, 0);
    padding-left: env(safe-area-inset-left, 0);
    padding-right: env(safe-area-inset-right, 0);
  }

  .header-section {
    padding-top: calc(12px + env(safe-area-inset-top, 0));
  }

  .calculator-keypad {
    padding-bottom: calc(env(safe-area-inset-bottom, 0) + 8px);
  }
}

/* PWA and standalone app optimizations */
@media (display-mode: standalone) {
  .calculator-card {
    border-radius: 0 !important;
  }

  .header-section {
    padding-top: calc(16px + env(safe-area-inset-top, 0));
  }
}

/* Improve performance on low-end devices */
@media (max-width: 600px) and (max-height: 800px) {
  .keypad-btn,
  .currency-item,
  .result-value {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

/* Dark mode mobile optimizations */
.dark-theme.mobile-fullscreen {
  background: var(--bg-primary) !important;
}

/* Improve contrast on mobile */
@media (max-width: 600px) {
  .currency-input :deep(.q-field__control) {
    border-width: 1.5px;
  }

  .result-section {
    border-width: 1.5px;
  }

  .keypad-btn {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12) !important;
  }

  .dark-theme .keypad-btn {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25) !important;
  }
}

/* Optimize for foldable devices */
@media (max-width: 600px) and (min-aspect-ratio: 2/1) {
  .calculator-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    align-items: start;
  }

  .amount-section,
  .result-section {
    grid-column: 1 / -1;
  }

  .calculator-keypad {
    grid-column: 1 / -1;
    margin-top: 16px;
  }
}

.currency-input.has-error :deep(.q-field__control) {
  border-color: var(--negative) !important;
  background: rgba(var(--negative-rgb), 0.05) !important;
}

.exchange-rate.error {
  color: var(--negative) !important;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.has-error {
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

.calculation-result {
  text-align: right;
  font-size: 12px;
  display: flex;
  align-items: center;
  opacity: 0.9;
  background: rgba(0, 0, 0, 0.02);
  padding: 3px 10px;
  border-radius: 14px;
  font-family: 'SF Mono', 'Roboto Mono', monospace;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.dark-theme .calculation-result {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(255, 255, 255, 0.08);
}

.calculation-result .result-value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
  letter-spacing: -0.3px;
  line-height: 1.2;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.currency-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 0;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0.9;
}
</style>
