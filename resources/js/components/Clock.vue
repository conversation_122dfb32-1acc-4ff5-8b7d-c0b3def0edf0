<script setup>
import { defineProps, ref, onMounted, onUnmounted, computed } from 'vue';

const props = defineProps({
  digitalTime: {
    type: String,
    required: true
  },
  currentDate: {
    type: String,
    required: true
  },
  currentLang: {
    type: String,
    default: 'en'
  }
});

const time = ref(props.digitalTime);
const date = ref(props.currentDate);
const seconds = ref(new Date().getSeconds());
let timer = null;

// Map for language-specific options
const langOptions = {
  en: { weekday: 'short', day: 'numeric', month: 'short' },
  ku: { weekday: 'short', day: 'numeric', month: 'short' },
  ar: { weekday: 'short', day: 'numeric', month: 'short' }
};

const isRTL = computed(() => {
  return props.currentLang === 'ku' || props.currentLang === 'ar';
});

const updateTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  seconds.value = now.getSeconds();

  time.value = `${hours}:${minutes}`;

  // Get language-specific options
  const options = langOptions[props.currentLang] || langOptions.en;

  // Get day name in the current language
  const locale = props.currentLang === 'ku' ? 'ckb' : props.currentLang;

  try {
    // Format date according to locale
    date.value = now.toLocaleDateString(locale, options);
  } catch (e) {
    // Fallback to English if locale not supported
    date.value = now.toLocaleDateString('en', options);
  }
};

onMounted(() => {
  updateTime();
  timer = setInterval(updateTime, 1000); // Update every second
});

onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>

<template>
  <div class="flex items-center justify-center full-width">
    <div class="flex items-center full-width q-pa-sm rounded-borders clock-container"
         :class="{'row-reverse': isRTL}">
      <q-icon name="schedule" class="text-primary q-opacity-8 pulse-icon clock-icon" size="md" />
      <div class="q-mx-sm clock-text-container">
        <div class="row no-wrap items-center">
          <div class="text-weight-bold text-primary no-wrap time-display">
            {{ time.split(':')[0] }}
            <span class="seconds-indicator text-primary q-opacity-6" :class="{'seconds-indicator-animate': seconds % 2 === 0}">:</span>
            {{ time.split(':')[1] }}
          </div>
        </div>
        <div class="text-weight-medium text-grey-8 no-wrap ellipsis date-display"
             :class="{'rtl-font': isRTL}">
          {{ date }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.clock-container {
  transition: all 0.3s ease;
  overflow: hidden;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 140px;
  padding: 12px !important;
}

.clock-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.3) 100%);
}

.time-display {
  position: relative;
  transition: all 0.3s ease;
  font-family: 'Roboto Mono', monospace;
  letter-spacing: 1px;
  font-size: 1.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(to right, #3366ff, #00ccff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.date-display {
  transition: all 0.3s ease;
  opacity: 0.9;
  font-size: 1.1rem;
  color: #334155;
  font-weight: 500;
  margin-top: 2px;
}

.clock-container:hover .time-display {
  transform: scale(1.05);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.clock-container:hover .date-display {
  opacity: 1;
  color: #1e293b;
}

.seconds-indicator {
  display: inline-block;
  font-size: 1.3rem;
  transition: opacity 0.3s ease;
  animation: blink 1s infinite;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(51, 102, 255, 0.5);
}

.seconds-indicator-animate {
  opacity: 1;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.seconds-display {
  display: none; /* Hide seconds display completely */
}

.clock-container:hover .seconds-display {
  opacity: 0.9;
}

.pulse-icon {
  transition: all 0.3s ease;
  color: #3366ff;
  margin-right: 8px;
  margin-left: 4px;
  font-size: 1.8rem !important;
}

.clock-container:hover .pulse-icon {
  animation: pulse 2s infinite;
  color: #0055ff;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* RTL support */
.rtl-font {
  font-family: 'Vazirmatn', 'Tajawal', sans-serif;
}

:root[class*="dark"] .clock-container,
:root.dark-theme .clock-container {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.08) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

:root[class*="dark"] .clock-container:hover,
:root.dark-theme .clock-container:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(14, 165, 233, 0.1) 100%);
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.25);
}

:root[class*="dark"] .time-display,
:root.dark-theme .time-display {
  background: linear-gradient(to right, #60a5fa, #93c5fd);
  -webkit-background-clip: text;
  background-clip: text;
  text-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

:root[class*="dark"] .date-display,
:root.dark-theme .date-display {
  color: #94a3b8;
}

:root[class*="dark"] .clock-container:hover .date-display,
:root.dark-theme .clock-container:hover .date-display {
  color: #cbd5e1;
}

:root[class*="dark"] .pulse-icon,
:root.dark-theme .pulse-icon {
  color: #60a5fa;
}

:root[class*="dark"] .clock-container:hover .pulse-icon,
:root.dark-theme .clock-container:hover .pulse-icon {
  color: #93c5fd;
}

@media (max-width: 768px) {
  .time-display {
    font-size: 1.3rem !important;
  }

  .date-display {
    font-size: 1rem !important;
  }
}

@media (max-width: 600px) {
  .clock-container {
    padding: 10px !important;
  }

  .clock-icon {
    font-size: 1.4rem !important;
  }

  .time-display {
    font-size: 1.2rem !important;
  }

  .date-display {
    font-size: 0.95rem !important;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .clock-text-container {
    margin-left: 6px !important;
    margin-right: 6px !important;
  }
}

@media (max-width: 480px) {
  .time-display {
    font-size: 1.1rem !important;
    letter-spacing: 0.5px !important;
  }

  .date-display {
    font-size: 0.9rem !important;
    max-width: 120px;
  }
}

@media (max-width: 360px) {
  .time-display {
    font-size: 0.75rem !important;
  }

  .date-display {
    font-size: 0.65rem !important;
    max-width: 80px;
  }

  .clock-icon {
    font-size: 0.9rem !important;
  }
}

/* Touch-friendly improvements */
@media (hover: none) {
  .clock-container:active {
    transform: scale(0.98);
  }
}

/* Landscape mode optimization */
@media (max-height: 500px) and (orientation: landscape) {
  .time-display {
    font-size: 0.8rem !important;
  }

  .date-display {
    font-size: 0.7rem !important;
  }

  .clock-container {
    padding: 6px !important;
  }
}
</style>
