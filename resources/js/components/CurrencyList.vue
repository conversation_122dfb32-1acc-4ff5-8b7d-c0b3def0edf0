<script setup>
import { defineProps, ref, computed } from 'vue';
import CountryFlag from '../components/CountryFlag.vue'; // Import CountryFlag component

const props = defineProps({
    currencies: {
        type: Array,
        required: true
    },
    t: {
        type: Object,
        required: true
    },
    formatCurrency: {
        type: Function,
        required: true
    },
    darkMode: {
        type: Boolean,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['open-calculator']);

// State
const searchQuery = ref('');
const isLoading = ref(props.loading);
const isRefreshing = ref(false);
const sortBy = ref({ label: 'Name (A-Z)', value: 'name-asc' });

const sortOptions = [
    { label: 'Name (A-Z)', value: 'name-asc' },
    { label: 'Name (Z-A)', value: 'name-desc' },
    { label: 'Sale Price (Low-High)', value: 'sale-asc' },
    { label: 'Sale Price (High-Low)', value: 'sale-desc' },
    { label: 'Buy Price (Low-High)', value: 'buy-asc' },
    { label: 'Buy Price (High-Low)', value: 'buy-desc' }
];

// Computed
const filteredCurrencies = computed(() => {
    let result = [...props.currencies];

    // Apply search filter
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(c => {
            const name = c.name || c.currency_code || '';
            const code = c.currency_code || '';
            return name.toLowerCase().includes(query) ||
                   code.toLowerCase().includes(query);
        });
    }

    // Apply sorting with null checks
    switch (sortBy.value.value) {
        case 'name-asc':
            result.sort((a, b) => {
                const nameA = String(a.name || a.currency_code || '');
                const nameB = String(b.name || b.currency_code || '');
                return nameA.localeCompare(nameB);
            });
            break;
        case 'name-desc':
            result.sort((a, b) => {
                const nameA = String(a.name || a.currency_code || '');
                const nameB = String(b.name || b.currency_code || '');
                return nameB.localeCompare(nameA);
            });
            break;
        case 'sale-asc':
            result.sort((a, b) => {
                const saleA = parseFloat(a.sale) || 0;
                const saleB = parseFloat(b.sale) || 0;
                return saleA - saleB;
            });
            break;
        case 'sale-desc':
            result.sort((a, b) => {
                const saleA = parseFloat(a.sale) || 0;
                const saleB = parseFloat(b.sale) || 0;
                return saleB - saleA;
            });
            break;
        case 'buy-asc':
            result.sort((a, b) => {
                const buyA = parseFloat(a.buy) || 0;
                const buyB = parseFloat(b.buy) || 0;
                return buyA - buyB;
            });
            break;
        case 'buy-desc':
            result.sort((a, b) => {
                const buyA = parseFloat(a.buy) || 0;
                const buyB = parseFloat(b.buy) || 0;
                return buyB - buyA;
            });
            break;
    }

    return result;
});

// Methods
function refreshData() {
    isRefreshing.value = true;
    // Simulate a refresh - in a real app, you'd call an API or emit an event
    setTimeout(() => {
        isRefreshing.value = false;
    }, 800);
}

const handleCalculatorClick = (currency) => {
    emit('open-calculator', currency);
};

// Just for demo purposes if loading prop not provided
if (isLoading.value) {
    setTimeout(() => {
        isLoading.value = false;
    }, 2000);
}
</script>

<template>
    <q-intersection once transition="scale">
        <q-card
            :class="['shadow-1 q-mb-lg', darkMode ? 'bg-dark currency-dark' : 'bg-white']"
            style="border-radius: 20px"
        >
            <!-- Search and Controls Section -->
            <q-card-section class="q-pa-md q-pb-none">
                <div class="row q-col-gutter-md items-center q-mb-md">
                    <!-- Search Input -->
                    <div class="col-12 col-md-8">
                        <q-input
                            v-model="searchQuery"
                            :placeholder="t.searchCurrencies || 'Search currencies...'"
                            outlined
                            dense
                            :dark="darkMode"
                            :class="['search-input', darkMode ? 'text-white' : 'text-dark']"
                        >
                            <template v-slot:prepend>
                                <q-icon name="search" />
                            </template>
                            <template v-slot:append v-if="searchQuery">
                                <q-icon name="clear" class="cursor-pointer" @click="searchQuery = ''" />
                            </template>
                        </q-input>
                    </div>

                    <!-- Sort and Refresh Controls -->
                    <div class="col-12 col-md-4">
                        <div class="row q-gutter-sm justify-end">
                            <q-select
                                v-model="sortBy"
                                :options="sortOptions"
                                outlined
                                dense
                                :dark="darkMode"
                                style="min-width: 160px"
                                option-label="label"
                                option-value="value"
                            >
                                <template v-slot:prepend>
                                    <q-icon name="sort" />
                                </template>
                            </q-select>

                            <q-btn
                                round
                                dense
                                :icon="isRefreshing ? 'refresh' : 'refresh'"
                                :loading="isRefreshing"
                                :color="darkMode ? 'grey-7' : 'grey-6'"
                                @click="refreshData"
                                size="md"
                            />
                        </div>
                    </div>
                </div>
            </q-card-section>

            <q-card-section class="q-pa-md q-pt-none">
                <!-- Header Row -->
                <div class="currency-header row items-center q-py-sm q-px-md q-mb-sm"
                     :class="[darkMode ? 'text-grey-4 border-dark' : 'text-grey-7 border-grey-3']">
                    <div class="col-5 col-sm-6 text-uppercase text-weight-bold">{{ t.currency }}</div>
                    <div class="col-3 col-sm-3 text-uppercase text-weight-bold text-center">{{ t.sale }}</div>
                    <div class="col-3 col-sm-3 text-uppercase text-weight-bold text-center">{{ t.buy }}</div>
                    <div class="col-1 hidden-xs"></div>
                </div>

                <!-- Loading Skeleton -->
                <div v-if="isLoading" class="currency-list">
                    <div
                        v-for="i in 8"
                        :key="`skeleton-${i}`"
                        class="currency-row-skeleton q-mb-xs"
                        :class="[darkMode ? 'bg-dark' : 'bg-white']"
                    >
                        <q-skeleton
                            :dark="darkMode"
                            class="full-width"
                            height="72px"
                            animation="wave"
                            :square="false"
                            :bordered="true"
                            :style="{borderRadius: '12px'}"
                        >
                            <div class="row items-center q-pa-md">
                                <!-- Currency Info Skeleton -->
                                <div class="col-5 col-sm-6">
                                    <div class="row items-center q-gutter-x-md">
                                        <q-skeleton type="rect" width="48px" height="36px" animation="none" />
                                        <div class="column q-gutter-y-sm">
                                            <q-skeleton type="text" width="120px" animation="none" />
                                            <q-skeleton type="rect" width="60px" height="20px" animation="none" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Value Skeletons -->
                                <div class="col-3 col-sm-3 text-center">
                                    <q-skeleton type="rect" height="32px" width="80px" animation="none" class="q-mx-auto" />
                                </div>

                                <div class="col-3 col-sm-3 text-center">
                                    <q-skeleton type="rect" height="32px" width="80px" animation="none" class="q-mx-auto" />
                                </div>

                                <div class="col-1 hidden-xs text-center">
                                    <q-skeleton type="circle" size="24px" animation="none" />
                                </div>
                            </div>
                        </q-skeleton>
                    </div>
                </div>

                <!-- No Results -->
                <div v-else-if="filteredCurrencies.length === 0" class="no-results q-pa-xl text-center">
                    <q-icon name="search_off" size="64px" :class="darkMode ? 'text-grey-6' : 'text-grey-7'" />
                    <p class="text-h6 q-mt-md q-mb-none" :class="darkMode ? 'text-grey-5' : 'text-grey-8'">
                        {{ t.noResults || 'No currencies found' }}
                    </p>
                    <p class="text-body2" :class="darkMode ? 'text-grey-6' : 'text-grey-6'">
                        {{ t.tryDifferentSearch || 'Try adjusting your search terms' }}
                    </p>
                </div>

                <!-- Currency List -->
                <div v-else class="currency-list">
                    <transition-group
                        appear
                        enter-active-class="animated fadeInUp"
                        leave-active-class="animated fadeOutDown"
                        class="full-width"
                    >
                        <div
                            v-for="(currency, index) in filteredCurrencies"
                            :key="currency.id"
                            class="currency-row q-mb-xs"
                            :style="{ animationDelay: `${index * 0.03}s` }"
                            @click="handleCalculatorClick(currency)"
                        >
                            <div
                                :class="[
                                    'currency-row-content row items-center q-pa-md cursor-pointer',
                                    darkMode ? 'currency-row-dark' : 'currency-row-light'
                                ]"
                            >
                                <!-- Currency Info -->
                                <div class="col-5 col-sm-6">
                                    <div class="row items-center q-gutter-x-md no-wrap">
                                        <CountryFlag
                                            :code="currency.currency_code || currency.code || 'USD'"
                                            :country="currency.name || currency.currency_code"
                                            :flag-icon="currency.flag_icon"
                                            :flag-country-code="currency.flag_country_code"
                                            size="medium"
                                        />
                                        <div class="currency-info">
                                            <div class="currency-name text-weight-bold"
                                                 :class="darkMode ? 'text-white' : 'text-dark'">
                                                {{ currency.name || currency.currency_code || 'Unknown Currency' }}
                                            </div>
                                            <div class="currency-code"
                                                 :class="darkMode ? 'text-grey-5' : 'text-grey-7'">
                                                {{ currency.currency_code || 'N/A' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sale Price -->
                                <div class="col-3 col-sm-3 text-center">
                                    <div
                                        :class="[
                                            'value-chip',
                                            currency.sale_status === 'up'
                                                ? darkMode ? 'sale-up-dark' : 'sale-up-light'
                                                : darkMode ? 'sale-dark' : 'sale-light'
                                        ]"
                                    >
                                        <q-icon
                                            :name="currency.sale_status === 'up' ? 'trending_up' : 'trending_flat'"
                                            size="xs"
                                            class="q-mr-xs"
                                        />
                                        <span class="value-text">{{ formatCurrency(currency.sale || 0) }}</span>
                                    </div>
                                </div>

                                <!-- Buy Price -->
                                <div class="col-3 col-sm-3 text-center">
                                    <div
                                        :class="[
                                            'value-chip',
                                            currency.buy_status === 'down'
                                                ? darkMode ? 'buy-down-dark' : 'buy-down-light'
                                                : darkMode ? 'buy-dark' : 'buy-light'
                                        ]"
                                    >
                                        <q-icon
                                            :name="currency.buy_status === 'down' ? 'trending_down' : 'trending_flat'"
                                            size="xs"
                                            class="q-mr-xs"
                                        />
                                        <span class="value-text">{{ formatCurrency(currency.buy || 0) }}</span>
                                    </div>
                                </div>

                                <!-- Action Button (Desktop only) -->
                                <div class="col-1 hidden-xs text-center">
                                    <q-btn
                                        round
                                        dense
                                        size="sm"
                                        icon="calculate"
                                        :color="darkMode ? 'grey-7' : 'grey-6'"
                                        @click.stop="handleCalculatorClick(currency)"
                                    />
                                </div>
                            </div>
                        </div>
                    </transition-group>
                </div>
            </q-card-section>
        </q-card>
    </q-intersection>
</template>

<style scoped>
.currency-dark {
    background: linear-gradient(145deg, #111827, #1f2937) !important;
    border: 1px solid rgba(55, 65, 81, 0.5);
}

.search-input :deep(.q-field__control) {
    border-radius: 12px;
}

.currency-header {
    border-bottom: 2px solid;
    border-radius: 8px 8px 0 0;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.border-dark {
    border-color: rgba(51, 65, 85, 0.6) !important;
}

.currency-list {
    max-height: 70vh;
    overflow-y: auto;
}

.currency-list::-webkit-scrollbar {
    width: 6px;
}

.currency-list::-webkit-scrollbar-track {
    background: transparent;
}

.currency-list::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}

.currency-list::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
}

.currency-row {
    transition: all 0.2s ease;
}

.currency-row-content {
    border-radius: 12px;
    transition: all 0.3s ease;
    min-height: 72px;
    border: 1px solid transparent;
}

.currency-row-light {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(226, 232, 240, 0.6);
    backdrop-filter: blur(10px);
}

.currency-row-dark {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(51, 65, 85, 0.4);
    backdrop-filter: blur(10px);
}

.currency-row:hover .currency-row-content {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.12);
    border-color: rgba(59, 130, 246, 0.4);
}

.currency-row-dark:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(59, 130, 246, 0.5);
}

.flag-wrapper {
    width: 48px;
    height: 36px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
    flex-shrink: 0;
}

.flag-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 100%);
    z-index: 1;
}

.currency-row-dark .flag-wrapper::after {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 100%);
}

.flag-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.currency-row:hover .flag-img {
    transform: scale(1.05);
}

.currency-info {
    min-width: 0;
    flex: 1;
}

.currency-name {
    font-size: 1rem;
    line-height: 1.2;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.currency-code {
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.value-chip {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    min-width: 80px;
    border: 1px solid;
}

.value-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
}

/* Sale Price Styles */
.sale-light {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #16a34a;
    border-color: rgba(34, 197, 94, 0.3);
}

.sale-up-light {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #15803d;
    border-color: rgba(34, 197, 94, 0.5);
}

.sale-dark {
    background: linear-gradient(135deg, rgba(22, 101, 52, 0.2), rgba(22, 101, 52, 0.1));
    color: #4ade80;
    border-color: rgba(74, 222, 128, 0.3);
}

.sale-up-dark {
    background: linear-gradient(135deg, rgba(22, 101, 52, 0.3), rgba(22, 101, 52, 0.2));
    color: #22c55e;
    border-color: rgba(74, 222, 128, 0.5);
}

/* Buy Price Styles */
.buy-light {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #dc2626;
    border-color: rgba(239, 68, 68, 0.3);
}

.buy-down-light {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #b91c1c;
    border-color: rgba(239, 68, 68, 0.5);
}

.buy-dark {
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.2), rgba(153, 27, 27, 0.1));
    color: #f87171;
    border-color: rgba(248, 113, 113, 0.3);
}

.buy-down-dark {
    background: linear-gradient(135deg, rgba(153, 27, 27, 0.3), rgba(153, 27, 27, 0.2));
    color: #ef4444;
    border-color: rgba(248, 113, 113, 0.5);
}

.value-chip:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.no-results {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .currency-header {
        font-size: 0.75rem;
        padding: 8px 12px;
    }

    .currency-row-content {
        padding: 12px;
        min-height: 64px;
    }

    .flag-wrapper {
        width: 40px;
        height: 30px;
    }

    .currency-name {
        font-size: 0.9rem;
    }

    .currency-code {
        font-size: 0.75rem;
    }

    .value-chip {
        padding: 6px 8px;
        font-size: 0.8rem;
        min-width: 70px;
    }

    .value-text {
        max-width: 80px;
    }
}

@media (max-width: 480px) {
    .currency-row-content {
        padding: 10px;
        min-height: 56px;
    }

    .flag-wrapper {
        width: 32px;
        height: 24px;
    }

    .currency-name {
        font-size: 0.85rem;
    }

    .currency-code {
        font-size: 0.7rem;
    }

    .value-chip {
        padding: 4px 6px;
        font-size: 0.75rem;
        min-width: 60px;
    }

    .value-text {
        max-width: 60px;
    }

    .currency-info {
        margin-left: 8px;
    }
}

/* Animation Classes */
.animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.fadeInUp {
    animation-name: fadeInUp;
}

.fadeOutDown {
    animation-name: fadeOutDown;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
    to {
        opacity: 0;
        transform: translate3d(0, 30px, 0);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .animated,
    .currency-row,
    .currency-row-content,
    .flag-img,
    .value-chip {
        animation: none !important;
        transition: none !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .currency-row-light {
        border-color: #000;
    }

    .currency-row-dark {
        border-color: #fff;
    }

    .value-chip {
        border-width: 2px;
    }
}
</style>
