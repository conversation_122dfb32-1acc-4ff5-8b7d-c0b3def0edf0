<template>
  <q-dialog v-model="showModal" persistent>
    <q-card style="min-width: 500px; max-width: 700px;">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          <q-icon name="sync" class="q-mr-sm" />
          Fetching Exchange Rates
        </div>
        <q-space />
        <q-btn
          icon="close"
          flat
          round
          dense
          v-close-popup
          :disable="isProcessing"
        />
      </q-card-section>

      <q-card-section>
        <div class="text-body2 text-grey-7 q-mb-md">
          Fetching rates from multiple sources to ensure accuracy and reliability
        </div>

        <!-- Overall Progress -->
        <div class="q-mb-lg">
          <div class="row items-center q-mb-sm">
            <div class="text-subtitle2">Overall Progress</div>
            <q-space />
            <div class="text-caption">
              {{ completedProviders }}/{{ totalProviders }} sources
            </div>
          </div>
          <q-linear-progress
            :value="overallProgress"
            color="primary"
            size="8px"
            class="q-mb-sm"
          />
          <div class="text-caption text-grey-6">
            {{ currentStatus }}
          </div>
        </div>

        <!-- Provider Status List -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Source Status</div>
          <q-list separator>
            <q-item
              v-for="provider in providerStatuses"
              :key="provider.key"
              class="q-px-none"
            >
              <q-item-section avatar>
                <q-icon
                  :name="getProviderIcon(provider.status)"
                  :color="getProviderColor(provider.status)"
                  size="sm"
                />
              </q-item-section>

              <q-item-section>
                <q-item-label>{{ provider.name }}</q-item-label>
                <q-item-label caption>
                  {{ provider.message || 'Waiting...' }}
                </q-item-label>
              </q-item-section>

              <q-item-section side v-if="provider.rate_count">
                <q-chip
                  size="sm"
                  color="positive"
                  text-color="white"
                >
                  {{ provider.rate_count }} rates
                </q-chip>
              </q-item-section>

              <q-item-section side v-if="provider.status === 'fetching'">
                <q-spinner color="primary" size="20px" />
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Results Summary -->
        <div v-if="fetchResults && !isProcessing" class="q-mt-md">
          <q-separator class="q-mb-md" />

          <div class="text-subtitle2 q-mb-sm">Results Summary</div>

          <div class="row q-gutter-md">
            <div class="col">
              <q-card flat bordered class="text-center q-pa-md">
                <div class="text-h4 text-positive">
                  {{ fetchResults.successful_providers_count }}
                </div>
                <div class="text-caption text-grey-6">
                  Successful Sources
                </div>
              </q-card>
            </div>

            <div class="col">
              <q-card flat bordered class="text-center q-pa-md">
                <div class="text-h4 text-primary">
                  {{ Object.keys(fetchResults.combined_rates || {}).length }}
                </div>
                <div class="text-caption text-grey-6">
                  Exchange Rates
                </div>
              </q-card>
            </div>

            <div class="col" v-if="fetchResults.updated_count !== undefined">
              <q-card flat bordered class="text-center q-pa-md">
                <div class="text-h4 text-info">
                  {{ fetchResults.updated_count }}
                </div>
                <div class="text-caption text-grey-6">
                  Rates Updated
                </div>
              </q-card>
            </div>
          </div>

          <!-- Error Summary -->
          <div v-if="Object.keys(fetchResults.errors || {}).length > 0" class="q-mt-md">
            <q-expansion-item
              icon="warning"
              label="View Errors"
              header-class="text-negative"
            >
              <q-card flat bordered class="q-pa-md">
                <div
                  v-for="(error, provider) in fetchResults.errors"
                  :key="provider"
                  class="q-mb-sm"
                >
                  <strong>{{ getProviderName(provider) }}:</strong> {{ error }}
                </div>
              </q-card>
            </q-expansion-item>
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pt-none">
        <q-btn
          flat
          label="Close"
          color="grey"
          v-close-popup
          :disable="isProcessing"
        />
        <q-btn
          v-if="!isProcessing && fetchResults"
          unelevated
          label="Refresh Rates"
          color="primary"
          @click="startFetching"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'

const $q = useQuasar()

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  autoStart: {
    type: Boolean,
    default: true
  },
  baseCurrency: {
    type: String,
    default: 'USD'
  },
  targetCurrencies: {
    type: Array,
    default: () => null
  },
  updateDatabase: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'completed', 'error'])

// Reactive data
const showModal = ref(props.modelValue)
const isProcessing = ref(false)
const currentStatus = ref('Initializing...')
const providerStatuses = ref([])
const fetchResults = ref(null)
const availableProviders = ref([])

// Computed
const totalProviders = computed(() => providerStatuses.value.length)
const completedProviders = computed(() =>
  providerStatuses.value.filter(p => p.status === 'success' || p.status === 'error').length
)
const overallProgress = computed(() =>
  totalProviders.value > 0 ? completedProviders.value / totalProviders.value : 0
)

// Watch for model value changes
watch(() => props.modelValue, (newVal) => {
  showModal.value = newVal
  if (newVal && props.autoStart) {
    startFetching()
  }
})

watch(showModal, (newVal) => {
  emit('update:modelValue', newVal)
})

// Methods
const getProviderIcon = (status) => {
  switch (status) {
    case 'success': return 'check_circle'
    case 'error': return 'error'
    case 'fetching': return 'sync'
    default: return 'radio_button_unchecked'
  }
}

const getProviderColor = (status) => {
  switch (status) {
    case 'success': return 'positive'
    case 'error': return 'negative'
    case 'fetching': return 'primary'
    default: return 'grey'
  }
}

const getProviderName = (providerKey) => {
  const provider = availableProviders.value.find(p => p.key === providerKey)
  return provider ? provider.name : providerKey
}

const loadProviders = async () => {
  try {
    const response = await axios.get('/api/exchange/providers')
    if (response.data.success) {
      availableProviders.value = response.data.data
      providerStatuses.value = response.data.data.map(provider => ({
        key: provider.key,
        name: provider.name,
        priority: provider.priority,
        status: 'waiting',
        message: 'Waiting...',
        rate_count: null
      }))
    }
  } catch (error) {
    console.error('Error loading providers:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load rate providers'
    })
  }
}

const updateProviderStatus = (update) => {
  const provider = providerStatuses.value.find(p => p.name === update.provider)
  if (provider) {
    provider.status = update.status
    provider.message = update.message
    if (update.rate_count) {
      provider.rate_count = update.rate_count
    }
  }
  currentStatus.value = update.message
}

const startFetching = async () => {
  isProcessing.value = true
  fetchResults.value = null
  currentStatus.value = 'Starting rate fetch...'

  // Reset provider statuses
  providerStatuses.value.forEach(provider => {
    provider.status = 'waiting'
    provider.message = 'Waiting...'
    provider.rate_count = null
  })

  try {
    if (props.updateDatabase) {
      // Update database with rates from multiple sources
      const response = await axios.post('/api/exchange/update-multi-source', {
        base_currency: props.baseCurrency,
        target_currencies: props.targetCurrencies
      })

      if (response.data.success) {
        fetchResults.value = response.data.data
        currentStatus.value = 'Database updated successfully!'

        emit('completed', {
          success: true,
          data: response.data.data
        })

        $q.notify({
          type: 'positive',
          message: `Successfully updated ${response.data.data.updated_count} exchange rates`,
          timeout: 3000
        })
      }
    } else {
      // Just fetch rates without updating database
      const response = await axios.get('/api/exchange/fetch-multi-source', {
        params: {
          base_currency: props.baseCurrency,
          target_currencies: props.targetCurrencies
        }
      })

      if (response.data.success) {
        fetchResults.value = response.data.data
        currentStatus.value = 'Rates fetched successfully!'

        // Update provider statuses based on results
        Object.entries(response.data.data.source_results).forEach(([providerKey, result]) => {
          const provider = providerStatuses.value.find(p => p.key === providerKey)
          if (provider) {
            provider.status = 'success'
            provider.message = `Fetched ${result.rate_count} rates`
            provider.rate_count = result.rate_count
          }
        })

        // Mark failed providers
        Object.entries(response.data.data.errors).forEach(([providerKey, error]) => {
          const provider = providerStatuses.value.find(p => p.key === providerKey)
          if (provider) {
            provider.status = 'error'
            provider.message = error
          }
        })

        emit('completed', {
          success: true,
          data: response.data.data
        })
      }
    }
  } catch (error) {
    console.error('Error fetching rates:', error)
    currentStatus.value = 'Error occurred while fetching rates'

    emit('error', {
      success: false,
      error: error.response?.data?.message || error.message
    })

    $q.notify({
      type: 'negative',
      message: error.response?.data?.message || 'Failed to fetch exchange rates',
      timeout: 5000
    })
  } finally {
    isProcessing.value = false
  }
}

// Initialize
const init = async () => {
  await loadProviders()
  if (props.modelValue && props.autoStart) {
    startFetching()
  }
}

// Auto-initialize when component mounts
init()
</script>

<style scoped>
.q-card {
  border-radius: 12px;
}

.q-linear-progress {
  border-radius: 4px;
}

.q-chip {
  font-size: 0.75rem;
}
</style>
