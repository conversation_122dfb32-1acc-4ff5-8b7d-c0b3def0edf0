<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="user-activity-dialog">
      <q-card-section class="dialog-header">
        <div class="header-info">
          <div class="text-h6">User Activity</div>
          <div class="text-subtitle2 text-grey-6" v-if="user">
            {{ user.name }} ({{ user.email }})
          </div>
        </div>
        <q-btn flat round dense icon="close" @click="closeDialog" />
      </q-card-section>

      <q-separator />

      <!-- Activity Filters -->
      <q-card-section class="filter-section">
        <div class="row q-gutter-md items-end">
          <div class="col-12 col-sm-4">
            <q-select
              v-model="activityFilter"
              :options="activityFilterOptions"
              label="Activity Type"
              outlined
              dense
              @update:model-value="loadActivity"
            />
          </div>
          <div class="col-12 col-sm-4">
            <q-select
              v-model="dateRange"
              :options="dateRangeOptions"
              label="Date Range"
              outlined
              dense
              @update:model-value="loadActivity"
            />
          </div>
          <div class="col-12 col-sm-4">
            <q-btn
              icon="refresh"
              label="Refresh"
              color="primary"
              outline
              @click="loadActivity"
              :loading="loading"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <!-- Activity Statistics -->
      <q-card-section class="stats-section">
        <div class="row q-gutter-md">
          <div class="col-6 col-sm-3">
            <div class="stat-item">
              <q-icon name="login" color="primary" size="24px" />
              <div class="stat-value">{{ activityStats.logins }}</div>
              <div class="stat-label">Logins</div>
            </div>
          </div>
          <div class="col-6 col-sm-3">
            <div class="stat-item">
              <q-icon name="edit" color="secondary" size="24px" />
              <div class="stat-value">{{ activityStats.edits }}</div>
              <div class="stat-label">Edits</div>
            </div>
          </div>
          <div class="col-6 col-sm-3">
            <div class="stat-item">
              <q-icon name="visibility" color="accent" size="24px" />
              <div class="stat-value">{{ activityStats.views }}</div>
              <div class="stat-label">Views</div>
            </div>
          </div>
          <div class="col-6 col-sm-3">
            <div class="stat-item">
              <q-icon name="schedule" color="warning" size="24px" />
              <div class="stat-value">{{ formatDuration(activityStats.totalTime) }}</div>
              <div class="stat-label">Total Time</div>
            </div>
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <!-- Activity Timeline -->
      <q-card-section class="timeline-section">
        <div class="section-header">
          <h6 class="section-title">Activity Timeline</h6>
          <q-chip
            v-if="filteredActivities.length > 0"
            :label="`${filteredActivities.length} activities`"
            color="primary"
            outline
            size="sm"
          />
        </div>

        <q-scroll-area class="activity-timeline" style="height: 400px;">
          <div v-if="loading" class="loading-state">
            <q-spinner-dots size="2rem" color="primary" />
            <div class="q-mt-sm">Loading activities...</div>
          </div>

          <div v-else-if="filteredActivities.length === 0" class="empty-state">
            <q-icon name="timeline" size="48px" color="grey-4" />
            <div class="empty-text">No activities found</div>
            <div class="empty-subtitle">Try adjusting your filters</div>
          </div>

          <div v-else class="timeline-list">
            <div
              v-for="(activity, index) in filteredActivities"
              :key="activity.id"
              class="timeline-item"
              :class="{ 'timeline-item-last': index === filteredActivities.length - 1 }"
            >
              <div class="timeline-marker">
                <q-icon
                  :name="getActivityIcon(activity.type)"
                  :color="getActivityColor(activity.type)"
                  size="20px"
                />
              </div>
              
              <div class="timeline-content">
                <div class="activity-header">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ formatActivityTime(activity.created_at) }}</div>
                </div>
                
                <div class="activity-description">{{ activity.description }}</div>
                
                <div v-if="activity.details" class="activity-details">
                  <q-expansion-item
                    icon="info"
                    label="Details"
                    dense
                    header-class="text-caption"
                  >
                    <div class="details-content">
                      <pre>{{ JSON.stringify(activity.details, null, 2) }}</pre>
                    </div>
                  </q-expansion-item>
                </div>
                
                <div class="activity-meta">
                  <q-chip
                    :color="getActivityColor(activity.type)"
                    text-color="white"
                    size="xs"
                  >
                    {{ activity.type }}
                  </q-chip>
                  <span class="activity-ip" v-if="activity.ip_address">
                    IP: {{ activity.ip_address }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </q-scroll-area>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right" class="dialog-actions">
        <q-btn flat label="Export" icon="download" @click="exportActivity" />
        <q-btn flat label="Close" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const $q = useQuasar()

// Reactive data
const showDialog = ref(false)
const loading = ref(false)
const activities = ref([])
const activityFilter = ref('all')
const dateRange = ref('7d')

const activityStats = ref({
  logins: 0,
  edits: 0,
  views: 0,
  totalTime: 0
})

// Options
const activityFilterOptions = [
  { label: 'All Activities', value: 'all' },
  { label: 'Logins', value: 'login' },
  { label: 'Edits', value: 'edit' },
  { label: 'Views', value: 'view' },
  { label: 'System', value: 'system' }
]

const dateRangeOptions = [
  { label: 'Last 7 days', value: '7d' },
  { label: 'Last 30 days', value: '30d' },
  { label: 'Last 90 days', value: '90d' },
  { label: 'All time', value: 'all' }
]

// Computed properties
const filteredActivities = computed(() => {
  let filtered = activities.value

  if (activityFilter.value !== 'all') {
    filtered = filtered.filter(a => a.type === activityFilter.value)
  }

  return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
})

// Methods
const loadActivity = async () => {
  if (!props.user) return

  loading.value = true

  try {
    // Mock activity data
    activities.value = generateMockActivity()
    updateActivityStats()
  } catch (error) {
    console.error('Error loading activity:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load user activity'
    })
  } finally {
    loading.value = false
  }
}

const generateMockActivity = () => {
  const activityTypes = ['login', 'edit', 'view', 'system']
  const mockActivities = []

  for (let i = 0; i < 20; i++) {
    const type = activityTypes[Math.floor(Math.random() * activityTypes.length)]
    const date = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)

    mockActivities.push({
      id: i + 1,
      type,
      title: getActivityTitle(type),
      description: getActivityDescription(type),
      created_at: date.toISOString(),
      ip_address: `192.168.1.${Math.floor(Math.random() * 255)}`,
      details: {
        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        session_id: `sess_${Math.random().toString(36).substr(2, 9)}`
      }
    })
  }

  return mockActivities
}

const getActivityTitle = (type) => {
  const titles = {
    login: 'User Login',
    edit: 'Data Modified',
    view: 'Page Viewed',
    system: 'System Action'
  }
  return titles[type] || 'Unknown Activity'
}

const getActivityDescription = (type) => {
  const descriptions = {
    login: 'User successfully logged into the admin panel',
    edit: 'Modified currency exchange rate settings',
    view: 'Viewed the analytics dashboard',
    system: 'System performed automated backup'
  }
  return descriptions[type] || 'Unknown activity performed'
}

const updateActivityStats = () => {
  const stats = {
    logins: 0,
    edits: 0,
    views: 0,
    totalTime: 0
  }

  activities.value.forEach(activity => {
    switch (activity.type) {
      case 'login':
        stats.logins++
        break
      case 'edit':
        stats.edits++
        break
      case 'view':
        stats.views++
        break
    }
  })

  stats.totalTime = Math.floor(Math.random() * 480) + 60 // 1-8 hours in minutes

  activityStats.value = stats
}

const getActivityIcon = (type) => {
  const icons = {
    login: 'login',
    edit: 'edit',
    view: 'visibility',
    system: 'settings'
  }
  return icons[type] || 'help'
}

const getActivityColor = (type) => {
  const colors = {
    login: 'positive',
    edit: 'primary',
    view: 'info',
    system: 'warning'
  }
  return colors[type] || 'grey'
}

const formatActivityTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

const formatDuration = (minutes) => {
  if (minutes < 60) return `${minutes}m`
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h ${mins}m`
}

const exportActivity = () => {
  // Export activity data
  const data = JSON.stringify(filteredActivities.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const a = document.createElement('a')
  a.href = url
  a.download = `user-activity-${props.user?.id || 'unknown'}.json`
  a.click()
  
  URL.revokeObjectURL(url)
  
  $q.notify({
    type: 'positive',
    message: 'Activity data exported successfully'
  })
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

// Watchers
watch(() => props.modelValue, (newVal) => {
  showDialog.value = newVal
  if (newVal && props.user) {
    loadActivity()
  }
})

watch(showDialog, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})
</script>

<style scoped>
.user-activity-dialog {
  width: 100%;
  max-width: 800px;
  border-radius: 12px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
}

.filter-section,
.stats-section,
.timeline-section {
  padding: 16px 24px;
}

.dialog-actions {
  padding: 16px 24px 20px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin: 8px 0 4px;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 48px 20px;
  color: #9ca3af;
}

.empty-text {
  margin-top: 12px;
  font-size: 1rem;
  font-weight: 500;
}

.empty-subtitle {
  margin-top: 4px;
  font-size: 0.875rem;
}

.timeline-list {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.timeline-item:not(.timeline-item-last)::after {
  content: '';
  position: absolute;
  left: 19px;
  top: 40px;
  bottom: -24px;
  width: 2px;
  background: #e5e7eb;
}

.timeline-marker {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  border: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  z-index: 1;
}

.timeline-content {
  flex: 1;
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.activity-title {
  font-weight: 600;
  color: #374151;
}

.activity-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.activity-description {
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.5;
}

.activity-details {
  margin: 12px 0;
}

.details-content {
  background: #f1f5f9;
  border-radius: 4px;
  padding: 8px;
  font-size: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
}

.details-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
}

.activity-ip {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Dark mode */
.body--dark .stat-item,
.body--dark .timeline-content {
  background: #374151;
}

.body--dark .stat-value,
.body--dark .section-title,
.body--dark .activity-title {
  color: #f9fafb;
}

.body--dark .timeline-marker {
  background: #4b5563;
  border-color: #6b7280;
}

.body--dark .timeline-item:not(.timeline-item-last)::after {
  background: #6b7280;
}

.body--dark .details-content {
  background: #4b5563;
  color: #e5e7eb;
}

/* Responsive */
@media (max-width: 800px) {
  .user-activity-dialog {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
  
  .filter-section,
  .stats-section,
  .timeline-section {
    padding: 12px 16px;
  }
  
  .timeline-marker {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }
  
  .timeline-item:not(.timeline-item-last)::after {
    left: 15px;
  }
}
</style>
