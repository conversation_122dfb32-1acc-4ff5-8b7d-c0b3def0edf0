<template>
  <q-card class="user-card">
    <q-card-section class="user-header">
      <div class="user-avatar-section">
        <q-avatar size="60px" class="user-avatar">
          <img v-if="user.avatar" :src="user.avatar" />
          <q-icon v-else name="person" size="32px" />
        </q-avatar>
        <div class="online-indicator" v-if="user.is_online">
          <q-icon name="circle" color="positive" size="12px" />
        </div>
      </div>

      <div class="user-info">
        <h6 class="user-name">{{ user.name }}</h6>
        <div class="user-email">{{ user.email }}</div>
        <q-chip
          :color="getRoleColor(user.role)"
          text-color="white"
          size="sm"
          class="user-role-chip"
        >
          {{ formatRole(user.role) }}
        </q-chip>
      </div>
    </q-card-section>

    <q-card-section class="user-details">
      <div class="detail-item">
        <q-icon name="schedule" size="16px" color="grey-6" />
        <span class="detail-label">Last Login:</span>
        <span class="detail-value">{{ formatLastLogin(user.last_login) }}</span>
      </div>

      <div class="detail-item">
        <q-icon
          :name="user.is_active ? 'check_circle' : 'cancel'"
          size="16px"
          :color="user.is_active ? 'positive' : 'negative'"
        />
        <span class="detail-label">Status:</span>
        <span class="detail-value" :class="user.is_active ? 'text-positive' : 'text-negative'">
          {{ user.is_active ? 'Active' : 'Inactive' }}
        </span>
      </div>

      <div class="detail-item">
        <q-icon name="login" size="16px" color="grey-6" />
        <span class="detail-label">Member Since:</span>
        <span class="detail-value">{{ formatMemberSince(user.created_at) }}</span>
      </div>
    </q-card-section>

    <q-card-actions class="user-actions">
      <q-btn
        @click="$emit('edit', user)"
        icon="edit"
        label="Edit"
        color="primary"
        flat
        size="sm"
        class="action-btn"
        no-caps
      />

      <q-btn
        @click="$emit('toggle-status', user)"
        :icon="user.is_active ? 'block' : 'check_circle'"
        :label="user.is_active ? 'Deactivate' : 'Activate'"
        :color="user.is_active ? 'warning' : 'positive'"
        flat
        size="sm"
        class="action-btn"
        no-caps
      />

      <q-btn
        @click="$emit('view-activity', user)"
        icon="history"
        label="Activity"
        color="info"
        flat
        size="sm"
        class="action-btn"
        no-caps
      />
    </q-card-actions>

    <!-- Quick Stats Overlay -->
    <div class="user-stats-overlay" v-if="showStats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ user.login_count || 0 }}</div>
          <div class="stat-label">Logins</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ user.actions_count || 0 }}</div>
          <div class="stat-label">Actions</div>
        </div>
      </div>
    </div>
  </q-card>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  user: {
    type: Object,
    required: true
  },
  showStats: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['edit', 'toggle-status', 'view-activity'])

// Methods
const getRoleColor = (role) => {
  if (!role) return 'grey'

  const colors = {
    'Super Admin': 'deep-purple',
    'Admin': 'primary',
    'Manager': 'secondary',
    'Editor': 'accent',
    'Viewer': 'info',
    'User': 'grey'
  }
  return colors[role] || 'grey'
}

const formatRole = (role) => {
  // Role is already formatted from UserResource
  return role || 'No Role'
}

const formatLastLogin = (lastLogin) => {
  if (!lastLogin) return 'Never'

  const now = new Date()
  const login = new Date(lastLogin)
  const diffMinutes = Math.floor((now - login) / (1000 * 60))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  if (diffMinutes < 10080) return `${Math.floor(diffMinutes / 1440)}d ago`
  return login.toLocaleDateString()
}

const formatMemberSince = (createdAt) => {
  if (!createdAt) return 'Unknown'

  const date = new Date(createdAt)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short'
  })
}
</script>

<style scoped>
.user-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.user-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.user-header {
  text-align: center;
  padding: 24px 20px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #f3f4f6;
}

.user-avatar-section {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.user-avatar {
  border: 3px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.online-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: white;
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-info {
  text-align: center;
}

.user-name {
  margin: 0 0 4px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.user-email {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 12px;
}

.user-role-chip {
  font-weight: 500;
}

.user-details {
  padding: 16px 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 0.875rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #6b7280;
  min-width: 80px;
}

.detail-value {
  color: #374151;
  font-weight: 500;
}

.user-actions {
  padding: 12px 16px 16px;
  justify-content: space-between;
  border-top: 1px solid #f3f4f6;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  min-width: 70px;
}

.user-stats-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.user-card:hover .user-stats-overlay {
  opacity: 1;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  text-align: center;
}

.stat-item {
  padding: 12px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Dark mode */
.body--dark .user-card {
  background: #2d2d2d;
  border-color: #404040;
}

.body--dark .user-header {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-bottom-color: #404040;
}

.body--dark .user-name,
.body--dark .detail-value,
.body--dark .stat-value {
  color: #f9fafb;
}

.body--dark .user-email,
.body--dark .detail-label,
.body--dark .stat-label {
  color: #d1d5db;
}

.body--dark .user-actions {
  border-top-color: #404040;
}

.body--dark .user-stats-overlay {
  background: rgba(45, 45, 45, 0.95);
}

/* Responsive */
@media (max-width: 480px) {
  .user-header {
    padding: 20px 16px 12px;
  }

  .user-avatar {
    width: 50px;
    height: 50px;
  }

  .user-name {
    font-size: 1rem;
  }

  .user-details {
    padding: 12px 16px;
  }

  .detail-item {
    font-size: 0.8rem;
    margin-bottom: 8px;
  }

  .user-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
