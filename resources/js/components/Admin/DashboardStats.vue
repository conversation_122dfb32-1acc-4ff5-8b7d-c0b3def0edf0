<template>
  <div class="dashboard-stats">
    <!-- Enhanced Stats Cards Row -->
    <div class="row q-gutter-lg q-mb-xl">
      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern primary">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="account_balance_wallet" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_up" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">{{ statistics.total_currencies || 0 }}</div>
              <div class="stats-label-modern">Total Currencies</div>
              <div class="stats-change-indicator">
                <q-icon name="trending_up" size="12px" />
                <span>+2 this month</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern secondary">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="check_circle" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_up" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">{{ statistics.active_currencies || 0 }}</div>
              <div class="stats-label-modern">Active Currencies</div>
              <div class="stats-change-indicator">
                <q-icon name="check" size="12px" />
                <span>{{ statistics.total_currencies ? ((statistics.active_currencies / statistics.total_currencies) * 100).toFixed(0) : 0 }}% active</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern accent">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="swap_horiz" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_flat" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">{{ statistics.total_exchange_rates || 0 }}</div>
              <div class="stats-label-modern">Exchange Rates</div>
              <div class="stats-change-indicator">
                <q-icon name="swap_horiz" size="12px" />
                <span>{{ statistics.active_exchange_rates || 0 }} active</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern" style="background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); color: white;">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="update" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon :name="statistics.rate_updates_today > 0 ? 'trending_up' : 'trending_flat'" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">{{ statistics.rate_updates_today || 0 }}</div>
              <div class="stats-label-modern">Updates Today</div>
              <div class="stats-change-indicator">
                <q-icon name="schedule" size="12px" />
                <span>{{ formatLastUpdate(statistics.last_rate_update) }}</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="row q-gutter-lg q-mb-xl">
      <!-- Currency Distribution Chart -->
      <div class="col-12 col-md-6">
        <q-card class="chart-card">
          <q-card-section>
            <div class="chart-header">
              <h6 class="chart-title">Currency Distribution</h6>
              <q-btn flat round dense icon="more_vert" size="sm">
                <q-menu>
                  <q-list>
                    <q-item clickable v-close-popup>
                      <q-item-section>Export</q-item-section>
                    </q-item>
                    <q-item clickable v-close-popup>
                      <q-item-section>Refresh</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-btn>
            </div>
            <div class="chart-content">
              <!-- Placeholder for chart - you can integrate Chart.js or similar -->
              <div class="chart-placeholder">
                <q-icon name="pie_chart" size="64px" color="grey-5" />
                <div class="text-grey-6 q-mt-md">Currency distribution chart</div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Recent Activity -->
      <div class="col-12 col-md-6">
        <q-card class="activity-card">
          <q-card-section>
            <div class="chart-header">
              <h6 class="chart-title">Recent Activity</h6>
              <q-btn flat round dense icon="refresh" size="sm" @click="loadStatistics" />
            </div>
            <q-list class="activity-list">
              <q-item v-for="(activity, index) in recentActivities" :key="index" class="activity-item">
                <q-item-section avatar>
                  <q-avatar :color="activity.color" text-color="white" size="32px">
                    <q-icon :name="activity.icon" />
                  </q-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ activity.title }}</q-item-label>
                  <q-item-label caption>{{ activity.description }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-item-label caption>{{ activity.time }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row q-gutter-lg">
      <div class="col-12">
        <q-card class="quick-actions-card">
          <q-card-section>
            <h6 class="chart-title q-mb-md">Quick Actions</h6>
            <div class="row q-gutter-md">
              <div class="col-auto">
                <q-btn
                  color="primary"
                  icon="add"
                  label="Add Currency"
                  @click="navigateTo('admin.currencies.create')"
                  class="quick-action-btn"
                />
              </div>
              <div class="col-auto">
                <q-btn
                  color="secondary"
                  icon="trending_up"
                  label="Update Rates"
                  @click="navigateTo('admin.currency-exchange-rates.index')"
                  class="quick-action-btn"
                />
              </div>
              <div class="col-auto">
                <q-btn
                  color="accent"
                  icon="analytics"
                  label="View Analytics"
                  class="quick-action-btn"
                />
              </div>
              <div class="col-auto">
                <q-btn
                  color="info"
                  icon="settings"
                  label="Settings"
                  class="quick-action-btn"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()

const statistics = ref({
  total_currencies: 0,
  active_currencies: 0,
  inactive_currencies: 0,
  total_exchange_rates: 0,
  active_exchange_rates: 0,
  rate_updates_today: 0,
  last_rate_update: null
})

const recentActivities = ref([
  {
    icon: 'add',
    color: 'positive',
    title: 'New Currency Added',
    description: 'Turkish Lira (TRY) was added to the system',
    time: '2 hours ago'
  },
  {
    icon: 'trending_up',
    color: 'warning',
    title: 'Exchange Rate Updated',
    description: 'USD/IQD rate changed from 1,320 to 1,325',
    time: '4 hours ago'
  },
  {
    icon: 'edit',
    color: 'info',
    title: 'Currency Modified',
    description: 'Euro (EUR) settings were updated',
    time: '1 day ago'
  },
  {
    icon: 'history',
    color: 'secondary',
    title: 'Rate History Archived',
    description: 'Old exchange rate data was archived',
    time: '2 days ago'
  }
])

const loadStatistics = async () => {
  try {
    const response = await axios.get('/api/admin/currencies-statistics')
    if (response.data.success) {
      statistics.value = response.data.statistics
    }
  } catch (error) {
    console.error('Failed to load statistics:', error)
  }
}

const formatLastUpdate = (date) => {
  if (!date) return 'No updates'
  const now = new Date()
  const updateDate = new Date(date)
  const diffHours = Math.floor((now - updateDate) / (1000 * 60 * 60))

  if (diffHours < 1) return 'Just now'
  if (diffHours < 24) return `${diffHours}h ago`
  return `${Math.floor(diffHours / 24)}d ago`
}

const navigateTo = (routeName) => {
  router.push({ name: routeName })
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.dashboard-stats {
  padding: 24px;
}

/* Enhanced Modern Stats Cards */
.stats-content-modern {
  padding: 32px 24px;
  position: relative;
}

.stats-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.stats-icon-modern {
  opacity: 0.9;
  transition: all var(--q-transition-normal) ease;
}

.stats-trend-modern {
  opacity: 0.7;
  transition: all var(--q-transition-normal) ease;
}

.stats-info-modern {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stats-card-modern:hover .stats-icon-modern {
  opacity: 1;
  transform: scale(1.1);
}

.stats-card-modern:hover .stats-trend-modern {
  opacity: 1;
  transform: scale(1.2);
}

/* Legacy stats card styles for backward compatibility */
.stats-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stats-icon {
  margin-right: 16px;
  opacity: 0.9;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stats-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.stats-change.neutral {
  color: rgba(255, 255, 255, 0.7);
}

.chart-card,
.activity-card,
.quick-actions-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: none;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.chart-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.chart-placeholder {
  text-align: center;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
  border-bottom: none;
}

.quick-action-btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  text-transform: none;
}

/* Dark mode */
.body--dark .chart-title {
  color: #f9fafb;
}

.body--dark .activity-item {
  border-bottom-color: #374151;
}

.body--dark .chart-card,
.body--dark .activity-card,
.body--dark .quick-actions-card {
  background: #2d2d2d;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-stats {
    padding: 16px;
  }

  .stats-content {
    padding: 16px;
  }

  .stats-value {
    font-size: 1.5rem;
  }

  .quick-action-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}
</style>
