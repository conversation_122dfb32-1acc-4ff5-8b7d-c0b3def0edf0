<template>
  <q-layout view="lHh Lpr lFf" class="admin-layout">
    <!-- Enhanced Header -->
    <q-header elevated class="admin-header">
      <q-toolbar class="admin-toolbar">
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
          class="q-mr-sm admin-menu-btn"
        />

        <q-avatar size="36px" class="q-mr-md admin-logo">
          <img src="/logo.png" alt="Daryay Bawar Logo" />
        </q-avatar>

        <q-toolbar-title class="admin-title">
          <div class="admin-title-content">
            <span class="admin-title-main">Dar<PERSON><PERSON>war</span>
            <span class="admin-title-sub">Admin Panel</span>
          </div>
        </q-toolbar-title>

        <q-space />

        <!-- Enhanced Search -->
        <q-input
          v-model="searchQuery"
          dense
          standout="bg-grey-1"
          placeholder="Search anything..."
          class="admin-search q-mr-lg"
          style="width: 320px"
        >
          <template v-slot:prepend>
            <q-icon name="search" color="grey-6" />
          </template>
          <template v-slot:append>
            <q-btn
              flat
              dense
              round
              icon="tune"
              size="sm"
              color="grey-6"
            >
              <q-tooltip>Advanced Search</q-tooltip>
            </q-btn>
          </template>
        </q-input>

        <!-- Notifications -->
        <q-btn flat round dense icon="notifications" class="q-mr-sm">
          <q-badge color="red" floating>3</q-badge>
          <q-menu>
            <q-list style="min-width: 300px">
              <q-item-label header>Notifications</q-item-label>
              <q-item clickable v-close-popup>
                <q-item-section avatar>
                  <q-icon name="currency_exchange" color="primary" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>New exchange rate updated</q-item-label>
                  <q-item-label caption>USD/IQD rate changed</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>

        <!-- User Menu -->
        <q-btn flat round dense class="admin-user-btn">
          <q-avatar size="32px">
            <img src="https://cdn.quasar.dev/img/avatar.png" />
          </q-avatar>
          <q-menu>
            <q-list style="min-width: 200px">
              <q-item clickable>
                <q-item-section avatar>
                  <q-icon name="person" />
                </q-item-section>
                <q-item-section>Profile</q-item-section>
              </q-item>
              <q-item clickable>
                <q-item-section avatar>
                  <q-icon name="settings" />
                </q-item-section>
                <q-item-section>Settings</q-item-section>
              </q-item>
              <q-separator />
              <q-item clickable>
                <q-item-section avatar>
                  <q-icon name="logout" />
                </q-item-section>
                <q-item-section>Logout</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </q-toolbar>
    </q-header>

    <!-- Left Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      class="admin-drawer"
      :width="280"
    >
      <q-scroll-area class="fit">
        <q-list class="admin-nav">
          <!-- Dashboard -->
          <q-item
            clickable
            :active="route.name === 'admin.dashboard'"
            active-class="admin-nav-active"
            @click="navigateTo('admin.dashboard')"
            class="admin-nav-item"
          >
            <q-item-section avatar>
              <q-icon name="dashboard" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Dashboard</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Currency Management -->
          <q-expansion-item
            icon="currency_exchange"
            label="Currency Management"
            :default-opened="route.name?.includes('currencies')"
            class="admin-nav-expansion"
          >
            <q-item
              clickable
              :active="route.name === 'admin.currencies.index'"
              active-class="admin-nav-active"
              @click="navigateTo('admin.currencies.index')"
              class="admin-nav-subitem"
            >
              <q-item-section avatar>
                <q-icon name="list" />
              </q-item-section>
              <q-item-section>
                <q-item-label>All Currencies</q-item-label>
              </q-item-section>
            </q-item>

            <q-item
              clickable
              :active="route.name === 'admin.currencies.create'"
              active-class="admin-nav-active"
              @click="navigateTo('admin.currencies.create')"
              class="admin-nav-subitem"
            >
              <q-item-section avatar>
                <q-icon name="add" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Add Currency</q-item-label>
              </q-item-section>
            </q-item>
          </q-expansion-item>

          <!-- Exchange Rates -->
          <q-expansion-item
            icon="trending_up"
            label="Exchange Rates"
            :default-opened="route.name?.includes('exchange')"
            class="admin-nav-expansion"
          >
            <q-item
              clickable
              :active="route.name === 'admin.currency-exchange-rates.index'"
              active-class="admin-nav-active"
              @click="navigateTo('admin.currency-exchange-rates.index')"
              class="admin-nav-subitem"
            >
              <q-item-section avatar>
                <q-icon name="swap_horiz" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Current Rates</q-item-label>
              </q-item-section>
            </q-item>

            <q-item
              clickable
              :active="route.name === 'admin.exchange-rate-history.index'"
              active-class="admin-nav-active"
              @click="navigateTo('admin.exchange-rate-history.index')"
              class="admin-nav-subitem"
            >
              <q-item-section avatar>
                <q-icon name="history" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Rate History</q-item-label>
              </q-item-section>
            </q-item>
          </q-expansion-item>

          <!-- Analytics -->
          <q-item
            clickable
            class="admin-nav-item"
          >
            <q-item-section avatar>
              <q-icon name="analytics" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Analytics</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Settings -->
          <q-item
            clickable
            class="admin-nav-item"
          >
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Settings</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Page Content -->
    <q-page-container class="admin-content">
      <slot />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute() // Used in template for navigation active states

const leftDrawerOpen = ref(false)
const searchQuery = ref('')

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

const navigateTo = (routeName) => {
  router.push({ name: routeName })
}
</script>

<style scoped>
.admin-layout {
  background: var(--q-admin-bg);
  min-height: 100vh;
}

.admin-header {
  background: var(--q-admin-gradient-primary);
  box-shadow: var(--q-admin-shadow);
  border: none;
}

.admin-toolbar {
  padding: 0 32px;
  height: 72px;
}

.admin-menu-btn {
  transition: all var(--q-transition-normal) ease;
}

.admin-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.admin-logo {
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all var(--q-transition-normal) ease;
}

.admin-logo:hover {
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
}

.admin-title-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.admin-title-main {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  line-height: 1.2;
}

.admin-title-sub {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-search {
  border-radius: var(--q-radius-lg);
  transition: all var(--q-transition-normal) ease;
}

.admin-search:hover {
  transform: scale(1.02);
}

.admin-search :deep(.q-field__control) {
  border-radius: var(--q-radius-lg);
  background: rgba(255, 255, 255, 0.95);
}

.admin-notification-btn,
.admin-user-btn {
  transition: all var(--q-transition-normal) ease;
}

.admin-notification-btn:hover,
.admin-user-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.admin-user-avatar {
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all var(--q-transition-normal) ease;
}

.admin-user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.6);
}

.admin-drawer {
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
}

.admin-nav {
  padding: 16px 0;
}

.admin-nav-item {
  margin: 4px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.admin-nav-item:hover {
  background: #f3f4f6;
}

.admin-nav-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

.admin-nav-active .q-icon {
  color: white !important;
}

.admin-nav-subitem {
  margin: 2px 32px 2px 48px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.admin-nav-subitem:hover {
  background: #f9fafb;
}

.admin-nav-expansion :deep(.q-expansion-item__container) {
  margin: 4px 16px;
  border-radius: 8px;
}

.admin-content {
  background: #f8fafc;
  min-height: calc(100vh - 64px);
}

.admin-user-btn {
  border-radius: 50%;
  transition: all 0.2s ease;
}

.admin-user-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Dark mode support */
.body--dark .admin-layout {
  background: #1a1a1a;
}

.body--dark .admin-drawer {
  background: #2d2d2d;
  border-right-color: #404040;
}

.body--dark .admin-nav-item:hover {
  background: #404040;
}

.body--dark .admin-nav-subitem:hover {
  background: #353535;
}

.body--dark .admin-content {
  background: #1a1a1a;
}
</style>
