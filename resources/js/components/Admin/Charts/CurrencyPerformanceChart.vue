<template>
  <div class="currency-performance-chart">
    <div class="chart-header">
      <h6 class="chart-title">Currency Performance</h6>
      <div class="chart-controls">
        <q-select
          v-model="selectedMetric"
          :options="metricOptions"
          @update:model-value="loadChartData"
          outlined
          dense
          style="min-width: 120px"
        />
      </div>
    </div>
    
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <div v-if="loading" class="chart-loading">
      <q-spinner-dots size="2rem" color="primary" />
      <div class="q-mt-sm">Loading performance data...</div>
    </div>
    
    <!-- Performance Summary -->
    <div class="performance-summary">
      <div class="summary-item" v-for="item in performanceSummary" :key="item.currency">
        <div class="summary-flag">
          <CountryFlag
            :code="item.currency"
            size="small"
            :shadow="false"
          />
        </div>
        <div class="summary-info">
          <div class="summary-currency">{{ item.currency }}</div>
          <div class="summary-change" :class="item.changeClass">
            <q-icon :name="item.changeIcon" size="14px" />
            {{ item.change }}
          </div>
        </div>
        <div class="summary-value">
          {{ item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import CountryFlag from '@/components/CountryFlag.vue'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

const props = defineProps({
  height: {
    type: Number,
    default: 250
  }
})

// Reactive data
const chartCanvas = ref(null)
const chartContainer = ref(null)
const chart = ref(null)
const loading = ref(false)
const selectedMetric = ref('volatility')
const performanceSummary = ref([])

const metricOptions = [
  { label: 'Volatility', value: 'volatility' },
  { label: 'Volume', value: 'volume' },
  { label: 'Change %', value: 'change' }
]

// Chart configuration
const getChartConfig = (data) => ({
  type: 'bar',
  data: {
    labels: data.labels || [],
    datasets: [{
      label: data.label || 'Performance',
      data: data.values || [],
      backgroundColor: data.colors || [],
      borderColor: data.borderColors || [],
      borderWidth: 1,
      borderRadius: 8,
      borderSkipped: false,
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          title: (context) => {
            return `${context[0].label}`
          },
          label: (context) => {
            const suffix = selectedMetric.value === 'volatility' ? '%' :
                          selectedMetric.value === 'volume' ? 'M' : '%'
            return `${data.label}: ${context.parsed.y.toFixed(2)}${suffix}`
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280'
        }
      },
      y: {
        display: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          drawBorder: false
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280',
          callback: function(value) {
            const suffix = selectedMetric.value === 'volatility' ? '%' :
                          selectedMetric.value === 'volume' ? 'M' : '%'
            return value.toFixed(1) + suffix
          }
        }
      }
    }
  }
})

// Methods
const loadChartData = async () => {
  loading.value = true
  
  try {
    const data = await generateMockPerformanceData()
    
    if (chart.value) {
      updateChart(data)
    } else {
      await nextTick()
      createChart(data)
    }
    
    generatePerformanceSummary(data)
  } catch (error) {
    console.error('Failed to load performance data:', error)
  } finally {
    loading.value = false
  }
}

const generateMockPerformanceData = async () => {
  const currencies = ['USD', 'EUR', 'GBP', 'TRY', 'IRR', 'SAR']
  const labels = currencies
  const values = []
  const colors = []
  const borderColors = []
  
  // Color palette
  const colorPalette = [
    { bg: 'rgba(102, 126, 234, 0.8)', border: '#667eea' },
    { bg: 'rgba(79, 172, 254, 0.8)', border: '#4facfe' },
    { bg: 'rgba(240, 147, 251, 0.8)', border: '#f093fb' },
    { bg: 'rgba(255, 236, 210, 0.8)', border: '#ffecd2' },
    { bg: 'rgba(168, 237, 234, 0.8)', border: '#a8edea' },
    { bg: 'rgba(255, 183, 197, 0.8)', border: '#ffb7c5' }
  ]
  
  currencies.forEach((currency, index) => {
    let value
    
    switch (selectedMetric.value) {
      case 'volatility':
        value = Math.random() * 5 + 0.5 // 0.5% to 5.5%
        break
      case 'volume':
        value = Math.random() * 100 + 10 // 10M to 110M
        break
      case 'change':
        value = (Math.random() - 0.5) * 10 // -5% to +5%
        break
      default:
        value = Math.random() * 100
    }
    
    values.push(value)
    colors.push(colorPalette[index].bg)
    borderColors.push(colorPalette[index].border)
  })
  
  const metricLabel = metricOptions.find(m => m.value === selectedMetric.value)?.label || 'Performance'
  
  return {
    labels,
    values,
    colors,
    borderColors,
    label: metricLabel
  }
}

const generatePerformanceSummary = (data) => {
  performanceSummary.value = data.labels.map((currency, index) => {
    const value = data.values[index]
    const isPositive = selectedMetric.value === 'change' ? value > 0 : true
    
    let formattedValue
    let change
    
    switch (selectedMetric.value) {
      case 'volatility':
        formattedValue = `${value.toFixed(2)}%`
        change = value > 3 ? 'High' : value > 1.5 ? 'Medium' : 'Low'
        break
      case 'volume':
        formattedValue = `${value.toFixed(1)}M`
        change = value > 70 ? 'High' : value > 40 ? 'Medium' : 'Low'
        break
      case 'change':
        formattedValue = `${value > 0 ? '+' : ''}${value.toFixed(2)}%`
        change = `${Math.abs(value).toFixed(2)}%`
        break
      default:
        formattedValue = value.toFixed(2)
        change = 'N/A'
    }
    
    return {
      currency,
      value: formattedValue,
      change,
      changeClass: isPositive && selectedMetric.value === 'change' ? 'positive' : 
                   !isPositive && selectedMetric.value === 'change' ? 'negative' : 'neutral',
      changeIcon: isPositive && selectedMetric.value === 'change' ? 'trending_up' : 
                  !isPositive && selectedMetric.value === 'change' ? 'trending_down' : 'remove'
    }
  })
}

const createChart = (data) => {
  if (!chartCanvas.value) return
  
  const ctx = chartCanvas.value.getContext('2d')
  chart.value = new ChartJS(ctx, getChartConfig(data))
}

const updateChart = (data) => {
  if (!chart.value) return
  
  chart.value.data.labels = data.labels
  chart.value.data.datasets[0].data = data.values
  chart.value.data.datasets[0].backgroundColor = data.colors
  chart.value.data.datasets[0].borderColor = data.borderColors
  chart.value.data.datasets[0].label = data.label
  chart.value.update('active')
}

const destroyChart = () => {
  if (chart.value) {
    chart.value.destroy()
    chart.value = null
  }
}

// Watchers
watch(selectedMetric, () => {
  loadChartData()
})

// Lifecycle
onMounted(() => {
  loadChartData()
})

onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
.currency-performance-chart {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.chart-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.chart-container {
  position: relative;
  padding: 24px;
  height: 250px;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  font-size: 0.875rem;
}

.performance-summary {
  padding: 16px 24px 24px;
  border-top: 1px solid #f3f4f6;
}

.summary-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  gap: 12px;
}

.summary-flag {
  flex-shrink: 0;
}

.summary-info {
  flex: 1;
}

.summary-currency {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.summary-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}

.summary-change.positive {
  color: #059669;
}

.summary-change.negative {
  color: #dc2626;
}

.summary-change.neutral {
  color: #6b7280;
}

.summary-value {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

/* Dark mode */
.body--dark .currency-performance-chart {
  background: #2d2d2d;
  border-color: #404040;
}

.body--dark .chart-header,
.body--dark .performance-summary {
  border-color: #404040;
}

.body--dark .chart-title,
.body--dark .summary-currency,
.body--dark .summary-value {
  color: #f9fafb;
}

.body--dark .chart-loading {
  background: rgba(45, 45, 45, 0.9);
  color: #d1d5db;
}

/* Responsive */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .chart-container {
    padding: 16px;
    height: 200px;
  }
  
  .performance-summary {
    padding: 12px 16px 16px;
  }
}
</style>
