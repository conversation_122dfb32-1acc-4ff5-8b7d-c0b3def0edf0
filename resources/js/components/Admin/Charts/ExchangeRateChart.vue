<template>
  <div class="exchange-rate-chart">
    <div class="chart-header">
      <h6 class="chart-title">Exchange Rate Trends</h6>
      <div class="chart-controls">
        <q-btn-toggle
          v-model="selectedPeriod"
          :options="periodOptions"
          @update:model-value="loadChartData"
          color="primary"
          outline
          size="sm"
          no-caps
        />
        <q-btn
          flat
          round
          dense
          icon="refresh"
          @click="loadChartData"
          :loading="loading"
          size="sm"
        />
      </div>
    </div>
    
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chartCanvas"></canvas>
    </div>
    
    <div v-if="loading" class="chart-loading">
      <q-spinner-dots size="2rem" color="primary" />
      <div class="q-mt-sm">Loading chart data...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import axios from 'axios'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

const props = defineProps({
  height: {
    type: Number,
    default: 300
  },
  currencies: {
    type: Array,
    default: () => ['USD', 'EUR', 'GBP']
  }
})

// Reactive data
const chartCanvas = ref(null)
const chartContainer = ref(null)
const chart = ref(null)
const loading = ref(false)
const selectedPeriod = ref('7d')
const chartData = ref([])

const periodOptions = [
  { label: '7 Days', value: '7d' },
  { label: '30 Days', value: '30d' },
  { label: '90 Days', value: '90d' },
  { label: '1 Year', value: '1y' }
]

// Chart configuration
const getChartConfig = (data) => ({
  type: 'line',
  data: {
    labels: data.labels || [],
    datasets: data.datasets || []
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      title: {
        display: false
      },
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: (context) => {
            return `Date: ${context[0].label}`
          },
          label: (context) => {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(4)}`
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          drawBorder: false
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280'
        }
      },
      y: {
        display: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          drawBorder: false
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280',
          callback: function(value) {
            return value.toFixed(4)
          }
        }
      }
    },
    elements: {
      line: {
        tension: 0.4,
        borderWidth: 2
      },
      point: {
        radius: 3,
        hoverRadius: 6,
        borderWidth: 2,
        backgroundColor: 'white'
      }
    }
  }
})

// Color palette for different currencies
const colorPalette = [
  { border: '#667eea', background: 'rgba(102, 126, 234, 0.1)' },
  { border: '#4facfe', background: 'rgba(79, 172, 254, 0.1)' },
  { border: '#f093fb', background: 'rgba(240, 147, 251, 0.1)' },
  { border: '#ffecd2', background: 'rgba(255, 236, 210, 0.1)' },
  { border: '#a8edea', background: 'rgba(168, 237, 234, 0.1)' }
]

// Methods
const loadChartData = async () => {
  loading.value = true
  
  try {
    // In a real app, this would call your API
    const response = await generateMockData()
    chartData.value = response
    
    if (chart.value) {
      updateChart(response)
    } else {
      await nextTick()
      createChart(response)
    }
  } catch (error) {
    console.error('Failed to load chart data:', error)
  } finally {
    loading.value = false
  }
}

const generateMockData = async () => {
  // Generate mock data for demonstration
  const days = selectedPeriod.value === '7d' ? 7 : 
               selectedPeriod.value === '30d' ? 30 : 
               selectedPeriod.value === '90d' ? 90 : 365
  
  const labels = []
  const datasets = []
  
  // Generate date labels
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    labels.push(date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }))
  }
  
  // Generate datasets for each currency
  props.currencies.forEach((currency, index) => {
    const baseRate = currency === 'USD' ? 1320 : 
                     currency === 'EUR' ? 1450 : 
                     currency === 'GBP' ? 1680 : 1000
    
    const data = []
    let currentRate = baseRate
    
    for (let i = 0; i < days; i++) {
      // Add some realistic volatility
      const change = (Math.random() - 0.5) * (baseRate * 0.02)
      currentRate += change
      data.push(currentRate)
    }
    
    const colors = colorPalette[index % colorPalette.length]
    
    datasets.push({
      label: `${currency}/IQD`,
      data: data,
      borderColor: colors.border,
      backgroundColor: colors.background,
      fill: true,
      tension: 0.4
    })
  })
  
  return { labels, datasets }
}

const createChart = (data) => {
  if (!chartCanvas.value) return
  
  const ctx = chartCanvas.value.getContext('2d')
  chart.value = new ChartJS(ctx, getChartConfig(data))
}

const updateChart = (data) => {
  if (!chart.value) return
  
  chart.value.data.labels = data.labels
  chart.value.data.datasets = data.datasets
  chart.value.update('active')
}

const destroyChart = () => {
  if (chart.value) {
    chart.value.destroy()
    chart.value = null
  }
}

// Watchers
watch(selectedPeriod, () => {
  loadChartData()
})

// Lifecycle
onMounted(() => {
  loadChartData()
})

onUnmounted(() => {
  destroyChart()
})
</script>

<style scoped>
.exchange-rate-chart {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f3f4f6;
}

.chart-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  position: relative;
  padding: 24px;
  height: 300px;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  font-size: 0.875rem;
}

/* Dark mode */
.body--dark .exchange-rate-chart {
  background: #2d2d2d;
  border-color: #404040;
}

.body--dark .chart-header {
  border-bottom-color: #404040;
}

.body--dark .chart-title {
  color: #f9fafb;
}

.body--dark .chart-loading {
  background: rgba(45, 45, 45, 0.9);
  color: #d1d5db;
}

/* Responsive */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .chart-controls {
    justify-content: center;
  }
  
  .chart-container {
    padding: 16px;
    height: 250px;
  }
}
</style>
