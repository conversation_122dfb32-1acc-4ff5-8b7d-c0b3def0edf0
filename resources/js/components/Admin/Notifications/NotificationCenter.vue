<template>
  <div class="notification-center">
    <q-btn
      flat
      round
      dense
      icon="notifications"
      class="notification-btn"
      @click="toggleNotifications"
    >
      <q-badge 
        v-if="unreadCount > 0" 
        color="red" 
        floating
        :label="unreadCount > 99 ? '99+' : unreadCount"
      />
      
      <q-menu
        v-model="showNotifications"
        anchor="bottom right"
        self="top right"
        :offset="[0, 8]"
        class="notification-menu"
      >
        <q-card class="notification-card">
          <q-card-section class="notification-header">
            <div class="header-content">
              <h6 class="header-title">Notifications</h6>
              <div class="header-actions">
                <q-btn
                  flat
                  round
                  dense
                  icon="done_all"
                  size="sm"
                  @click="markAllAsRead"
                  :disable="unreadCount === 0"
                >
                  <q-tooltip>Mark all as read</q-tooltip>
                </q-btn>
                <q-btn
                  flat
                  round
                  dense
                  icon="settings"
                  size="sm"
                  @click="openSettings"
                >
                  <q-tooltip>Notification settings</q-tooltip>
                </q-btn>
              </div>
            </div>
          </q-card-section>

          <q-separator />

          <div class="notification-filters">
            <q-btn-toggle
              v-model="activeFilter"
              :options="filterOptions"
              @update:model-value="filterNotifications"
              color="primary"
              outline
              size="sm"
              no-caps
            />
          </div>

          <q-scroll-area class="notification-list" style="height: 400px;">
            <div v-if="filteredNotifications.length === 0" class="empty-state">
              <q-icon name="notifications_none" size="48px" color="grey-4" />
              <div class="empty-text">No notifications</div>
            </div>
            
            <div
              v-for="notification in filteredNotifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-icon">
                <q-icon
                  :name="getNotificationIcon(notification.type)"
                  :color="getNotificationColor(notification.type)"
                  size="20px"
                />
              </div>
              
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.message }}</div>
                <div class="notification-time">{{ formatTime(notification.created_at) }}</div>
              </div>
              
              <div class="notification-actions">
                <q-btn
                  flat
                  round
                  dense
                  icon="close"
                  size="xs"
                  @click.stop="dismissNotification(notification.id)"
                />
              </div>
            </div>
          </q-scroll-area>

          <q-separator />

          <q-card-actions class="notification-footer">
            <q-btn
              flat
              label="View All"
              color="primary"
              @click="viewAllNotifications"
              no-caps
            />
            <q-space />
            <q-btn
              flat
              label="Clear All"
              color="negative"
              @click="clearAllNotifications"
              no-caps
            />
          </q-card-actions>
        </q-card>
      </q-menu>
    </q-btn>

    <!-- Notification Toast -->
    <div
      v-for="toast in toastNotifications"
      :key="toast.id"
      class="notification-toast"
      :class="`toast-${toast.type}`"
    >
      <div class="toast-icon">
        <q-icon
          :name="getNotificationIcon(toast.type)"
          size="20px"
        />
      </div>
      <div class="toast-content">
        <div class="toast-title">{{ toast.title }}</div>
        <div class="toast-message">{{ toast.message }}</div>
      </div>
      <q-btn
        flat
        round
        dense
        icon="close"
        size="sm"
        @click="dismissToast(toast.id)"
        class="toast-close"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Reactive data
const showNotifications = ref(false)
const notifications = ref([])
const toastNotifications = ref([])
const activeFilter = ref('all')
const wsConnection = ref(null)

const filterOptions = [
  { label: 'All', value: 'all' },
  { label: 'System', value: 'system' },
  { label: 'Rates', value: 'rates' },
  { label: 'Users', value: 'users' }
]

// Computed properties
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

const filteredNotifications = computed(() => {
  if (activeFilter.value === 'all') {
    return notifications.value
  }
  return notifications.value.filter(n => n.type === activeFilter.value)
})

// Methods
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
}

const loadNotifications = async () => {
  try {
    // Mock notifications for demonstration
    notifications.value = [
      {
        id: 1,
        type: 'rates',
        title: 'Exchange Rate Updated',
        message: 'USD/IQD rate changed from 1,320 to 1,325',
        created_at: new Date().toISOString(),
        read: false,
        action_url: '/admin/currency-exchange-rates'
      },
      {
        id: 2,
        type: 'system',
        title: 'System Maintenance',
        message: 'Scheduled maintenance will begin at 2:00 AM',
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: false,
        action_url: null
      },
      {
        id: 3,
        type: 'users',
        title: 'New User Registration',
        message: 'John Doe has registered as a new admin user',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: true,
        action_url: '/admin/users'
      }
    ]
  } catch (error) {
    console.error('Failed to load notifications:', error)
  }
}

const markAllAsRead = async () => {
  try {
    notifications.value.forEach(n => n.read = true)
    // In real app, call API to mark as read
  } catch (error) {
    console.error('Failed to mark notifications as read:', error)
  }
}

const dismissNotification = async (id) => {
  try {
    notifications.value = notifications.value.filter(n => n.id !== id)
    // In real app, call API to dismiss notification
  } catch (error) {
    console.error('Failed to dismiss notification:', error)
  }
}

const clearAllNotifications = async () => {
  try {
    notifications.value = []
    // In real app, call API to clear all notifications
  } catch (error) {
    console.error('Failed to clear notifications:', error)
  }
}

const handleNotificationClick = (notification) => {
  // Mark as read
  notification.read = true
  
  // Navigate to action URL if available
  if (notification.action_url) {
    router.push(notification.action_url)
  }
  
  showNotifications.value = false
}

const filterNotifications = () => {
  // Filter logic is handled by computed property
}

const openSettings = () => {
  // Open notification settings
  console.log('Open notification settings')
}

const viewAllNotifications = () => {
  router.push('/admin/notifications')
  showNotifications.value = false
}

const getNotificationIcon = (type) => {
  const icons = {
    system: 'settings',
    rates: 'trending_up',
    users: 'person',
    security: 'security',
    error: 'error',
    warning: 'warning',
    success: 'check_circle',
    info: 'info'
  }
  return icons[type] || 'notifications'
}

const getNotificationColor = (type) => {
  const colors = {
    system: 'primary',
    rates: 'secondary',
    users: 'accent',
    security: 'deep-purple',
    error: 'negative',
    warning: 'warning',
    success: 'positive',
    info: 'info'
  }
  return colors[type] || 'grey'
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffMinutes = Math.floor((now - time) / (1000 * 60))
  
  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  return time.toLocaleDateString()
}

const showToast = (notification) => {
  const toast = {
    ...notification,
    id: Date.now() + Math.random()
  }
  
  toastNotifications.value.push(toast)
  
  // Auto dismiss after 5 seconds
  setTimeout(() => {
    dismissToast(toast.id)
  }, 5000)
}

const dismissToast = (id) => {
  toastNotifications.value = toastNotifications.value.filter(t => t.id !== id)
}

const setupWebSocket = () => {
  // In a real app, connect to WebSocket for real-time notifications
  // wsConnection.value = new WebSocket('ws://localhost:8080/notifications')
  
  // Mock real-time notifications
  setInterval(() => {
    if (Math.random() > 0.8) { // 20% chance every 10 seconds
      const mockNotification = {
        id: Date.now(),
        type: ['rates', 'system', 'users'][Math.floor(Math.random() * 3)],
        title: 'New Notification',
        message: 'This is a real-time notification',
        created_at: new Date().toISOString(),
        read: false
      }
      
      notifications.value.unshift(mockNotification)
      showToast(mockNotification)
    }
  }, 10000)
}

// Lifecycle
onMounted(() => {
  loadNotifications()
  setupWebSocket()
})

onUnmounted(() => {
  if (wsConnection.value) {
    wsConnection.value.close()
  }
})
</script>

<style scoped>
.notification-center {
  position: relative;
}

.notification-btn {
  transition: all 0.2s ease;
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.notification-card {
  width: 400px;
  max-height: 600px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.notification-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f3f4f6;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.header-actions {
  display: flex;
  gap: 4px;
}

.notification-filters {
  padding: 12px 20px;
  border-bottom: 1px solid #f3f4f6;
}

.notification-list {
  background: #fafafa;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background 0.2s ease;
}

.notification-item:hover {
  background: #f9fafb;
}

.notification-item.unread {
  background: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.notification-message {
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
  margin-bottom: 6px;
}

.notification-time {
  color: #9ca3af;
  font-size: 0.75rem;
}

.notification-actions {
  margin-left: 8px;
}

.notification-footer {
  padding: 12px 20px;
}

.empty-state {
  text-align: center;
  padding: 48px 20px;
  color: #9ca3af;
}

.empty-text {
  margin-top: 12px;
  font-size: 0.875rem;
}

/* Toast Notifications */
.notification-toast {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 350px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin-bottom: 12px;
  z-index: 9999;
  animation: slideIn 0.3s ease;
  border-left: 4px solid #3b82f6;
}

.toast-rates {
  border-left-color: #10b981;
}

.toast-system {
  border-left-color: #f59e0b;
}

.toast-users {
  border-left-color: #8b5cf6;
}

.toast-error {
  border-left-color: #ef4444;
}

.toast-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.toast-message {
  color: #6b7280;
  font-size: 0.8rem;
  line-height: 1.4;
}

.toast-close {
  margin-left: 8px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Dark mode */
.body--dark .notification-card {
  background: #2d2d2d;
}

.body--dark .notification-header {
  border-bottom-color: #404040;
}

.body--dark .header-title,
.body--dark .notification-title {
  color: #f9fafb;
}

.body--dark .notification-filters {
  border-bottom-color: #404040;
}

.body--dark .notification-list {
  background: #1f1f1f;
}

.body--dark .notification-item {
  border-bottom-color: #404040;
}

.body--dark .notification-item:hover {
  background: #374151;
}

.body--dark .notification-item.unread {
  background: #1e3a8a;
}

.body--dark .notification-toast {
  background: #2d2d2d;
}

/* Responsive */
@media (max-width: 480px) {
  .notification-card {
    width: 320px;
  }
  
  .notification-toast {
    width: 300px;
    right: 10px;
  }
}
</style>
