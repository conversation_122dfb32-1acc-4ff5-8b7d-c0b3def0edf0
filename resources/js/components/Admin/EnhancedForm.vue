<template>
  <div class="enhanced-form">
    <!-- Form Header -->
    <div class="form-header" v-if="showHeader">
      <div class="form-header-content">
        <div class="form-title-section">
          <h2 class="form-title">{{ title }}</h2>
          <p class="form-subtitle" v-if="subtitle">{{ subtitle }}</p>
        </div>
        
        <div class="form-actions" v-if="showHeaderActions">
          <slot name="header-actions" />
        </div>
      </div>
    </div>

    <!-- Form Content -->
    <q-form @submit="onSubmit" @reset="onReset" class="enhanced-form-content">
      <div class="form-sections">
        <!-- Dynamic Form Sections -->
        <div
          v-for="(section, sectionIndex) in sections"
          :key="sectionIndex"
          class="form-section"
        >
          <!-- Section Header -->
          <div class="section-header" v-if="section.title">
            <h3 class="section-title">{{ section.title }}</h3>
            <p class="section-description" v-if="section.description">
              {{ section.description }}
            </p>
          </div>

          <!-- Section Fields -->
          <div class="section-fields" :class="section.layout || 'grid-2'">
            <div
              v-for="field in section.fields"
              :key="field.name"
              class="field-wrapper"
              :class="field.class"
            >
              <!-- Text Input -->
              <q-input
                v-if="field.type === 'text' || field.type === 'email' || field.type === 'password'"
                v-model="formData[field.name]"
                :type="field.type"
                :label="field.label"
                :placeholder="field.placeholder"
                :hint="field.hint"
                :rules="field.rules"
                :readonly="field.readonly"
                :disable="field.disable"
                :required="field.required"
                outlined
                class="enhanced-input"
              >
                <template v-if="field.prepend" v-slot:prepend>
                  <q-icon :name="field.prepend" />
                </template>
                <template v-if="field.append" v-slot:append>
                  <q-icon :name="field.append" />
                </template>
              </q-input>

              <!-- Textarea -->
              <q-input
                v-else-if="field.type === 'textarea'"
                v-model="formData[field.name]"
                type="textarea"
                :label="field.label"
                :placeholder="field.placeholder"
                :hint="field.hint"
                :rules="field.rules"
                :readonly="field.readonly"
                :disable="field.disable"
                :required="field.required"
                :rows="field.rows || 4"
                outlined
                class="enhanced-textarea"
              />

              <!-- Select -->
              <q-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.name]"
                :options="field.options"
                :label="field.label"
                :hint="field.hint"
                :rules="field.rules"
                :readonly="field.readonly"
                :disable="field.disable"
                :required="field.required"
                :multiple="field.multiple"
                :use-chips="field.multiple"
                outlined
                emit-value
                map-options
                class="enhanced-select"
              >
                <template v-if="field.prepend" v-slot:prepend>
                  <q-icon :name="field.prepend" />
                </template>
              </q-select>

              <!-- Checkbox -->
              <q-checkbox
                v-else-if="field.type === 'checkbox'"
                v-model="formData[field.name]"
                :label="field.label"
                :disable="field.disable"
                class="enhanced-checkbox"
              />

              <!-- Radio Group -->
              <q-option-group
                v-else-if="field.type === 'radio'"
                v-model="formData[field.name]"
                :options="field.options"
                :label="field.label"
                :disable="field.disable"
                type="radio"
                class="enhanced-radio-group"
              />

              <!-- Date Input -->
              <q-input
                v-else-if="field.type === 'date'"
                v-model="formData[field.name]"
                :label="field.label"
                :hint="field.hint"
                :rules="field.rules"
                :readonly="field.readonly"
                :disable="field.disable"
                :required="field.required"
                outlined
                class="enhanced-date"
              >
                <template v-slot:append>
                  <q-icon name="event" class="cursor-pointer">
                    <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                      <q-date v-model="formData[field.name]" mask="YYYY-MM-DD">
                        <div class="row items-center justify-end">
                          <q-btn v-close-popup label="Close" color="primary" flat />
                        </div>
                      </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>

              <!-- File Upload -->
              <q-file
                v-else-if="field.type === 'file'"
                v-model="formData[field.name]"
                :label="field.label"
                :hint="field.hint"
                :rules="field.rules"
                :disable="field.disable"
                :required="field.required"
                :accept="field.accept"
                :multiple="field.multiple"
                outlined
                class="enhanced-file"
              >
                <template v-slot:prepend>
                  <q-icon name="attach_file" />
                </template>
              </q-file>

              <!-- Custom Field Slot -->
              <div v-else-if="field.type === 'custom'" class="custom-field">
                <slot :name="`field-${field.name}`" :field="field" :value="formData[field.name]" />
              </div>
            </div>
          </div>
        </div>

        <!-- Default Slot for Custom Content -->
        <slot :formData="formData" />
      </div>

      <!-- Form Footer -->
      <div class="form-footer">
        <div class="form-footer-content">
          <div class="form-footer-left">
            <slot name="footer-left" />
          </div>
          
          <div class="form-footer-right">
            <q-btn
              v-if="showReset"
              type="reset"
              color="grey-7"
              outline
              :label="resetLabel"
              class="q-mr-sm"
            />
            
            <q-btn
              v-if="showCancel"
              color="grey-7"
              outline
              :label="cancelLabel"
              class="q-mr-sm"
              @click="onCancel"
            />
            
            <q-btn
              type="submit"
              :color="submitColor"
              :label="submitLabel"
              :loading="loading"
              :disable="!isFormValid"
              class="enhanced-submit-btn"
            />
          </div>
        </div>
      </div>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  sections: {
    type: Array,
    required: true
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showHeaderActions: {
    type: Boolean,
    default: false
  },
  showReset: {
    type: Boolean,
    default: true
  },
  showCancel: {
    type: Boolean,
    default: true
  },
  submitLabel: {
    type: String,
    default: 'Save'
  },
  resetLabel: {
    type: String,
    default: 'Reset'
  },
  cancelLabel: {
    type: String,
    default: 'Cancel'
  },
  submitColor: {
    type: String,
    default: 'primary'
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'reset', 'cancel'])

// Reactive data
const formData = ref({ ...props.modelValue })

// Computed properties
const isFormValid = computed(() => {
  // Basic validation - can be enhanced
  return true
})

// Methods
const onSubmit = () => {
  emit('submit', formData.value)
}

const onReset = () => {
  formData.value = { ...props.modelValue }
  emit('reset')
}

const onCancel = () => {
  emit('cancel')
}

// Watchers
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  formData.value = { ...newValue }
}, { deep: true })

// Lifecycle
onMounted(() => {
  // Initialize form data
  props.sections.forEach(section => {
    section.fields.forEach(field => {
      if (!(field.name in formData.value)) {
        formData.value[field.name] = field.default || null
      }
    })
  })
})
</script>

<style scoped>
.enhanced-form {
  background: var(--q-admin-surface);
  border-radius: var(--q-radius-xl);
  box-shadow: var(--q-admin-shadow);
  overflow: hidden;
}

.form-header {
  padding: 32px 32px 0 32px;
  border-bottom: 1px solid var(--q-admin-border);
  margin-bottom: 32px;
}

.form-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 24px;
}

.form-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--q-admin-text);
  margin: 0 0 8px 0;
}

.form-subtitle {
  font-size: 1rem;
  color: var(--q-admin-text-light);
  margin: 0;
}

.enhanced-form-content {
  padding: 0 32px;
}

.form-section {
  margin-bottom: 48px;
}

.section-header {
  margin-bottom: 24px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--q-admin-text);
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 0.875rem;
  color: var(--q-admin-text-light);
  margin: 0;
}

.section-fields {
  display: grid;
  gap: 24px;
}

.section-fields.grid-1 {
  grid-template-columns: 1fr;
}

.section-fields.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.section-fields.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.field-wrapper {
  display: flex;
  flex-direction: column;
}

.enhanced-input,
.enhanced-textarea,
.enhanced-select,
.enhanced-date,
.enhanced-file {
  border-radius: var(--q-radius-lg);
}

.enhanced-submit-btn {
  border-radius: var(--q-radius-lg);
  padding: 12px 32px;
  font-weight: 600;
  text-transform: none;
}

.form-footer {
  padding: 32px;
  border-top: 1px solid var(--q-admin-border);
  margin-top: 32px;
  background: var(--q-admin-bg);
}

.form-footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
  .enhanced-form-content {
    padding: 0 24px;
  }
  
  .form-header {
    padding: 24px 24px 0 24px;
  }
  
  .form-footer {
    padding: 24px;
  }
  
  .section-fields.grid-2,
  .section-fields.grid-3 {
    grid-template-columns: 1fr;
  }
  
  .form-header-content,
  .form-footer-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
