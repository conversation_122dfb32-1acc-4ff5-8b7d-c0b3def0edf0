<template>
  <div class="enhanced-data-table">
    <!-- Table Header with Actions -->
    <div class="table-header" v-if="showHeader">
      <div class="table-header-content">
        <div class="table-title-section">
          <h3 class="table-title">{{ title }}</h3>
          <p class="table-subtitle" v-if="subtitle">{{ subtitle }}</p>
        </div>
        
        <div class="table-actions">
          <!-- Search -->
          <q-input
            v-if="searchable"
            v-model="searchQuery"
            dense
            standout="bg-grey-1"
            placeholder="Search..."
            class="table-search"
            style="width: 280px"
          >
            <template v-slot:prepend>
              <q-icon name="search" color="grey-6" />
            </template>
            <template v-slot:append v-if="searchQuery">
              <q-btn
                flat
                dense
                round
                icon="clear"
                size="sm"
                @click="searchQuery = ''"
              />
            </template>
          </q-input>

          <!-- Filters -->
          <q-btn
            v-if="filterable"
            flat
            dense
            icon="filter_list"
            label="Filters"
            class="q-ml-sm"
            @click="showFilters = !showFilters"
          >
            <q-badge v-if="activeFiltersCount > 0" color="primary" floating>
              {{ activeFiltersCount }}
            </q-badge>
          </q-btn>

          <!-- Export -->
          <q-btn
            v-if="exportable"
            flat
            dense
            icon="download"
            label="Export"
            class="q-ml-sm"
            @click="exportData"
          />

          <!-- Bulk Actions -->
          <q-btn
            v-if="bulkActions && selectedRows.length > 0"
            flat
            dense
            icon="more_vert"
            :label="`${selectedRows.length} selected`"
            class="q-ml-sm"
          >
            <q-menu>
              <q-list>
                <q-item
                  v-for="action in bulkActions"
                  :key="action.name"
                  clickable
                  v-close-popup
                  @click="handleBulkAction(action)"
                >
                  <q-item-section avatar>
                    <q-icon :name="action.icon" :color="action.color" />
                  </q-item-section>
                  <q-item-section>{{ action.label }}</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>

          <!-- Custom Actions -->
          <slot name="actions" />
        </div>
      </div>

      <!-- Filters Panel -->
      <div v-if="showFilters && filterable" class="filters-panel">
        <div class="filters-content">
          <slot name="filters" :filters="filters" :updateFilter="updateFilter" />
        </div>
      </div>
    </div>

    <!-- Enhanced Table -->
    <q-table
      ref="tableRef"
      :rows="filteredRows"
      :columns="enhancedColumns"
      :loading="loading"
      :pagination="pagination"
      :selected="selectedRows"
      :selection="selectable ? 'multiple' : 'none'"
      row-key="id"
      class="admin-table enhanced-table"
      flat
      @update:selected="selectedRows = $event"
      @request="onRequest"
    >
      <!-- Custom column templates -->
      <template v-for="column in columns" :key="column.name" v-slot:[`body-cell-${column.name}`]="props">
        <slot :name="`cell-${column.name}`" :props="props" :row="props.row" :value="props.value">
          <q-td :props="props">
            {{ props.value }}
          </q-td>
        </slot>
      </template>

      <!-- Actions column -->
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <div class="row-actions">
            <slot name="row-actions" :row="props.row" />
          </div>
        </q-td>
      </template>

      <!-- Loading state -->
      <template v-slot:loading>
        <q-inner-loading showing color="primary" />
      </template>

      <!-- No data state -->
      <template v-slot:no-data="{ message }">
        <div class="full-width row flex-center text-grey-6 q-gutter-sm">
          <q-icon size="2em" name="sentiment_dissatisfied" />
          <span>{{ message || 'No data available' }}</span>
        </div>
      </template>
    </q-table>

    <!-- Table Footer with Info -->
    <div class="table-footer" v-if="showFooter">
      <div class="table-info">
        <span class="text-grey-6">
          Showing {{ startIndex }} to {{ endIndex }} of {{ totalRows }} entries
        </span>
      </div>
      
      <div class="table-pagination">
        <q-pagination
          v-model="pagination.page"
          :max="maxPages"
          :max-pages="6"
          direction-links
          boundary-links
          @update:model-value="onPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  columns: {
    type: Array,
    required: true
  },
  rows: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  searchable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: true
  },
  exportable: {
    type: Boolean,
    default: true
  },
  selectable: {
    type: Boolean,
    default: true
  },
  bulkActions: {
    type: Array,
    default: () => []
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  serverSide: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['request', 'bulk-action', 'export'])

// Reactive data
const tableRef = ref()
const searchQuery = ref('')
const showFilters = ref(false)
const selectedRows = ref([])
const filters = ref({})

const pagination = ref({
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
})

// Computed properties
const enhancedColumns = computed(() => {
  const cols = [...props.columns]
  
  // Add actions column if not present
  if (!cols.find(col => col.name === 'actions')) {
    cols.push({
      name: 'actions',
      label: 'Actions',
      field: 'actions',
      align: 'right',
      sortable: false,
      style: 'width: 120px'
    })
  }
  
  return cols
})

const filteredRows = computed(() => {
  if (props.serverSide) {
    return props.rows
  }
  
  let filtered = [...props.rows]
  
  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(row => {
      return Object.values(row).some(value => 
        String(value).toLowerCase().includes(query)
      )
    })
  }
  
  // Apply custom filters
  Object.entries(filters.value).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      filtered = filtered.filter(row => {
        if (Array.isArray(value)) {
          return value.includes(row[key])
        }
        return String(row[key]).toLowerCase().includes(String(value).toLowerCase())
      })
    }
  })
  
  return filtered
})

const activeFiltersCount = computed(() => {
  return Object.values(filters.value).filter(value => 
    value !== null && value !== undefined && value !== ''
  ).length
})

const totalRows = computed(() => {
  return props.serverSide ? pagination.value.rowsNumber : filteredRows.value.length
})

const maxPages = computed(() => {
  return Math.ceil(totalRows.value / pagination.value.rowsPerPage)
})

const startIndex = computed(() => {
  return (pagination.value.page - 1) * pagination.value.rowsPerPage + 1
})

const endIndex = computed(() => {
  const end = pagination.value.page * pagination.value.rowsPerPage
  return Math.min(end, totalRows.value)
})

// Methods
const updateFilter = (key, value) => {
  filters.value[key] = value
}

const handleBulkAction = (action) => {
  emit('bulk-action', {
    action: action.name,
    rows: selectedRows.value
  })
}

const exportData = () => {
  emit('export', {
    rows: filteredRows.value,
    columns: props.columns
  })
}

const onRequest = (requestProp) => {
  if (props.serverSide) {
    emit('request', {
      ...requestProp,
      search: searchQuery.value,
      filters: filters.value
    })
  }
}

const onPageChange = (page) => {
  pagination.value.page = page
  if (props.serverSide) {
    onRequest({ pagination: pagination.value })
  }
}

// Watchers
watch(searchQuery, () => {
  if (props.serverSide) {
    pagination.value.page = 1
    onRequest({ pagination: pagination.value })
  }
})

watch(() => filters.value, () => {
  if (props.serverSide) {
    pagination.value.page = 1
    onRequest({ pagination: pagination.value })
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (props.serverSide) {
    onRequest({ pagination: pagination.value })
  }
})
</script>

<style scoped>
.enhanced-data-table {
  background: var(--q-admin-surface);
  border-radius: var(--q-radius-xl);
  box-shadow: var(--q-admin-shadow);
  overflow: hidden;
}

.table-header {
  padding: 24px;
  border-bottom: 1px solid var(--q-admin-border);
  background: var(--q-admin-surface);
}

.table-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.table-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--q-admin-text);
  margin: 0 0 4px 0;
}

.table-subtitle {
  font-size: 0.875rem;
  color: var(--q-admin-text-light);
  margin: 0;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-search {
  border-radius: var(--q-radius-lg);
}

.filters-panel {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--q-admin-border);
}

.enhanced-table {
  border: none;
}

.enhanced-table :deep(.q-table__top) {
  display: none;
}

.enhanced-table :deep(.q-table__bottom) {
  display: none;
}

.row-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid var(--q-admin-border);
  background: var(--q-admin-surface);
}

.table-info {
  font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 768px) {
  .table-header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-actions {
    flex-wrap: wrap;
  }
  
  .table-search {
    width: 100% !important;
  }
  
  .table-footer {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
