import { createI18n } from 'vue-i18n';
import en from './translations/en';
import ar from './translations/ar';
import ku from './translations/ku';
import { mergeTranslations } from './laravel-translations';

export const availableLanguages = [
    {
        code: 'en',
        name: 'English',
        flag: 'https://raw.githubusercontent.com/lipis/flag-icons/master/flags/4x3/us.svg',
        isRTL: false
    },
    {
        code: 'ar',
        name: 'العربية',
        flag: 'https://raw.githubusercontent.com/lipis/flag-icons/master/flags/4x3/iq.svg',
        isRTL: true
    },
    {
        code: 'ku',
        name: 'کوردی',
        flag: 'https://upload.wikimedia.org/wikipedia/commons/3/35/Flag_of_Kurdistan.svg',
        isRTL: true
    }
];

const messages = {
    en,
    ar,
    ku
};

// Get saved language from localStorage or default to English
const savedLanguage = localStorage.getItem('language') || 'en';

// Create i18n instance
const i18n = createI18n({
    legacy: false,
    globalInjection: true,
    locale: savedLanguage,
    fallbackLocale: 'en',
    messages,
    silentTranslationWarn: true,
    missingWarn: false,
    fallbackWarn: true,
    allowComposition: true
});

// Load and merge Laravel translations
mergeTranslations(i18n, savedLanguage);

// Export a function to change language
export async function setLanguage(locale) {
    if (availableLanguages.some(lang => lang.code === locale)) {
        // Update i18n
        i18n.global.locale.value = locale;
        localStorage.setItem('language', locale);

        // Get the language config
        const langConfig = availableLanguages.find(lang => lang.code === locale);

        // Update document attributes
        document.documentElement.lang = locale;
        document.documentElement.dir = langConfig.isRTL ? 'rtl' : 'ltr';

        // Update body classes for RTL/LTR styling
        document.body.classList.remove('rtl', 'ltr');
        document.body.classList.add(langConfig.isRTL ? 'rtl' : 'ltr');

        // Apply RTL styles with a small delay to ensure they take effect
        setTimeout(() => {
            // Force application of RTL styles
            if (langConfig.isRTL) {
                document.querySelector('html').style.direction = 'rtl';
                document.querySelector('html').style.textAlign = 'right';
            } else {
                document.querySelector('html').style.direction = 'ltr';
                document.querySelector('html').style.textAlign = 'left';
            }
        }, 0);

        // Merge Laravel translations
        await mergeTranslations(i18n, locale);

        // Force a reactive update
        i18n.global.mergeLocaleMessage(locale, {});

        // Dispatch a custom event for language change
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { locale, isRTL: langConfig.isRTL }
        }));
    }
}

// Export the useI18n function for composition API
export const useI18n = () => i18n.global;

export default i18n;
