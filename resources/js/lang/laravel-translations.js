import axios from 'axios';

// Function to load translations from Laravel
export async function loadLaravelTranslations(locale) {
    try {
        const response = await axios.get(`/api/translations/${locale}`);
        return response.data;
    } catch (error) {
        console.error('Error loading translations:', error);
        return {};
    }
}

// Function to merge Laravel translations with Vue i18n
export async function mergeTranslations(i18n, locale) {
    try {
    const laravelTranslations = await loadLaravelTranslations(locale);

        // Process Spatie translations (they have a specific format: table.id.attribute)
        const processedTranslations = processSpatieTranslations(laravelTranslations);

    // Merge Laravel translations with existing Vue i18n messages
        i18n.global.mergeLocaleMessage(locale, processedTranslations);

    return i18n;
    } catch (error) {
        console.error('Error merging translations:', error);
        return i18n;
    }
}

// Function to process Spatie translations into a nested structure
function processSpatieTranslations(translations) {
    const processed = { ...translations };

    // Find all keys that match the Spatie format (table.id.attribute)
    const spatieKeys = Object.keys(translations).filter(key => {
        const parts = key.split('.');
        return parts.length === 3 && !isNaN(parts[1]); // Check if middle part is a number (ID)
    });

    // Process each Spatie translation
    spatieKeys.forEach(key => {
        const [table, id, attribute] = key.split('.');

        // Create nested structure if it doesn't exist
        if (!processed[table]) {
            processed[table] = {};
        }

        if (!processed[table][id]) {
            processed[table][id] = {};
        }

        // Move the translation to the nested structure
        processed[table][id][attribute] = translations[key];

        // Remove the original key
        delete processed[key];
    });

    return processed;
}

// Function to update document attributes based on locale
export function updateDocumentAttributes(locale) {
    // Set the HTML lang attribute
    document.documentElement.lang = locale;

    // Set the document direction based on locale
    const isRTL = locale === 'ar' || locale === 'ku';
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';

    // Update body class for RTL support
    document.body.classList.toggle('rtl', isRTL);

    // Dispatch a custom event for language change
    window.dispatchEvent(new CustomEvent('languageChanged', {
        detail: { locale, isRTL }
    }));
}
