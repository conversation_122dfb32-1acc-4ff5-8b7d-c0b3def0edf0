export default {

    mieed: 'Mieed',
    common: {
        selected: 'Selected',
        permissions: 'Permissions',
        actions: 'Actions',
        clear_all: 'Clear All',
        select_all: 'Select All',
        role_create_group: 'Role Create Group',
        related_permissions: 'Related Permissions',
        select_language: 'Select language',
        switch_language: 'Switch',
        language_suggestion: 'Would you prefer to view this page in <strong>{detected}</strong>?',
        change: 'Change',
        welcome: 'Welcome',
        profile: 'Profile',
        settings: 'Settings',
        logout: 'Logout',
        confirm_delete: 'Confirm Delete',
        are_you_sure_delete: 'Are you sure you want to delete this item?',
        yes: 'Yes',
        no: 'No',
        deleted_successfully: 'Item deleted successfully',
        search_placeholder: 'Search...',
        create_button: 'Create',
        edit_button: 'Edit',
        delete_button: 'Delete',
        save_button: 'Save',
        cancel_button: 'Cancel',
        back_button: 'Back',
        next_button: 'Next',
        previous_button: 'Previous',
        first_button: 'First',
        last_button: 'Last',
        current_locale: 'Current Language',
        changing_language: 'Changing Language...',
        please_wait: 'Please wait while we update your language settings',
        loading: 'Loading...',
        processing: 'Processing...',
        saving: 'Saving...',
        updating: 'Updating...',
        deleting: 'Deleting...',
        searching: 'Searching...',
        no_results: 'No results found',
        no_data: 'No data available',
        operation_success: 'Operation completed successfully',
        operation_failed: 'Operation failed',
        try_again: 'Please try again',
        connection_error: 'Connection error',
        server_error: 'Server error',
        validation_error: 'Validation error',
        unauthorized: 'Unauthorized access',
        forbidden: 'Access forbidden',
        not_found: 'Resource not found',
        session_expired: 'Session expired',
        login_required: 'Login required',
        user_details: "User Details",
        last_activity: "Last Activity",
        role: "Role",
        roles: {
            super_admin: "Super Admin",
            admin: "Admin",
            user: "User"
        },
        language: {
            code: 'en',
            name: 'English',
            flag: '🇺🇸',
            isRTL: false
        },
        search: "Search",
        active_users: "Active Users",
        inactive_users: "Inactive Users",
        total_roles: "Total Roles",
        no_user: "No users found",
        last_login: "Last Login",
        total_users: "Total Users",
        new_users_today: "New Users Today",
        back: 'Back',
        close: 'Close',
        expand: 'Expand',
        collapse: 'Collapse',
        not_available: 'Not Available',
        product_image: 'Product Image',
        image_gallery: 'Image Gallery',
        click_to_enlarge: 'Click to enlarge'
    },

    // Role & Permission Translations
    roles: {
        admin: 'Admin',
        manager: 'Manager',
        user: 'User',
        custom: 'Custom Role',
        view_mode: {
            grid: 'Grid View',
            list: 'List View',
            switch_view: 'Switch View Mode'
        },
        permissions: {
            select_all: 'Select All',
            clear_all: 'Clear All',
            selected_count: 'Selected',
            total_count: 'Total',
            group: 'Group',
            action: 'Action',
            permission: 'Permission'
        }
    },

    permissions: {
        // General Management
        general: {
            manage_roles: 'Manage Roles',
            manage_permissions: 'Manage Permissions',
            manage_users: 'Manage Users',
            manage_products: 'Manage Products',
            manage_categories: 'Manage Categories',
            manage_brands: 'Manage Brands',
            manage_units: 'Manage Units',
            manage_suppliers: 'Manage Suppliers',
            manage_customers: 'Manage Customers',
            manage_purchases: 'Manage Purchases',
            manage_sales: 'Manage Sales',
            manage_stock: 'Manage Stock',
            manage_accounts: 'Manage Accounts',
            manage_profile: 'Manage Profile'
        },

        // Account Management
        account_management: {
            view_accounts: 'View Accounts',
            create_accounts: 'Create Accounts',
            edit_accounts: 'Edit Accounts',
            delete_accounts: 'Delete Accounts',
            view_account_tree: 'View Account Tree',
            view_account_transfers: 'View Account Transfers',
            create_account_transfers: 'Create Account Transfers',
            edit_account_transfers: 'Edit Account Transfers',
            delete_account_transfers: 'Delete Account Transfers',
            view_account_balance: 'View Account Balance',
            view_account_history: 'View Account History',
            view_account_reports: 'View Account Reports',
            export_accounts: 'Export Accounts',
            import_accounts: 'Import Accounts'
        },

        // Brand Management
        brand_management: {
            view_brands: 'View Brands',
            create_brands: 'Create Brands',
            edit_brands: 'Edit Brands',
            delete_brands: 'Delete Brands',
            view_brand_products: 'View Brand Products',
            manage_brand_products: 'Manage Brand Products',
            export_brands: 'Export Brands',
            import_brands: 'Import Brands'
        },

        // Category Management
        category_management: {
            view_categories: 'View Categories',
            create_categories: 'Create Categories',
            edit_categories: 'Edit Categories',
            delete_categories: 'Delete Categories',
            view_category_products: 'View Category Products',
            manage_category_products: 'Manage Category Products',
            export_categories: 'Export Categories',
            import_categories: 'Import Categories'
        },

        // Cost Center Management
        cost_center_management: {
            view_cost_centers: 'View Cost Centers',
            create_cost_centers: 'Create Cost Centers',
            edit_cost_centers: 'Edit Cost Centers',
            delete_cost_centers: 'Delete Cost Centers',
            view_cost_center_reports: 'View Cost Center Reports',
            export_cost_centers: 'Export Cost Centers',
            import_cost_centers: 'Import Cost Centers'
        },

        // Currency Management
        currency_management: {
            view_currencies: 'View Currencies',
            create_currencies: 'Create Currencies',
            edit_currencies: 'Edit Currencies',
            delete_currencies: 'Delete Currencies',
            view_currency_rates: 'View Currency Rates',
            update_currency_rates: 'Update Currency Rates',
            view_currency_history: 'View Currency History',
            export_currencies: 'Export Currencies',
            import_currencies: 'Import Currencies'
        },

        // Customer Management
        customer_management: {
            view_customers: 'View Customers',
            create_customers: 'Create Customers',
            edit_customers: 'Edit Customers',
            delete_customers: 'Delete Customers',
            view_customer_details: 'View Customer Details',
            view_customer_balance: 'View Customer Balance',
            view_customer_payments: 'View Customer Payments',
            view_customer_sales: 'View Customer Sales',
            export_customers: 'Export Customers',
            import_customers: 'Import Customers'
        },



        // Deposit Management
        deposit_management: {
            view_deposits: 'View Deposits',
            create_deposits: 'Create Deposits',
            edit_deposits: 'Edit Deposits',
            delete_deposits: 'Delete Deposits',
            view_deposit_details: 'View Deposit Details',
            view_deposit_history: 'View Deposit History',
            view_deposit_reports: 'View Deposit Reports',
            approve_deposits: 'Approve Deposits',
            export_deposits: 'Export Deposits',
            import_deposits: 'Import Deposits'
        },

        // Discount Management
        discount_management: {
            view_discounts: 'View Discounts',
            create_discounts: 'Create Discounts',
            edit_discounts: 'Edit Discounts',
            delete_discounts: 'Delete Discounts',
            view_discount_details: 'View Discount Details',
            view_discount_history: 'View Discount History',
            view_discount_reports: 'View Discount Reports',
            apply_discounts: 'Apply Discounts',
            export_discounts: 'Export Discounts',
            import_discounts: 'Import Discounts'
        },

        // Expense Management
        expense_management: {
            view_expenses: 'View Expenses',
            create_expenses: 'Create Expenses',
            edit_expenses: 'Edit Expenses',
            delete_expenses: 'Delete Expenses',
            view_expense_details: 'View Expense Details',
            view_expense_history: 'View Expense History',
            approve_expenses: 'Approve Expenses',
            export_expenses: 'Export Expenses',
            import_expenses: 'Import Expenses'
        },

        // Invoice Management
        invoice_management: {
            generate_invoices: 'Generate Invoices',
            view_customer_sales_history: 'View Customer Sales History',
            view_customer_payment_history: 'View Customer Payment History',
            generate_payment_invoices: 'Generate Payment Invoices',
            view_invoice_details: 'View Invoice Details',
            edit_invoices: 'Edit Invoices',
            delete_invoices: 'Delete Invoices',
            print_invoices: 'Print Invoices',
            email_invoices: 'Email Invoices',
            export_invoices: 'Export Invoices',
            import_invoices: 'Import Invoices'
        },

        // Payment Management
        payment_management: {
            view_payments: 'View Payments',
            create_payments: 'Create Payments',
            edit_payments: 'Edit Payments',
            delete_payments: 'Delete Payments',
            view_payment_details: 'View Payment Details',
            view_payment_reports: 'View Payment Reports',
            process_payments: 'Process Payments',
            refund_payments: 'Refund Payments',
            export_payments: 'Export Payments',
            import_payments: 'Import Payments'
        },

        // Product Management
        product_management: {
            view_products: 'View Products',
            create_products: 'Create Products',
            edit_products: 'Edit Products',
            delete_products: 'Delete Products',
            upload_product_images: 'Upload Product Images',
            delete_product_images: 'Delete Product Images',
            search_products: 'Search Products',
            export_products: 'Export Products',
            import_products: 'Import Products',
            view_product_history: 'View Product History',
            view_product_stock: 'View Product Stock',
            update_product_stock: 'Update Product Stock',
            view_product_prices: 'View Product Prices',
            update_product_prices: 'Update Product Prices',
            view_product_categories: 'View Product Categories',
            manage_product_categories: 'Manage Product Categories'
        },

        // Purchase Management
        purchase_management: {
            view_purchases: 'View Purchases',
            create_purchases: 'Create Purchases',
            edit_purchases: 'Edit Purchases',
            delete_purchases: 'Delete Purchases',
            view_purchase_details: 'View Purchase Details',
            view_purchase_history: 'View Purchase History',
            view_purchase_payments: 'View Purchase Payments',
            view_purchase_returns: 'View Purchase Returns',
            create_purchase_returns: 'Create Purchase Returns',
            edit_purchase_returns: 'Edit Purchase Returns',
            delete_purchase_returns: 'Delete Purchase Returns',
            export_purchases: 'Export Purchases',
            import_purchases: 'Import Purchases'
        },

        // Report Management
        report_management: {
            view_customer_history: 'View Customer History',
            view_stock_reports: 'View Stock Reports',
            view_purchase_reports: 'View Purchase Reports',
            view_payment_history: 'View Payment History',
            view_expense_reports: 'View Expense Reports',
            view_reports: 'View Reports',
            generate_reports: 'Generate Reports',
            view_sales_reports: 'View Sales Reports',
            view_product_sales_reports: 'View Product Sales Reports',
            view_profit_reports: 'View Profit Reports',
            view_loss_reports: 'View Loss Reports',
            view_tax_reports: 'View Tax Reports',
            view_inventory_reports: 'View Inventory Reports',
            view_financial_reports: 'View Financial Reports',
            export_reports: 'Export Reports',
            schedule_reports: 'Schedule Reports'
        },

        // Role & Permission Management
        role_permission_management: {
            view_roles: 'View Roles',
            create_roles: 'Create Roles',
            edit_roles: 'Edit Roles',
            delete_roles: 'Delete Roles',
            assign_roles: 'Assign Roles',
            view_permissions: 'View Permissions',
            create_permissions: 'Create Permissions',
            edit_permissions: 'Edit Permissions',
            delete_permissions: 'Delete Permissions',
            assign_permissions: 'Assign Permissions'
        },

        // Sale Management
        sale_management: {
            view_sales: 'View Sales',
            create_sales: 'Create Sales',
            edit_sales: 'Edit Sales',
            delete_sales: 'Delete Sales',
            view_sale_details: 'View Sale Details',
            view_sale_history: 'View Sale History',
            view_sale_reports: 'View Sale Reports',
            view_sale_payments: 'View Sale Payments',
            view_sale_returns: 'View Sale Returns',
            create_sale_returns: 'Create Sale Returns',
            edit_sale_returns: 'Edit Sale Returns',
            delete_sale_returns: 'Delete Sale Returns',
            export_sales: 'Export Sales',
            import_sales: 'Import Sales'
        },

        // Settings Management
        settings_management: {
            view_settings: 'View Settings',
            edit_settings: 'Edit Settings',
            view_company_settings: 'View Company Settings',
            edit_company_settings: 'Edit Company Settings',
            view_system_settings: 'View System Settings',
            edit_system_settings: 'Edit System Settings',
            view_email_settings: 'View Email Settings',
            edit_email_settings: 'Edit Email Settings',
            view_notification_settings: 'View Notification Settings',
            edit_notification_settings: 'Edit Notification Settings',
            view_backup_settings: 'View Backup Settings',
            edit_backup_settings: 'Edit Backup Settings',
            view_security_settings: 'View Security Settings',
            edit_security_settings: 'Edit Security Settings',
            view_localization_settings: 'View Localization Settings',
            edit_localization_settings: 'Edit Localization Settings'
        },

        // Stock Management
        stock_management: {
            view_stock_transfers: 'View Stock Transfers',
            create_stock_transfers: 'Create Stock Transfers',
            edit_stock_transfers: 'Edit Stock Transfers',
            delete_stock_transfers: 'Delete Stock Transfers',
            view_stock_movement: 'View Stock Movement',
            view_stock_alerts: 'View Stock Alerts',
            view_stock_history: 'View Stock History',
            view_stock_levels: 'View Stock Levels',
            update_stock_levels: 'Update Stock Levels',
            export_stock_data: 'Export Stock Data',
            import_stock_data: 'Import Stock Data'
        },

        // Supplier Management
        supplier_management: {
            view_suppliers: 'View Suppliers',
            create_suppliers: 'Create Suppliers',
            edit_suppliers: 'Edit Suppliers',
            delete_suppliers: 'Delete Suppliers',
            view_supplier_details: 'View Supplier Details',
            view_supplier_history: 'View Supplier History',
            view_supplier_balance: 'View Supplier Balance',
            view_supplier_payments: 'View Supplier Payments',
            view_supplier_purchases: 'View Supplier Purchases',
            export_suppliers: 'Export Suppliers',
            import_suppliers: 'Import Suppliers'
        },

        // Unit Management
        unit_management: {
            view_units: 'View Units',
            create_units: 'Create Units',
            edit_units: 'Edit Units',
            delete_units: 'Delete Units',
            view_unit_conversions: 'View Unit Conversions',
            manage_unit_conversions: 'Manage Unit Conversions',
            export_units: 'Export Units',
            import_units: 'Import Units'
        },

        // User Management
        user_management: {
            view_users: 'View Users',
            create_users: 'Create Users',
            edit_users: 'Edit Users',
            delete_users: 'Delete Users',
            view_user_profile: 'View User Profile',
            edit_user_profile: 'Edit User Profile',
            delete_user_profile: 'Delete User Profile',
            change_user_password: 'Change User Password',
            view_user_activity: 'View User Activity',
            export_users: 'Export Users',
            import_users: 'Import Users'
        },

        // Warehouse Management
        warehouse_management: {
            view_warehouses: 'View Warehouses',
            create_warehouses: 'Create Warehouses',
            edit_warehouses: 'Edit Warehouses',
            delete_warehouses: 'Delete Warehouses',
            view_warehouse_stock: 'View Warehouse Stock',
            manage_warehouse_stock: 'Manage Warehouse Stock',
            view_warehouse_transfers: 'View Warehouse Transfers',
            manage_warehouse_transfers: 'Manage Warehouse Transfers',
            export_warehouses: 'Export Warehouses',
            import_warehouses: 'Import Warehouses'
        },

        // Management Group
        management_group: {
            view_management: 'View Management',
            create_management: 'Create Management',
            edit_management: 'Edit Management',
            delete_management: 'Delete Management',
            view_management_details: 'View Management Details',
            view_management_history: 'View Management History',
            view_management_reports: 'View Management Reports',
            export_management: 'Export Management',
            import_management: 'Import Management'
        }
    },

    // Menus translations
    menus: {
        dashboard: 'Dashboard',
        products: 'Products',
        all_products: 'All Products',
        create_products: 'Create Products',
        category: 'Category',
        brand: 'Brand',
        unit: 'Unit',
        suppliers: 'Suppliers',
        customers: 'Customers',
        purchases: 'Purchases',
        sales: 'Sales',
        sales_return: 'Sales Return',
        purchase_return: 'Purchase Return',
        stock_transfers: 'Stock Transfers',
        accounting: 'Accounting',
        account: 'Account',
        transfers: 'Transfers',
        payments: 'Payments',
        discounts: 'Discounts',
        expenses: 'Expenses',
        deposits: 'Deposits',
        reports: 'Reports',
        reports_overview: 'Reports Overview',
        sales_report: 'Sales Report',
        purchase_report: 'Purchase Report',
        product_stock_report: 'Product Stock Report',
        stock_report: 'Stock Report',
        account_report: 'Account Report',
        expense_report: 'Expense Report',
        settings: 'Settings',
        warehouse: 'Warehouse',
        currency: 'Currency',
        'currency-exchange': 'Currency Exchange',
        role: 'Role',
        roles: 'Roles',
        user: 'User',
        users: 'Users',
        permissions: 'Permissions',
        'user-management': 'User Management',
        accounts: 'Accounts'
    },

    // Report Types
    sales_report: 'Sales Report',
    purchase_report: 'Purchase Report',
    product_stock_report: 'Product Stock Report',
    stock_report: 'Stock Report',
    account_report: 'Account Report',
    expense_report: 'Expense Report',

    // Actions
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    create: 'Create',
    update: 'Update',
    view: 'View',
    export: 'Export',
    import: 'Import',
    filter: 'Filter',
    clear: 'Clear',
    upload: 'Upload',
    download: 'Download',
    print: 'Print',
    preview: 'Preview',
    submit: 'Submit',
    reset: 'Reset',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    first: 'First',
    last: 'Last',

    // Messages
    confirm_delete: 'Are you sure you want to delete this item?',
    item_deleted: 'Item deleted successfully',
    item_saved: 'Item saved successfully',
    error_occurred: 'An error occurred',
    viewport_warning: 'This example was made for viewport sizes 920px and above :)',
    no_data: 'No data available',
    loading: 'Loading...',
    please_wait: 'Please wait...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    confirm: 'Confirm',
    cancel: 'Cancel',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',

    // Form Labels
    form: {
        name: 'Name',
        description: 'Description',
        price: 'Price',
        quantity: 'Quantity',
        total: 'Total',
        date: 'Date',
        status: 'Status',
        type: 'Type',
        code: 'Code',
        email: 'Email',
        phone: 'Phone',
        address: 'Address',
        notes: 'Notes',
        select: 'Select',
        required: 'Required',
        optional: 'Optional',
        active: 'Active',
        inactive: 'Inactive',
        published: 'Published',
        unpublished: 'Unpublished',
        visible: 'Visible',
        hidden: 'Hidden',
        enabled: 'Enabled',
        disabled: 'Disabled',
        enter_name_en: 'Enter name in English',
        enter_name_ku: 'Enter name in Kurdish',
        enter_company_name: 'Enter company name',
        enter_phone: 'Enter phone number',
        enter_address: 'Enter address',
        enter_warehouse_name: 'Enter warehouse name',
        enter_warehouse_phone: 'Enter warehouse phone',
        enter_warehouse_address: 'Enter warehouse address',
        enter_cost_center_name: 'Enter cost center name',
        enter_limit: 'Enter limit amount',
        enter_notes: 'Enter notes',
        select_warehouse: 'Select warehouse',
        select_currency: 'Select currency',
        select_account_box: 'Select account box',
        select_cost_center: 'Select cost center',
        select_customer_type: 'Select customer type',
        start_date: 'Start Date',
        end_date: 'End Date',
        individual: 'Individual',
        business: 'Business',
        project: 'Project',
        name_en: 'Full name (English)',
        name_ku: 'Full name (Kurdish)',
        company_name: 'Company name',
        currency: 'Currency',
        account_box: 'Account Box',
        amount: 'Amount',
        note: 'Note',
        confirm_password: 'Confirm Password',
        role: 'Role',
        basic_info: 'Basic Information',
        stock_info: 'Stock Information',
        pricing_info: 'Pricing Information',
        status_info: 'Status Information',
        additional_info: 'Additional Information',
        current_stock: 'Current Stock',
        stock_history: 'Stock History',
        price_history: 'Price History',
        activity_log: 'Activity Log',
        old_price: 'Old Price',
        new_price: 'New Price',
        business_price: 'Business Price',
        individual_price: 'Individual Price',
        project_price: 'Project Price',
        shipping_method: 'Shipping Method',
        pcs_per_box: 'Pieces per Box',
        size: 'Size',
        alert_quantity: 'Alert Quantity',
        warehouse_stock: 'Warehouse Stock',
        warehouse: 'Warehouse',
        quantity: 'Quantity',
        type: 'Type',
        user: 'User',
        action: 'Action',
        date: 'Date'
    },

    // Status
    status: {
        active: 'Active',
        inactive: 'Inactive',
        pending: 'Pending',
        completed: 'Completed',
        cancelled: 'Cancelled',
        draft: 'Draft',
        archived: 'Archived',
        deleted: 'Deleted',
        processing: 'Processing',
        failed: 'Failed',
        warning: 'Warning',
        error: 'Error',
        out_of_stock: 'Out of Stock',
        low_stock: 'Low Stock',
        in_stock: 'In Stock'
    },

    // Pages
    pages: {
        // Auth Pages
        auth: {
            login: {
                title: 'Login',
                email: 'Email',
                password: 'Password',
                remember_me: 'Remember me',
                forgot_password: 'Forgot password?',
                login_button: 'Login',
                no_account: "Don't have an account?",
                register: 'Register',
                or: 'Or',
                continue_with: 'Continue with'
            },
            register: {
                title: 'Register',
                name: 'Name',
                email: 'Email',
                password: 'Password',
                confirm_password: 'Confirm Password',
                have_account: 'Already have an account?',
                register_button: 'Register'
            },
            verify_email: {
                title: 'Verify Email',
                message: 'Please verify your email address',
                resend: 'A new verification link has been sent to your email address'
            },
            forgot_password: {
                title: 'Forgot Password',
                message: 'Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.',
                email: 'Email',
                reset_button: 'Email Password Reset Link'
            },
            reset_password: {
                title: 'Reset Password',
                email: 'Email',
                password: 'New Password',
                confirm_password: 'Confirm New Password',
                reset_button: 'Reset Password'
            },
            logout: 'Logout',
            logout_confirm: 'Are you sure you want to logout?',
            profile: 'My Profile',
            settings: 'Settings'
        },

        // Admin Pages
        admin: {
            dashboard: {
                title: 'Dashboard',
                overview: 'Overview',
                recent_activity: 'Recent Activity',
                statistics: 'Statistics'
            },
            products: {
                title: 'Products',
                add_new: 'Add New Product',
                edit: 'Edit Product',
                list: 'Product List',
                category: 'Category',
                brand: 'Brand',
                unit: 'Unit',
                price: 'Price',
                stock: 'Stock',
                actions: 'Actions',
                search_placeholder: 'Search products...',
                create_button: 'Create Product',
                delete_confirm: 'Are you sure you want to delete this product?',
                delete_success: 'Product deleted successfully',
                business_price: 'Business Price',
                stock_alert: 'Stock Alert'
            },
            customers: {
                title: 'Customers',
                add_new: 'Add New Customer',
                edit: 'Edit Customer',
                list: 'Customer List',
                details: 'Customer Details'
            },
            suppliers: {
                title: 'Suppliers',
                add_new: 'Add New Supplier',
                edit: {
                    title: 'Update Supplier'
                },
                list: 'Supplier List',
                details: 'Supplier Details',
                payment: {
                    title: 'Pay For {name}'
                }
            },
            sales: {
                title: 'Sales',
                new_sale: 'New Sale',
                list: 'Sales List',
                details: 'Sale Details',
                return: 'Sales Return'
            },
            purchases: {
                title: 'Purchases',
                new_purchase: 'New Purchase',
                list: 'Purchase List',
                details: 'Purchase Details',
                return: 'Purchase Return'
            },
            reports: {
                title: 'Reports',
                sales: 'Sales Report',
                purchases: 'Purchase Report',
                stock: 'Stock Report',
                expenses: 'Expense Report',
                generate: 'Generate Report',
                export: 'Export Report'
            },
            settings: {
                title: 'Settings',
                general: 'General Settings',
                company: 'Company Information',
                currency: 'Currency Settings',
                warehouse: 'Warehouse Settings',
                users: 'User Management',
                roles: 'Role Management'
            },
            roles: {
                name: "Name",
                permissions: "Permissions",
                create: "Create Role",
                edit: "Edit Role",
                delete: "Delete Role",
                delete_confirm: "Are you sure you want to delete this role?",
                delete_success: "Role deleted successfully",
                delete_error: "Failed to delete role"
            },
            users: {
                title: "User Management",
                index: {
                    title: "User Management",
                    search: "Search users...",
                    no_user: "No users found",
                    last_login: "Last Login",
                    total_users: "Total Users",
                    active_users: "Active Users",
                    inactive_users: "Inactive Users",
                    new_users_today: "New Users Today",
                    total_roles: "Total Roles"
                },
                create: {
                    title: "Create New User",
                    form: {
                        name: "Full Name",
                        email: "Email Address",
                        password: "Password",
                        confirm_password: "Confirm Password",
                        role: "User Role",
                        status: "Account Status",
                        permissions: "User Permissions",
                        validation: {
                            name_required: "Name is required",
                            email_required: "Email is required",
                            email_invalid: "Please enter a valid email address",
                            password_required: "Password is required",
                            password_min: "Password must be at least 8 characters",
                            password_confirmation: "Passwords do not match",
                            role_required: "Role is required"
                        }
                    },
                    success: "User created successfully",
                    error: "Error creating user"
                },
                edit: {
                    title: "Edit User",
                    form: {
                        name: "Full Name",
                        email: "Email Address",
                        password: "New Password (optional)",
                        confirm_password: "Confirm New Password",
                        role: "User Role",
                        status: "Account Status",
                        permissions: "User Permissions",
                        validation: {
                            name_required: "Name is required",
                            email_required: "Email is required",
                            email_invalid: "Please enter a valid email address",
                            password_min: "Password must be at least 8 characters",
                            password_confirmation: "Passwords do not match",
                            role_required: "Role is required"
                        }
                    },
                    success: "User updated successfully",
                    error: "Error updating user"
                },
                status: {
                    active: "Active",
                    inactive: "Inactive",
                    suspended: "Suspended",
                    deleted: "Deleted"
                },
                actions: {
                    create: "Create User",
                    edit: "Edit User",
                    delete: "Delete User",
                    activate: "Activate User",
                    deactivate: "Deactivate User",
                    suspend: "Suspend User",
                    restore: "Restore User",
                    view_details: "View Details",
                    change_password: "Change Password",
                    manage_permissions: "Manage Permissions"
                },
                messages: {
                    confirm_delete: "Are you sure you want to delete this user?",
                    confirm_activate: "Are you sure you want to activate this user?",
                    confirm_deactivate: "Are you sure you want to deactivate this user?",
                    confirm_suspend: "Are you sure you want to suspend this user?",
                    confirm_restore: "Are you sure you want to restore this user?",
                    success_create: "User created successfully",
                    success_update: "User updated successfully",
                    success_delete: "User deleted successfully",
                    success_activate: "User activated successfully",
                    success_deactivate: "User deactivated successfully",
                    success_suspend: "User suspended successfully",
                    success_restore: "User restored successfully",
                    error_create: "Failed to create user",
                    error_update: "Failed to update user",
                    error_delete: "Failed to delete user",
                    error_activate: "Failed to activate user",
                    error_deactivate: "Failed to deactivate user",
                    error_suspend: "Failed to suspend user",
                    error_restore: "Failed to restore user"
                },
                permissions: {
                    title: "User Permissions",
                    direct: "Direct Permissions",
                    inherited: "Inherited Permissions",
                    select_all: "Select All",
                    deselect_all: "Deselect All",
                    save: "Save Permissions",
                    cancel: "Cancel",
                    success: "Permissions updated successfully",
                    error: "Failed to update permissions"
                },
                profile: {
                    title: "User Profile",
                    personal_info: "Personal Information",
                    security: "Security",
                    activity: "Activity Log",
                    sessions: "Active Sessions",
                    two_factor: "Two-Factor Authentication",
                    api_tokens: "API Tokens",
                    notifications: "Notifications",
                    preferences: "Preferences"
                }
            }
        },

        // Profile Pages
        profile: {
            title: 'Profile',
            edit_profile: 'Edit Profile',
            change_password: 'Change Password',
            delete_account: 'Delete Account',
            personal_info: 'Personal Information',
            name: 'Name',
            email: 'Email',
            phone: 'Phone',
            update_success: 'Profile updated successfully',
            password_updated: 'Password updated successfully',
            account_deleted: 'Account deleted successfully'
        },

        // Website Pages
        website: {
            home: {
                title: 'Home',
                welcome: 'Welcome to our store',
                featured_products: 'Featured Products',
                view_all: 'View All Products'
            },
            about: {
                title: 'About Us',
                our_story: 'Our Story',
                mission: 'Our Mission',
                vision: 'Our Vision'
            },
            contact: {
                title: 'Contact Us',
                name: 'Your Name',
                email: 'Your Email',
                subject: 'Subject',
                message: 'Message'
            },
            products: {
                title: 'Products',
                categories: 'Categories',
                price_range: 'Price Range',
                sort_by: 'Sort By',
                add_to_cart: 'Add to Cart',
                view_details: 'View Details'
            },
            save_button: 'Save',
            cancel_button: 'Cancel'
        }
    },

    // Table Columns
    table: {
        image: 'Image',
        code: 'Code',
        name: 'Name',
        brand: 'Brand',
        category: 'Category',
        unit: 'Unit',
        business_price: 'Business Price',
        stock: 'Stock',
        actions: 'Actions',
        no_data: 'No data available',
        loading: 'Loading...',
        search: 'Search',
        filter: 'Filter',
        clear: 'Clear',
        export: 'Export',
        import: 'Import',
        id: 'ID',
        short_name: 'Short Name',
        base_unit: 'Base Unit',
        operator: 'Operator',
        operator_value: 'Operator Value',
        reference: 'Reference',
        type: 'Type',
        amount: 'Amount',
        payment_method: 'Payment Method',
        date: 'Date',
        quantity: 'Quantity',
        average_price: 'Average Price',
        price: 'Price',
        total: 'Total',
        cost: 'Cost',
        total_cost: 'Total Cost',
        stock_in_warehouse: 'Stock in Warehouse',
        description: 'Description',
        purchase_price: 'Purchase Price',
        barcode: 'Barcode',
        no_records: 'No records found',
        balance: 'Balance',
        currency: 'Currency',
        status: 'Status',
        accounts: 'Accounts'
    },

    // Button Text
    buttons: {
        filter: 'Filter',
        print: 'Print',
        export: 'Export',
        submit: 'Submit',
        cancel: 'Cancel',
        save: 'Save',
        edit: 'Edit',
        delete: 'Delete',
        view: 'View',
        create: 'Create',
        update: 'Update',
        add: 'Add',
        search: 'Search',
        reset: 'Reset',
        pay: 'Pay',
        return: 'Return',
        calculate: 'Calculate',
        submit_and_print: 'Submit and Print',
        add_deposit: 'Add Deposit',
        add_expense: 'Add Expense',
        add_supplier: 'Add Supplier',
        add_warehouse: 'Add Warehouse',
        add_currency: 'Add Currency',
        add_unit: 'Add Unit',
        add_brand: 'Add Brand',
        add_category: 'Add Category',
        create_sale: 'Create Sale',
        create_purchase: 'Create Purchase',
        create_transfer: 'Create Transfer',
        create_payment: 'Create Payment',
        toggle_theme: 'Toggle Theme',
        toggle_theme_light: 'Light Mode',
        toggle_theme_dark: 'Dark Mode'
    },

    profile: {
        title: 'Profile',
        edit_profile: 'Edit Profile',
        change_password: 'Change Password',
        delete_account: 'Delete Account',
        personal_info: 'Personal Information',
        name: 'Name',
        email: 'Email',
        phone: 'Phone',
        update_success: 'Profile updated successfully',
        password_updated: 'Password updated successfully',
        account_deleted: 'Account deleted successfully'
    },

    auth: {
        logout: 'Logout',
        logout_confirm: 'Are you sure you want to logout?',
        profile: 'My Profile',
        settings: 'Settings'
    },

    validation: {
        required: "This field is required"
    },

    // System Messages
    system: {
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Information',
        confirm: 'Confirm',
        loading: 'Loading...',
        processing: 'Processing...',
        saving: 'Saving...',
        updating: 'Updating...',
        deleting: 'Deleting...',
        searching: 'Searching...',
        no_results: 'No results found',
        no_data: 'No data available',
        please_wait: 'Please wait...',
        operation_success: 'Operation completed successfully',
        operation_failed: 'Operation failed',
        try_again: 'Please try again',
        connection_error: 'Connection error',
        server_error: 'Server error',
        validation_error: 'Validation error',
        unauthorized: 'Unauthorized access',
        forbidden: 'Access forbidden',
        not_found: 'Resource not found',
        session_expired: 'Session expired',
        login_required: 'Login required'
    },

    // Date and Time
    datetime: {
        today: 'Today',
        yesterday: 'Yesterday',
        tomorrow: 'Tomorrow',
        this_week: 'This week',
        last_week: 'Last week',
        next_week: 'Next week',
        this_month: 'This month',
        last_month: 'Last month',
        next_month: 'Next month',
        this_year: 'This year',
        last_year: 'Last year',
        next_year: 'Next year',
        days: {
            monday: 'Monday',
            tuesday: 'Tuesday',
            wednesday: 'Wednesday',
            thursday: 'Thursday',
            friday: 'Friday',
            saturday: 'Saturday',
            sunday: 'Sunday'
        },
        months: {
            january: 'January',
            february: 'February',
            march: 'March',
            april: 'April',
            may: 'May',
            june: 'June',
            july: 'July',
            august: 'August',
            september: 'September',
            october: 'October',
            november: 'November',
            december: 'December'
        }
    },

    // File Operations
    files: {
        upload: 'Upload',
        download: 'Download',
        delete: 'Delete',
        rename: 'Rename',
        move: 'Move',
        copy: 'Copy',
        paste: 'Paste',
        select: 'Select',
        deselect: 'Deselect',
        preview: 'Preview',
        open: 'Open',
        close: 'Close',
        save: 'Save',
        cancel: 'Cancel',
        error: {
            too_large: 'File is too large',
            invalid_type: 'Invalid file type',
            upload_failed: 'Upload failed',
            download_failed: 'Download failed',
            delete_failed: 'Delete failed'
        }
    },

    // Pagination
    pagination: {
        showing: 'Showing',
        to: 'to',
        of: 'of',
        entries: 'entries',
        per_page: 'per page',
        first: 'First',
        last: 'Last',
        next: 'Next',
        previous: 'Previous',
        page: 'Page',
        go_to: 'Go to page',
        no_results: 'No results found'
    },

    // Form Validation
    validation: {
        required: 'This field is required',
        email: 'Please enter a valid email address',
        min_length: 'Minimum length is {length} characters',
        max_length: 'Maximum length is {length} characters',
        min_value: 'Minimum value is {value}',
        max_value: 'Maximum value is {value}',
        numeric: 'Please enter a number',
        integer: 'Please enter an integer',
        decimal: 'Please enter a decimal number',
        url: 'Please enter a valid URL',
        phone: 'Please enter a valid phone number',
        password: {
            min_length: 'Password must be at least {length} characters',
            uppercase: 'Password must contain at least one uppercase letter',
            lowercase: 'Password must contain at least one lowercase letter',
            number: 'Password must contain at least one number',
            special: 'Password must contain at least one special character',
            match: 'Passwords do not match'
        }
    },

    // Page sections and titles
    page: {
        sales: {
            title: 'Sales',
            return: 'Sales Return'
        },
        purchases: {
            title: 'Purchases',
            return: 'Purchase Return'
        },
        stock: {
            title: 'Stock',
            transfers: 'Stock Transfers'
        },
        accounting: {
            title: 'Accounting',
            account: 'Account',
            transfers: 'Transfers',
            payments: 'Payments',
            discounts: 'Discounts',
            expenses: 'Expenses',
            deposits: 'Deposits'
        },
        reports: {
            title: 'Reports',
            overview: 'Reports Overview',
            sales: 'Sales Report',
            purchases: 'Purchase Report',
            product_stock: 'Product Stock Report',
            stock: 'Stock Report',
            accounts: 'Account Report',
            expenses: 'Expense Report'
        },
        settings: {
            title: 'Settings',
            warehouse: 'Warehouse',
            currency: 'Currency',
            roles: {
                title: 'Role'
            },
            users: 'User Management'
        }
    },

    messages: {
        created_successfully: 'Account created successfully',
        deleted_successfully: 'Account deleted successfully',
        error_occurred: 'An error occurred',
        confirm: 'Confirm',
        confirm_delete: 'Are you sure you want to delete this account?',
        no_data: 'No data available',
        confirm_delete: "Are you sure you want to delete this user?",
        confirm_activate: "Are you sure you want to activate this user?",
        confirm_deactivate: "Are you sure you want to deactivate this user?",
        success_create: "User created successfully",
        success_update: "User updated successfully",
        success_delete: "User deleted successfully",
        success_activate: "User activated successfully",
        success_deactivate: "User deactivated successfully",
        error_create: "Failed to create user",
        error_update: "Failed to update user",
        error_delete: "Failed to delete user",
        error_activate: "Failed to activate user",
        error_deactivate: "Failed to deactivate user",
        error_loading_stats: "Error loading user statistics",
        error_loading_activity: "Error loading user activity"
    },

    dashboard: {
        title: 'Dashboard',
        average_daily_revenue: 'Average Daily Revenue',
        average_monthly_revenue: 'Average Monthly Revenue',
        revenue_by_category: 'Revenue by Category',
        monthly_revenue_trend: 'Monthly Revenue Trend',
        sales: {
            title: 'Sales',
            today: 'Today',
            this_month: 'This Month',
            total: 'Total',
            change: 'Change'
        },
        purchases: {
            title: 'Purchases',
            today: 'Today',
            this_month: 'This Month',
            total: 'Total',
            change: 'Change'
        },
        expenses: {
            title: 'Expenses',
            today: 'Today',
            this_month: 'This Month',
            total: 'Total',
            change: 'Change'
        },
        profit: {
            title: 'Profit',
            this_month: 'This Month',
            last_month: 'Last Month',
            change: 'Change'
        },
        returns: {
            title: 'Returns',
            sales_returns: 'Sales Returns',
            purchase_returns: 'Purchase Returns'
        },
        counts: {
            title: 'Counts',
            products: 'Products',
            customers: 'Customers',
            suppliers: 'Suppliers'
        },
        new_this_month: {
            title: 'New This Month',
            new_customers: 'New Customers',
            new_suppliers: 'New Suppliers'
        },
        top_products: {
            title: 'Top Products'
        },
        recent_activity: {
            recent_sales: 'Recent Sales',
            recent_purchases: 'Recent Purchases',
            customer: 'Customer',
            supplier: 'Supplier',
            amount: 'Amount',
            date: 'Date'
        },
        top_customers: {
            title: 'Top Customers',
            total_spent: 'Total Spent',
            number_of_purchases: 'Number of Purchases'
        }
    },

    activity_log: {
        title: 'Activity Log',
        date: 'Date',
        user: 'User',
        event: 'Event',
        subject: 'Subject',
        description: 'Description',
        all_subjects: 'All Subjects',
        all_events: 'All Events',
        created: 'Created',
        updated: 'Updated',
        deleted: 'Deleted',
        system: 'System',
        filters: {
            subject_type: 'Subject Type',
            event: 'Event',
            start_date: 'Start Date',
            end_date: 'End Date'
        }
    },

    // Tabs
    tabs: {
        details: 'Details',
        stock: 'Stock',
        pricing: 'Pricing',
        history: 'History'
    },

    stats: {
        total_products: 'Total Products',
        in_stock: 'In Stock',
        low_stock: 'Low Stock',
        out_of_stock: 'Out of Stock',
        stock_status: {
            in_stock: 'In Stock',
            low_stock: 'Low Stock',
            out_of_stock: 'Out of Stock'
        }
    }
};
