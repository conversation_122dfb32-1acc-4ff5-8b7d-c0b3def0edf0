import '../css/app.css';

// Import icon libraries
import '@quasar/extras/roboto-font-latin-ext/roboto-font-latin-ext.css'
import '@quasar/extras/material-icons/material-icons.css'
import '@quasar/extras/fontawesome-v5/fontawesome-v5.css'
import '@quasar/extras/fontawesome-v6/fontawesome-v6.css'

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';
import { createPinia } from 'pinia';
import i18n from '@/lang/i18n';
import { Quasar, Dialog, Notify, Loading, Meta, AppFullscreen, AppVisibility, BottomSheet, Cookies, LocalStorage, SessionStorage } from 'quasar';
import AuthenticatedLayout from '@/layouts/AdminLayout.vue';
import UserLayout from '@/layouts/UserLayout.vue';
const pinia = createPinia();
const appName = import.meta.env.VITE_APP_NAME ?? import.meta.env.VITE_APP_FALLBACK_NAME ?? 'Daryay Bawar';

// Register service worker - do this as early as possible
if ('serviceWorker' in navigator) {
    // Register immediately
    navigator.serviceWorker.register('/service-worker.js')
        .then(registration => {
            console.log('[PWA] Service Worker registered with scope:', registration.scope);

            // Check if there's an update available
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                console.log('[PWA] New service worker installing');

                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        console.log('[PWA] New content available, refresh to update');
                        // Optionally show a UI to let the user refresh the page
                        if (window.Quasar && window.Quasar.Notify) {
                            window.Quasar.Notify.create({
                                message: 'New version available',
                                caption: 'Tap to refresh',
                                icon: 'update',
                                color: 'info',
                                position: 'top',
                                timeout: 10000,
                                actions: [
                                    { label: 'Refresh', color: 'white', handler: () => { window.location.reload(); } }
                                ]
                            });
                        }
                    } else if (newWorker.state === 'activated') {
                        console.log('[PWA] New service worker activated');
                    }
                });
            });

            // Check for version updates periodically
            setInterval(() => {
                if (navigator.serviceWorker.controller) {
                    const messageChannel = new MessageChannel();

                    messageChannel.port1.onmessage = (event) => {
                        if (event.data && event.data.type === 'VERSION_INFO') {
                            console.log('[PWA] Service Worker version:', event.data.version);
                            // Here you could compare versions and show update UI if needed
                        }
                    };

                    navigator.serviceWorker.controller.postMessage({
                        type: 'CHECK_FOR_UPDATES'
                    }, [messageChannel.port2]);
                }
            }, 60 * 60 * 1000); // Check once per hour

            return registration;
        })
        .catch(error => {
            console.error('[PWA] Service Worker registration failed:', error);
        });

    // Handle service worker controlled pages
    if (navigator.serviceWorker.controller) {
        console.log('[PWA] This page is currently controlled by:', navigator.serviceWorker.controller);
    }

    // Handle service worker updates
    navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('[PWA] Service worker controller changed');
    });

    // Special handling for iOS
    const ua = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
    const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                 (window.navigator.standalone === true);

    if (isIOS && isPWA) {
        console.log('[PWA] iOS PWA detected, applying special handling');

        // Force preload of logo and splash screen
        const preloadLogo = new Image();
        preloadLogo.src = '/logo.png';

        const preloadIcon = new Image();
        preloadIcon.src = '/apple-touch-icon.png';

        // Add apple-touch-icon links
        const linkIcon = document.createElement('link');
        linkIcon.rel = 'apple-touch-icon-precomposed';
        linkIcon.href = '/apple-touch-icon.png';
        document.head.appendChild(linkIcon);
    }
}

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: async (name) => {
        const pages = import.meta.glob('./Pages/**/*.vue', { eager: true });
        const page = pages[`./Pages/${name}.vue`];
        if (!page) {
            throw new Error(`Page ${name} not found`);
        }

        if ('default' in page && name.includes('Admin')) {
            page.default.layout = page.default.layout || AuthenticatedLayout;
        } else if ('default' in page && name.includes('Web')) {
            page.default.layout = page.default.layout || UserLayout;
        }
        return page.default;
    },
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .use(i18n)
            .use(Quasar, {
                plugins: {
                    Dialog,
                    Notify,
                    Loading,
                    Meta,
                    AppFullscreen,
                    AppVisibility,
                    BottomSheet,
                    Cookies,
                    LocalStorage,
                    SessionStorage
                },
                framework: {
                    cssAddon: true
                }
            })
            .use(pinia);

        app.mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});
