import { useI18n } from 'vue-i18n';
import { hasPermission } from './utils/permissions';

const getMenuItems = () => {
    const { t } = useI18n();

    return [
        {
            name: t('menus.dashboard'),
            icon: 'dashboard',
            route: 'admin.dashboard',
        },


        {
            name: t('menus.currency'),
            icon: 'attach_money',
            route: 'admin.currencies.index',
        },
        {
            name: t('menus.currency-exchange'),
            icon: 'swap_horiz',
            route: 'admin.currency-exchange-rates.index',
        },

        // User Management Section
        {
            name: t('menus.user-management'),
            icon: 'people',
            submenu: [
                {
                    name: t('menus.users'),
                    icon: 'person',
                    route: 'admin.users.index',
                    permission: 'view users'
                },
                {
                    name: t('menus.roles'),
                    icon: 'admin_panel_settings',
                    route: 'admin.roles.index',
                    permission: 'view roles'
                },
                {
                    name: t('menus.permissions'),
                    icon: 'key',
                    route: 'admin.permissions.index',
                    permission: 'view permissions'
                }
            ]
        }

    ];
};

// Function to filter menu items based on permissions
function getFilteredMenus(user) {
    const menuItems = getMenuItems();

    return menuItems.filter(item => {
        // If user is admin or super-admin, show all menus
        if (user?.roles?.some(role => ['admin', 'super-admin'].includes(role.name))) {
            return true;
        }

        // Check if user has permission for the main menu item
        if (item.permission && !hasPermission(item.permission)) {
            return false;
        }

        // If menu has submenu, filter submenu items
        if (item.submenu) {
            item.submenu = item.submenu.filter(subItem => {
                return !subItem.permission || hasPermission(subItem.permission);
            });
            // Only show menu if it has visible submenu items
            return item.submenu.length > 0;
        }

        return true;
    });
}

export { getMenuItems, getFilteredMenus };

