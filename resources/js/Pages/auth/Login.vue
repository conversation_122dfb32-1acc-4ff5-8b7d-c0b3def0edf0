<script setup>
import { ref } from "vue";
import { Head, Link, useForm } from "@inertiajs/vue3";
import { useI18n } from "@/composables/useI18n";
import { useQuasar } from "quasar";
import { router } from "@inertiajs/vue3";

const { t, locale } = useI18n();
const $q = useQuasar();
const showPassword = ref(false);

const props = defineProps({
  status: String,
  canResetPassword: {
    type: Boolean,
    default: false,
  },
});

const form = useForm({
  email: "",
  password: "",
  remember: false,
});

const submit = () => {
  form.post(route("login"), {
    onFinish: () => {
      form.reset("password");
    },
  });
};

// Language selection
const languages = [
  { code: "en", name: "English", flag: "us" },
  { code: "ar", name: "العربية", flag: "sa" },
  { code: "ku", name: "کوردی", flag: "iq" },
];

const currentLang = ref(locale.value);
const showLangMenu = ref(false);

const changeLanguage = (lang) => {
  currentLang.value = lang;
  locale.value = lang;
  $q.lang.set(lang);

  // Update the URL with the new language
  router.visit(route("login", { lang }), {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      showLangMenu.value = false;
    },
  });
};
</script>

<script>
import UserLayout from "@/layouts/UserLayout.vue";

export default {
  layout: UserLayout,
};
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <Head :title="t('pages.auth.login.title')" />

    <!-- Main Container -->
    <div class="min-h-screen flex items-center justify-center p-6">
      <!-- Login Card -->
      <div class="w-full max-w-md bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden login-card">
        <!-- Card Header -->
        <div class="px-10 pt-10 pb-8 text-center border-b border-gray-50">
          <!-- Logo -->
          <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl mb-8 shadow-xl">
            <img src="/logo.jpg" alt="Logo" class="w-20 h-20 rounded-2xl object-cover" />
          </div>

          <!-- Title -->
          <h1 class="text-3xl font-bold text-gray-900 mb-3">
            {{ t("pages.auth.login.title") }}
          </h1>
          <p class="text-gray-600 text-lg">
            {{ t("pages.auth.login.subtitle") }}
          </p>
        </div>

        <!-- Language Selector -->
        <div class="absolute top-4 right-4">
          <q-btn
            flat
            round
            dense
            icon="language"
            @click="showLangMenu = !showLangMenu"
            class="text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
          >
            <q-tooltip>{{ t("common.change_language") }}</q-tooltip>
          </q-btn>
          <q-menu v-model="showLangMenu" :offset="[0, 8]">
            <q-list style="min-width: 140px" class="bg-white rounded-lg shadow-lg border border-gray-200">
              <q-item
                v-for="lang in languages"
                :key="lang.code"
                clickable
                v-close-popup
                @click="changeLanguage(lang.code)"
                class="hover:bg-gray-50 transition-colors duration-200"
              >
                <q-item-section avatar>
                  <q-icon :name="`flag-${lang.flag}`" size="20px" />
                </q-item-section>
                <q-item-section class="text-gray-700">{{ lang.name }}</q-item-section>
                <q-item-section avatar v-if="currentLang === lang.code">
                  <q-icon name="check" color="blue" size="16px" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </div>

        <!-- Card Body -->
        <div class="px-10 py-10">
          <!-- Status Message -->
          <q-banner
            v-if="status"
            class="bg-green-50 text-green-700 rounded-lg mb-6 border border-green-200"
          >
            <template v-slot:avatar>
              <q-icon name="check_circle" color="green" />
            </template>
            {{ status }}
          </q-banner>

          <!-- Login Form -->
          <q-form @submit.prevent="submit" class="space-y-6">
            <!-- Email Field -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ t('pages.auth.login.email') }}
              </label>
              <q-input
                v-model="form.email"
                type="email"
                :error="!!form.errors.email"
                :error-message="form.errors.email"
                outlined
                class="clean-input"
                placeholder="Enter your email address"
              >
                <template v-slot:prepend>
                  <q-icon name="mail" color="gray-400" />
                </template>
              </q-input>
            </div>

            <!-- Password Field -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ t('pages.auth.login.password') }}
              </label>
              <q-input
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                :error="!!form.errors.password"
                :error-message="form.errors.password"
                outlined
                class="clean-input"
                placeholder="Enter your password"
              >
                <template v-slot:prepend>
                  <q-icon name="lock" color="gray-400" />
                </template>
                <template v-slot:append>
                  <q-icon
                    :name="showPassword ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer text-gray-400 hover:text-gray-600 transition-colors"
                    @click="showPassword = !showPassword"
                  />
                </template>
              </q-input>
            </div>

            <!-- Remember Me and Forgot Password -->
            <div class="flex items-center justify-between">
              <q-checkbox
                v-model="form.remember"
                :label="t('pages.auth.login.remember_me')"
                color="blue"
                class="text-sm text-gray-600"
              />
              <Link
                v-if="canResetPassword"
                :href="route('password.request')"
                class="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200 hover:underline"
              >
                {{ t("pages.auth.login.forgot_password") }}
              </Link>
            </div>

            <!-- Submit Button -->
            <div class="pt-2">
              <q-btn
                type="submit"
                color="blue"
                class="w-full clean-button"
                :loading="form.processing"
                :disabled="form.processing"
                size="lg"
                unelevated
                no-caps
              >
                <template v-if="!form.processing">
                  {{ t("pages.auth.login.login_button") }}
                </template>
                <template v-else>
                  <q-spinner class="mr-2" size="20px" />
                  {{ t("common.processing") || "Processing..." }}
                </template>
              </q-btn>
            </div>
          </q-form>
        </div>

        <!-- Card Footer -->
        <div class="px-10 py-8 bg-gray-50 border-t border-gray-100 text-center">
          <p class="text-gray-500 text-sm">
            © 2024 Daryay Bawar. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Login Card */
.login-card {
  position: relative;
  animation: slideInUp 0.6s ease-out;
  max-width: 480px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.1);
}

.login-card:hover {
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    0 15px 35px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Clean Input Styles */
.clean-input :deep(.q-field__control) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  background: #ffffff;
  transition: all 0.2s ease;
  height: 56px;
}

.clean-input :deep(.q-field__control):hover {
  border-color: #d1d5db;
}

.clean-input :deep(.q-field--focused .q-field__control) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clean-input :deep(.q-field__native) {
  color: #374151;
  font-size: 16px;
  padding-left: 16px;
}

.clean-input :deep(.q-field__native::placeholder) {
  color: #9ca3af;
}

.clean-input :deep(.q-field__prepend) {
  padding-left: 16px;
}

.clean-input :deep(.q-field__append) {
  padding-right: 16px;
}

/* Clean Button */
.clean-button {
  border-radius: 12px;
  height: 56px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s ease;
  background: #3b82f6;
  color: white;
}

.clean-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}

.clean-button:active {
  transform: translateY(0);
}

.clean-button:disabled {
  opacity: 0.6;
  transform: none;
}

/* RTL Support */
[dir="rtl"] .flex.items-center.justify-between {
  flex-direction: row-reverse;
}

[dir="rtl"] .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .q-input :deep(.q-field__control) {
  flex-direction: row-reverse;
}

[dir="rtl"] .q-input :deep(.q-field__marginal) {
  margin-right: 0;
  margin-left: 8px;
}

[dir="rtl"] .q-checkbox :deep(.q-checkbox__inner) {
  margin-right: 0;
  margin-left: 8px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .login-card {
    margin: 1rem;
    max-width: none;
  }

  .clean-input :deep(.q-field__control) {
    height: 48px;
  }

  .clean-button {
    height: 48px;
    font-size: 14px;
  }

  .clean-input :deep(.q-field__native) {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .login-card {
    margin: 0.5rem;
  }
}

/* Focus states for better accessibility */
.clean-input :deep(.q-field--focused .q-field__control) {
  outline: none;
}

.clean-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
</style>
