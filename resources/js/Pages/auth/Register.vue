<script setup lang="ts">
import { Head, Link, useForm } from '@inertiajs/vue3';
import { QForm, QInput, QBtn } from 'quasar';

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
});

const submit = () => {
    form.post(route('register'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation');
        },
    });
};
</script>
<script lang="ts">
import UserLayout from "@/layouts/UserLayout.vue";

export default {
    layout: UserLayout
}
</script>
<template>
    <Head title="Register" />

    <q-form @submit.prevent="submit" class="q-gutter-md">
        <div>
            <q-input
                v-model="form.name"
                label="Name"
                :error="!!form.errors.name"
                :error-message="form.errors.name"
                outlined
                required
                autofocus
                autocomplete="name"
            />
        </div>

        <div>
            <q-input
                v-model="form.email"
                label="Email"
                type="email"
                :error="!!form.errors.email"
                :error-message="form.errors.email"
                outlined
                required
                autocomplete="username"
            />
        </div>

        <div>
            <q-input
                v-model="form.password"
                label="Password"
                type="password"
                :error="!!form.errors.password"
                :error-message="form.errors.password"
                outlined
                required
                autocomplete="new-password"
            />
        </div>

        <div>
            <q-input
                v-model="form.password_confirmation"
                label="Confirm Password"
                type="password"
                :error="!!form.errors.password_confirmation"
                :error-message="form.errors.password_confirmation"
                outlined
                required
                autocomplete="new-password"
            />
        </div>

        <div class="row justify-end items-center q-mt-md">
            <Link
                :href="route('login')"
                class="text-primary q-mr-md"
            >
                Already registered?
            </Link>

            <q-btn
                type="submit"
                color="primary"
                :loading="form.processing"
                :disable="form.processing"
            >
                Register
            </q-btn>
        </div>
    </q-form>
</template>
