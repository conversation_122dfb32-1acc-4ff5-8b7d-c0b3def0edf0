<template>
  <q-page class="currency-exchange-page">
    <!-- Hero Section -->
    <section class="hero-section bg-gradient-to-br from-blue-600 to-purple-700 text-white">
      <div class="container mx-auto px-4 py-16">
        <div class="text-center mb-12">
          <h1 class="text-5xl font-bold mb-4">{{ $t('currency_exchange') }}</h1>
          <p class="text-xl opacity-90">{{ $t('real_time_rates_with_history') }}</p>

          <!-- Rate Update Status -->
          <div class="flex justify-center items-center mt-4">
            <q-chip
              :color="updateStatus.is_stale ? 'warning' : 'positive'"
              text-color="white"
              icon="update"
              size="sm"
            >
              Last updated: {{ formatLastUpdate(updateStatus.last_update) }}
            </q-chip>
            <q-btn
              @click="refreshRates"
              :loading="refreshing"
              icon="refresh"
              flat
              round
              color="white"
              class="q-ml-sm"
              size="sm"
            >
              <q-tooltip>Quick refresh</q-tooltip>
            </q-btn>
            <q-btn
              @click="showRateFetchingModal = true"
              icon="sync"
              flat
              round
              color="white"
              class="q-ml-sm"
              size="sm"
            >
              <q-tooltip>Fetch from multiple sources</q-tooltip>
            </q-btn>
            <q-btn
              @click="showCurrencyCreateModal = true"
              icon="add_circle"
              flat
              round
              color="white"
              class="q-ml-sm"
              size="sm"
            >
              <q-tooltip>Add new currency</q-tooltip>
            </q-btn>
            <q-btn
              @click="showExchangeRateCreateModal = true"
              icon="swap_horiz"
              flat
              round
              color="white"
              class="q-ml-sm"
              size="sm"
            >
              <q-tooltip>Add exchange rate</q-tooltip>
            </q-btn>
          </div>
        </div>

        <!-- Currency Converter -->
        <div class="max-w-4xl mx-auto">
          <q-card class="currency-converter-card bg-white text-dark rounded-2xl shadow-2xl">
            <q-card-section class="p-8">
              <div class="row q-gutter-md">
                <!-- From Currency -->
                <div class="col-12 col-md-5">
                  <q-select
                    v-model="fromCurrency"
                    :options="currencyOptions"
                    option-value="id"
                    option-label="display_name"
                    label="From Currency"
                    outlined
                    emit-value
                    map-options
                    class="currency-select"
                  >
                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section avatar>
                          <span class="text-2xl">{{ scope.opt.flag_icon }}</span>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ scope.opt.name }}</q-item-label>
                          <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-btn
                            @click.stop="toggleFavorite(scope.opt.id)"
                            :icon="isFavorite(scope.opt.id) ? 'star' : 'star_border'"
                            :color="isFavorite(scope.opt.id) ? 'amber' : 'grey'"
                            flat
                            round
                            size="sm"
                          />
                        </q-item-section>
                      </q-item>
                    </template>
                  </q-select>

                  <q-input
                    v-model.number="amount"
                    type="number"
                    label="Amount"
                    outlined
                    class="q-mt-md"
                    :min="fromCurrencyData?.min_amount || 0.01"
                    :max="fromCurrencyData?.max_amount"
                    step="0.01"
                    @input="convertCurrency"
                  >
                    <template v-slot:append>
                      <q-btn
                        @click="showQuickAmounts = !showQuickAmounts"
                        icon="calculate"
                        flat
                        round
                        size="sm"
                      />
                    </template>
                  </q-input>

                  <!-- Quick Amount Buttons -->
                  <div v-if="showQuickAmounts" class="q-mt-sm">
                    <q-btn-group flat>
                      <q-btn
                        v-for="quickAmount in quickAmounts"
                        :key="quickAmount"
                        @click="setQuickAmount(quickAmount)"
                        :label="quickAmount"
                        size="sm"
                        color="primary"
                        outline
                      />
                    </q-btn-group>
                  </div>
                </div>

                <!-- Swap Button -->
                <div class="col-12 col-md-2 flex items-center justify-center">
                  <q-btn
                    @click="swapCurrencies"
                    icon="swap_horiz"
                    round
                    color="primary"
                    size="lg"
                    class="swap-btn"
                  />
                </div>

                <!-- To Currency -->
                <div class="col-12 col-md-5">
                  <q-select
                    v-model="toCurrency"
                    :options="currencyOptions"
                    option-value="id"
                    option-label="display_name"
                    label="To Currency"
                    outlined
                    emit-value
                    map-options
                    class="currency-select"
                  >
                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section avatar>
                          <span class="text-2xl">{{ scope.opt.flag_icon }}</span>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ scope.opt.name }}</q-item-label>
                          <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-btn
                            @click.stop="toggleFavorite(scope.opt.id)"
                            :icon="isFavorite(scope.opt.id) ? 'star' : 'star_border'"
                            :color="isFavorite(scope.opt.id) ? 'amber' : 'grey'"
                            flat
                            round
                            size="sm"
                          />
                        </q-item-section>
                      </q-item>
                    </template>
                  </q-select>

                  <q-input
                    v-model="convertedAmount"
                    label="Converted Amount"
                    outlined
                    readonly
                    class="q-mt-md"
                  >
                    <template v-slot:append>
                      <q-btn
                        @click="copyToClipboard(convertedAmount)"
                        icon="content_copy"
                        flat
                        round
                        size="sm"
                      />
                    </template>
                  </q-input>
                </div>
              </div>

              <!-- Exchange Rate Info -->
              <div v-if="exchangeRate" class="q-mt-lg text-center">
                <q-chip
                  :color="rateChangeColor"
                  text-color="white"
                  icon="trending_up"
                  size="lg"
                >
                  1 {{ fromCurrencyData?.code }} = {{ exchangeRate }} {{ toCurrencyData?.code }}
                </q-chip>
                <p class="text-grey-6 q-mt-sm">
                  Last updated: {{ lastUpdated }}
                </p>

                <!-- Rate Alert Button -->
                <q-btn
                  @click="showRateAlert = true"
                  icon="notifications"
                  label="Set Rate Alert"
                  color="secondary"
                  outline
                  size="sm"
                  class="q-mt-sm"
                />
              </div>

              <!-- Calculation History -->
              <div v-if="calculationHistory.length > 0" class="q-mt-lg">
                <q-expansion-item
                  icon="history"
                  label="Recent Calculations"
                  header-class="text-primary"
                >
                  <q-list>
                    <q-item
                      v-for="(calc, index) in calculationHistory.slice(0, 5)"
                      :key="index"
                      clickable
                      @click="loadCalculation(calc)"
                    >
                      <q-item-section>
                        <q-item-label>
                          {{ calc.amount }} {{ calc.from }} = {{ calc.result }} {{ calc.to }}
                        </q-item-label>
                        <q-item-label caption>
                          Rate: {{ calc.rate }} • {{ calc.timestamp }}
                        </q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-expansion-item>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </section>

    <!-- Favorites Section -->
    <section v-if="favoriteCurrencies.length > 0" class="favorites-section py-8 bg-grey-1">
      <div class="container mx-auto px-4">
        <h2 class="text-2xl font-bold text-center mb-8">{{ $t('favorite_currencies') }}</h2>

        <div class="row q-gutter-md justify-center">
          <div
            v-for="currency in favoriteCurrencies"
            :key="currency.id"
            class="col-auto"
          >
            <q-card class="favorite-card cursor-pointer" @click="selectFavoriteCurrency(currency)">
              <q-card-section class="text-center q-pa-md">
                <div class="text-3xl q-mb-sm">{{ currency.flag_icon }}</div>
                <div class="text-h6">{{ currency.code }}</div>
                <div class="text-caption">{{ currency.name }}</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- Popular Exchange Rates -->
    <section class="popular-rates-section py-16 bg-grey-1">
      <div class="container mx-auto px-4">
        <div class="row items-center q-mb-lg">
          <div class="col">
            <h2 class="text-3xl font-bold">{{ $t('popular_exchange_rates') }}</h2>
          </div>
          <div class="col-auto">
            <q-btn-toggle
              v-model="rateViewMode"
              :options="[
                { label: 'Grid', value: 'grid', icon: 'grid_view' },
                { label: 'List', value: 'list', icon: 'list' }
              ]"
              color="primary"
              outline
            />
          </div>
        </div>

        <!-- Grid View -->
        <div v-if="rateViewMode === 'grid'" class="row q-gutter-md">
          <div
            v-for="rate in popularRates"
            :key="`${rate.from_currency.code}-${rate.to_currency.code}`"
            class="col-12 col-sm-6 col-md-4 col-lg-3"
          >
            <q-card class="rate-card h-full cursor-pointer" @click="selectRate(rate)">
              <q-card-section class="text-center">
                <div class="flex items-center justify-center q-mb-md">
                  <span class="text-3xl q-mr-sm">{{ rate.from_currency.flag_icon }}</span>
                  <q-icon name="arrow_forward" size="sm" class="q-mx-sm" />
                  <span class="text-3xl q-ml-sm">{{ rate.to_currency.flag_icon }}</span>
                </div>

                <h3 class="text-lg font-semibold">
                  {{ rate.from_currency.code }} / {{ rate.to_currency.code }}
                </h3>

                <div class="text-2xl font-bold text-primary q-mt-sm">
                  {{ formatRate(rate.rate, rate.to_currency.decimal_places) }}
                </div>

                <q-chip
                  :color="getChangeColor(0)"
                  text-color="white"
                  size="sm"
                  class="q-mt-sm"
                >
                  <q-icon name="history" class="q-mr-xs" />
                  View History
                </q-chip>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- List View -->
        <div v-else>
          <q-table
            :rows="popularRates"
            :columns="rateColumns"
            row-key="id"
            flat
            bordered
            :pagination="{ rowsPerPage: 10 }"
          >
            <template v-slot:body-cell-pair="props">
              <q-td :props="props">
                <div class="flex items-center">
                  <span class="text-lg q-mr-xs">{{ props.row.from_currency.flag_icon }}</span>
                  <span class="font-medium">{{ props.row.from_currency.code }}</span>
                  <q-icon name="arrow_forward" size="xs" class="q-mx-sm" />
                  <span class="text-lg q-mr-xs">{{ props.row.to_currency.flag_icon }}</span>
                  <span class="font-medium">{{ props.row.to_currency.code }}</span>
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-rate="props">
              <q-td :props="props">
                <span class="text-h6 text-primary">
                  {{ formatRate(props.row.rate, props.row.to_currency.decimal_places) }}
                </span>
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  @click="selectRate(props.row)"
                  icon="calculate"
                  color="primary"
                  flat
                  round
                  size="sm"
                />
              </q-td>
            </template>
          </q-table>
        </div>
      </div>
    </section>

    <!-- Enhanced Historical Chart Section -->
    <section v-if="showChart" class="chart-section py-16">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">{{ $t('rate_history') }}</h2>

        <q-card class="chart-card">
          <q-card-section>
            <div class="row items-center q-mb-md">
              <div class="col">
                <h3 class="text-xl font-semibold">
                  {{ fromCurrencyData?.code }} to {{ toCurrencyData?.code }} History
                </h3>
              </div>
              <div class="col-auto">
                <q-btn-toggle
                  v-model="chartPeriod"
                  :options="chartPeriodOptions"
                  @update:model-value="loadChartData"
                  color="primary"
                  outline
                />
              </div>
            </div>

            <div class="chart-container" style="height: 500px;">
              <div v-if="chartLoading" class="flex items-center justify-center h-full">
                <q-spinner-dots size="3rem" color="primary" />
                <span class="q-ml-md text-lg">Loading chart data...</span>
              </div>
              <div v-else-if="chartData.length === 0" class="flex items-center justify-center h-full text-grey-6">
                <q-icon name="show_chart" size="4rem" class="q-mr-md" />
                <span class="text-lg">No historical data available</span>
              </div>
              <div v-else class="h-full">
                <!-- Enhanced chart stats -->
                <div class="chart-stats q-mb-md">
                  <div class="row q-gutter-md">
                    <div class="col">
                      <q-card flat bordered class="text-center">
                        <q-card-section class="q-pa-sm">
                          <div class="text-caption text-grey-6">Highest</div>
                          <div class="text-h6 text-positive">{{ chartStats.highest }}</div>
                          <div class="text-caption">{{ chartStats.highestDate }}</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col">
                      <q-card flat bordered class="text-center">
                        <q-card-section class="q-pa-sm">
                          <div class="text-caption text-grey-6">Lowest</div>
                          <div class="text-h6 text-negative">{{ chartStats.lowest }}</div>
                          <div class="text-caption">{{ chartStats.lowestDate }}</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col">
                      <q-card flat bordered class="text-center">
                        <q-card-section class="q-pa-sm">
                          <div class="text-caption text-grey-6">Average</div>
                          <div class="text-h6">{{ chartStats.average }}</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col">
                      <q-card flat bordered class="text-center">
                        <q-card-section class="q-pa-sm">
                          <div class="text-caption text-grey-6">Volatility</div>
                          <div class="text-h6 text-warning">{{ chartStats.volatility }}%</div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </div>
                </div>

                <!-- Enhanced chart visualization -->
                <div class="chart-visual bg-grey-1 rounded" style="height: 350px; position: relative;">
                  <div class="absolute inset-0 p-4">
                    <div class="text-center q-mb-md">
                      <q-icon name="trending_up" size="3rem" class="text-primary" />
                      <div class="text-h6 q-mt-sm">Interactive Chart</div>
                      <div class="text-caption">{{ chartData.length }} data points for {{ chartPeriod }} days</div>
                    </div>

                    <!-- Simple line representation -->
                    <div class="chart-line-container" style="height: 200px; position: relative;">
                      <svg width="100%" height="100%" class="chart-svg">
                        <polyline
                          :points="chartPoints"
                          fill="none"
                          stroke="#1976d2"
                          stroke-width="2"
                        />
                        <circle
                          v-for="(point, index) in chartPointsArray"
                          :key="index"
                          :cx="point.x"
                          :cy="point.y"
                          r="3"
                          fill="#1976d2"
                          class="chart-point"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </section>

    <!-- Rate History Table -->
    <section v-if="showHistory" class="history-section py-16 bg-grey-1">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">{{ $t('recent_rate_changes') }}</h2>

        <q-card class="history-card">
          <q-table
            :rows="historyData"
            :columns="historyColumns"
            row-key="id"
            :pagination="{ rowsPerPage: 10 }"
            flat
            bordered
          >
            <template v-slot:body-cell-change="props">
              <q-td :props="props">
                <q-chip
                  :color="getChangeColor(props.row.change_percentage)"
                  text-color="white"
                  size="sm"
                >
                  <q-icon
                    :name="props.row.change_type === 'increase' ? 'trending_up' : props.row.change_type === 'decrease' ? 'trending_down' : 'trending_flat'"
                    class="q-mr-xs"
                  />
                  {{ props.row.change_percentage > 0 ? '+' : '' }}{{ props.row.change_percentage }}%
                </q-chip>
              </q-td>
            </template>

            <template v-slot:body-cell-source="props">
              <q-td :props="props">
                <q-badge :color="getSourceColor(props.row.source)">
                  {{ props.row.source }}
                </q-badge>
              </q-td>
            </template>
          </q-table>
        </q-card>
      </div>
    </section>

    <!-- Rate Alert Dialog -->
    <q-dialog v-model="showRateAlert">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">Set Rate Alert</div>
          <div class="text-subtitle2">Get notified when {{ fromCurrencyData?.code }}/{{ toCurrencyData?.code }} reaches your target rate</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            v-model.number="alertRate"
            type="number"
            label="Target Rate"
            outlined
            :hint="`Current rate: ${exchangeRate}`"
          />

          <q-select
            v-model="alertCondition"
            :options="[
              { label: 'Above', value: 'above' },
              { label: 'Below', value: 'below' }
            ]"
            label="Condition"
            outlined
            class="q-mt-md"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn label="Set Alert" color="primary" @click="setRateAlert" />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Rate Fetching Modal -->
    <RateFetchingModal
      v-model="showRateFetchingModal"
      :update-database="false"
      @completed="onRateFetchCompleted"
      @error="onRateFetchError"
    />

    <!-- Currency Create Modal -->
    <CurrencyCreateModal
      v-model="showCurrencyCreateModal"
      @created="onCurrencyCreated"
      @error="onCurrencyCreateError"
    />

    <!-- Exchange Rate Create Modal -->
    <ExchangeRateCreateModal
      v-model="showExchangeRateCreateModal"
      :currencies="currencies"
      @created="onExchangeRateCreated"
      @error="onExchangeRateCreateError"
    />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import axios from 'axios'
import RateFetchingModal from '@/components/RateFetchingModal.vue'
import CurrencyCreateModal from '@/components/CurrencyCreateModal.vue'
import ExchangeRateCreateModal from '@/components/ExchangeRateCreateModal.vue'

const { t } = useI18n()

// Reactive data
const currencies = ref([])
const popularRates = ref([])
const fromCurrency = ref(null)
const toCurrency = ref(null)
const amount = ref(1)
const convertedAmount = ref(0)
const exchangeRate = ref(null)
const lastUpdated = ref(null)
const loading = ref(false)
const showChart = ref(false)
const showHistory = ref(false)
const chartPeriod = ref('30')
const chartData = ref([])
const chartLoading = ref(false)
const historyData = ref([])
const showRateAlert = ref(false)
const alertRate = ref(null)
const alertCondition = ref('above')
const showQuickAmounts = ref(false)
const quickAmounts = ref([1, 10, 100, 500, 1000])
const rateViewMode = ref('grid')
const calculationHistory = ref([])
const favoriteCurrencies = ref([])
const favorites = ref(JSON.parse(localStorage.getItem('currency_favorites') || '[]'))
const updateStatus = ref({ last_update: null, is_stale: true })
const refreshing = ref(false)
const showRateFetchingModal = ref(false)
const showCurrencyCreateModal = ref(false)
const showExchangeRateCreateModal = ref(false)

// Chart data
const chartPeriodOptions = [
  { label: '7D', value: '7' },
  { label: '30D', value: '30' },
  { label: '90D', value: '90' },
  { label: '1Y', value: '365' }
]

// History table columns
const historyColumns = [
  { name: 'date', label: 'Date', field: 'effective_date', sortable: true, format: val => new Date(val).toLocaleDateString() },
  { name: 'rate', label: 'Rate', field: 'rate', sortable: true, format: val => Number(val).toFixed(4) },
  { name: 'change', label: 'Change', field: 'change_percentage', sortable: true },
  { name: 'source', label: 'Source', field: 'source', sortable: true },
  { name: 'notes', label: 'Notes', field: 'notes' }
]

// Rate table columns
const rateColumns = [
  { name: 'pair', label: 'Currency Pair', field: 'pair', sortable: false },
  { name: 'rate', label: 'Exchange Rate', field: 'rate', sortable: true },
  { name: 'updated', label: 'Last Updated', field: 'updated_at', sortable: true, format: val => new Date(val).toLocaleString() },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false }
]

// Computed properties
const currencyOptions = computed(() => {
  return currencies.value.map(currency => ({
    ...currency,
    display_name: `${currency.name} (${currency.code})`
  }))
})

const fromCurrencyData = computed(() => {
  return currencies.value.find(c => c.id === fromCurrency.value)
})

const toCurrencyData = computed(() => {
  return currencies.value.find(c => c.id === toCurrency.value)
})

const rateChangeColor = computed(() => {
  return 'primary'
})

const chartStats = computed(() => {
  if (chartData.value.length === 0) {
    return { highest: 0, lowest: 0, average: 0, highestDate: '', lowestDate: '', volatility: '0%' }
  }

  const rates = chartData.value.map(d => parseFloat(d.rate))
  const highest = Math.max(...rates)
  const lowest = Math.min(...rates)
  const average = rates.reduce((a, b) => a + b, 0) / rates.length
  const highestItem = chartData.value.find(d => parseFloat(d.rate) === highest)
  const lowestItem = chartData.value.find(d => parseFloat(d.rate) === lowest)
  const highestDate = highestItem ? new Date(highestItem.date).toLocaleDateString() : ''
  const lowestDate = lowestItem ? new Date(lowestItem.date).toLocaleDateString() : ''
  const volatility = rates.length > 1 ? Math.sqrt(rates.reduce((a, b) => a + Math.pow(b - average, 2), 0) / rates.length) : 0

  return {
    highest: highest.toFixed(4),
    lowest: lowest.toFixed(4),
    average: average.toFixed(4),
    highestDate,
    lowestDate,
    volatility: (volatility / average * 100).toFixed(2) + '%'
  }
})

const chartPoints = computed(() => {
  if (chartData.value.length === 0) return ''

  const rates = chartData.value.map(d => parseFloat(d.rate))
  const minRate = Math.min(...rates)
  const maxRate = Math.max(...rates)
  const range = maxRate - minRate || 1

  return chartData.value.map((point, index) => {
    const x = (index / (chartData.value.length - 1)) * 100
    const y = 100 - ((parseFloat(point.rate) - minRate) / range) * 100
    return `${x},${y}`
  }).join(' ')
})

const chartPointsArray = computed(() => {
  if (chartData.value.length === 0) return []

  const rates = chartData.value.map(d => parseFloat(d.rate))
  const minRate = Math.min(...rates)
  const maxRate = Math.max(...rates)
  const range = maxRate - minRate || 1

  return chartData.value.map((point, index) => {
    const x = (index / (chartData.value.length - 1)) * 100
    const y = 100 - ((parseFloat(point.rate) - minRate) / range) * 100
    return { x: `${x}%`, y: `${y}%` }
  })
})

// Methods
const loadCurrencies = async () => {
  try {
    const response = await axios.get('/api/exchange/currencies')
    currencies.value = response.data.data

    // Set default currencies (USD and IQD)
    const usd = currencies.value.find(c => c.code === 'USD')
    const iqd = currencies.value.find(c => c.code === 'IQD')

    if (usd && iqd) {
      fromCurrency.value = usd.id
      toCurrency.value = iqd.id
    }
  } catch (error) {
    console.error('Error loading currencies:', error)
  }
}

const loadPopularRates = async () => {
  try {
    const response = await axios.get('/api/exchange/rates')
    popularRates.value = response.data.data.slice(0, 8)
  } catch (error) {
    console.error('Error loading popular rates:', error)
  }
}

const convertCurrency = async () => {
  if (!fromCurrency.value || !toCurrency.value || !amount.value) {
    return
  }

  if (fromCurrency.value === toCurrency.value) {
    convertedAmount.value = amount.value
    exchangeRate.value = 1
    return
  }

  loading.value = true

  try {
    const fromCode = fromCurrencyData.value?.code
    const toCode = toCurrencyData.value?.code

    const response = await axios.get('/api/exchange/convert', {
      params: {
        from: fromCode,
        to: toCode,
        amount: amount.value
      }
    })

    if (response.data.success) {
      convertedAmount.value = response.data.data.converted_amount
      exchangeRate.value = response.data.data.rate
      lastUpdated.value = new Date(response.data.data.last_updated).toLocaleString()
      showChart.value = true
      loadChartData()
      loadHistoryData()

      // Save to calculation history
      saveCalculationHistory({
        from: fromCode,
        to: toCode,
        amount: amount.value,
        result: response.data.data.converted_amount,
        rate: response.data.data.rate,
        timestamp: new Date().toLocaleString()
      })
    }
  } catch (error) {
    console.error('Error converting currency:', error)
  } finally {
    loading.value = false
  }
}

const swapCurrencies = () => {
  const temp = fromCurrency.value
  fromCurrency.value = toCurrency.value
  toCurrency.value = temp
  convertCurrency()
}

const loadChartData = async () => {
  if (!fromCurrencyData.value || !toCurrencyData.value) return

  chartLoading.value = true

  try {
    const response = await axios.get('/api/exchange/history', {
      params: {
        from: fromCurrencyData.value.code,
        to: toCurrencyData.value.code,
        period: chartPeriod.value
      }
    })

    if (response.data.success) {
      chartData.value = response.data.data.history
    }
  } catch (error) {
    console.error('Error loading chart data:', error)
    chartData.value = []
  } finally {
    chartLoading.value = false
  }
}

const loadHistoryData = async () => {
  if (!fromCurrencyData.value || !toCurrencyData.value) return

  try {
    const response = await axios.get('/api/exchange/history', {
      params: {
        from: fromCurrencyData.value.code,
        to: toCurrencyData.value.code,
        period: 30
      }
    })

    if (response.data.success) {
      historyData.value = response.data.data.history.slice(0, 20) // Show last 20 entries
      showHistory.value = historyData.value.length > 0
    }
  } catch (error) {
    console.error('Error loading history data:', error)
  }
}

const selectRate = (rate) => {
  fromCurrency.value = rate.from_currency_id
  toCurrency.value = rate.to_currency_id
  convertCurrency()
}

const formatRate = (rate, decimalPlaces = 4) => {
  return Number(rate).toFixed(decimalPlaces)
}

const getChangeColor = (changePercentage) => {
  if (changePercentage > 0) return 'positive'
  if (changePercentage < 0) return 'negative'
  return 'grey'
}

const getSourceColor = (source) => {
  switch (source) {
    case 'api': return 'blue'
    case 'automatic': return 'green'
    case 'manual': return 'orange'
    default: return 'grey'
  }
}

const toggleFavorite = (currencyId) => {
  const index = favorites.value.indexOf(currencyId)
  if (index > -1) {
    favorites.value.splice(index, 1)
  } else {
    favorites.value.push(currencyId)
  }
  localStorage.setItem('currency_favorites', JSON.stringify(favorites.value))
  updateFavoriteCurrencies()
}

const isFavorite = (currencyId) => {
  return favorites.value.includes(currencyId)
}

const updateFavoriteCurrencies = () => {
  favoriteCurrencies.value = currencies.value.filter(c => favorites.value.includes(c.id))
}

const setQuickAmount = (amount) => {
  amount.value = amount
  convertCurrency()
}

const loadCalculation = (calc) => {
  const fromCurr = currencies.value.find(c => c.code === calc.from)
  const toCurr = currencies.value.find(c => c.code === calc.to)

  if (fromCurr && toCurr) {
    fromCurrency.value = fromCurr.id
    toCurrency.value = toCurr.id
    amount.value = calc.amount
    convertCurrency()
  }
}

const selectFavoriteCurrency = (currency) => {
  if (!fromCurrency.value) {
    fromCurrency.value = currency.id
  } else if (!toCurrency.value || fromCurrency.value === currency.id) {
    toCurrency.value = currency.id
  } else {
    fromCurrency.value = currency.id
  }
  convertCurrency()
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text.toString())
    // Show success notification
    console.log('Copied to clipboard:', text)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

const setRateAlert = () => {
  const alert = {
    from: fromCurrencyData.value?.code,
    to: toCurrencyData.value?.code,
    targetRate: alertRate.value,
    condition: alertCondition.value,
    currentRate: exchangeRate.value,
    timestamp: new Date().toISOString()
  }

  // Store alert in localStorage for now
  const alerts = JSON.parse(localStorage.getItem('rate_alerts') || '[]')
  alerts.push(alert)
  localStorage.setItem('rate_alerts', JSON.stringify(alerts))

  showRateAlert.value = false
  console.log('Rate alert set:', alert)
}

const formatLastUpdate = (lastUpdate) => {
  if (!lastUpdate) return 'Never'
  const now = new Date()
  const update = new Date(lastUpdate)
  const diffMinutes = Math.floor((now - update) / (1000 * 60))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  return update.toLocaleDateString()
}

const refreshRates = async () => {
  refreshing.value = true
  try {
    await loadPopularRates()
    await loadUpdateStatus()
    if (fromCurrency.value && toCurrency.value) {
      await convertCurrency()
    }
  } catch (error) {
    console.error('Error refreshing rates:', error)
  } finally {
    refreshing.value = false
  }
}

const loadUpdateStatus = async () => {
  try {
    const response = await axios.get('/api/exchange/update-status')
    if (response.data.success) {
      updateStatus.value = response.data.data
    }
  } catch (error) {
    console.error('Error loading update status:', error)
    updateStatus.value = {
      last_update: new Date(),
      is_stale: false
    }
  }
}

const saveCalculationHistory = (calculation) => {
  calculationHistory.value.unshift(calculation)
  if (calculationHistory.value.length > 20) {
    calculationHistory.value = calculationHistory.value.slice(0, 20)
  }
  localStorage.setItem('calculation_history', JSON.stringify(calculationHistory.value))
}

const onRateFetchCompleted = async (result) => {
  showRateFetchingModal.value = false

  if (result.success) {
    await loadPopularRates()
    await loadUpdateStatus()
    if (fromCurrency.value && toCurrency.value) {
      await convertCurrency()
    }

    // Show success notification with details
    const message = `Successfully fetched rates from ${result.data.successful_providers_count} sources`
    console.log('Rate fetch completed:', result)
  }
}

const onRateFetchError = (error) => {
  showRateFetchingModal.value = false
  console.error('Rate fetch error:', error)
}

const onCurrencyCreated = async (result) => {
  showCurrencyCreateModal.value = false

  if (result.success) {
    await loadCurrencies()
    console.log('Currency created successfully:', result)
  }
}

const onCurrencyCreateError = (error) => {
  showCurrencyCreateModal.value = false
  console.error('Currency create error:', error)
}

const onExchangeRateCreated = async (result) => {
  showExchangeRateCreateModal.value = false

  if (result.success) {
    await loadPopularRates()
    if (fromCurrency.value && toCurrency.value) {
      await convertCurrency()
    }
    console.log('Exchange rate created successfully:', result)
  }
}

const onExchangeRateCreateError = (error) => {
  showExchangeRateCreateModal.value = false
  console.error('Exchange rate create error:', error)
}

// Watchers
watch([fromCurrency, toCurrency], () => {
  convertCurrency()
})

// Lifecycle
onMounted(() => {
  loadCurrencies()
  loadPopularRates()
  loadUpdateStatus()

  // Load saved data from localStorage
  const savedHistory = localStorage.getItem('calculation_history')
  if (savedHistory) {
    calculationHistory.value = JSON.parse(savedHistory)
  }

  // Update favorite currencies when currencies are loaded
  watch(currencies, () => {
    updateFavoriteCurrencies()
  }, { immediate: true })
})
</script>

<style scoped>
.currency-exchange-page {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.currency-converter-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.currency-select {
  font-size: 1.1rem;
}

.swap-btn {
  transition: transform 0.3s ease;
}

.swap-btn:hover {
  transform: rotate(180deg);
}

.rate-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
}

.rate-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-card, .history-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart-container {
  border-radius: 8px;
}

.chart-visual {
  border: 2px dashed #e0e0e0;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.chart-point {
  cursor: pointer;
  transition: r 0.2s ease;
}

.chart-point:hover {
  r: 5;
}

.favorite-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
  min-width: 100px;
}

.favorite-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.favorites-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.rate-card {
  position: relative;
  overflow: hidden;
}

.rate-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.rate-card:hover::before {
  left: 100%;
}

.currency-select .q-field__control {
  font-weight: 500;
}

.chart-stats .q-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 0;
  }

  .currency-converter-card {
    margin: 0 1rem;
  }

  .chart-container {
    height: 300px !important;
  }

  .chart-visual {
    height: 200px !important;
  }
}
</style>
