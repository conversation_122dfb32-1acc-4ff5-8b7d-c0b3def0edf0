<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

// Set viewport meta tag for mobile responsiveness
$q.meta.set({
  viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
});

const currencies = ref([
  // ... existing currencies data
]);

const updateInterval = ref(null);
const lastUpdatedTime = ref(new Date());
const lastUpdated = ref('');
const showCalculator = ref(false);
const selectedCurrency = ref(null);
const amount = ref(1);
const operation = ref(null);
const secondAmount = ref(null);
const showSecondInput = ref(false);
const result = ref(null);
const activeTab = ref('buy');
const calculationHistory = ref([]);
const digitalTime = ref('');

// Language translations
const translations = {
  ku: {
    title: 'نرخی دراوەکان',
    lastUpdate: 'دوایین نوێکردنەوە',
    // ... rest of translations
  },
  en: {
    title: 'Currency Rates',
    lastUpdate: 'Last Update',
    // ... rest of translations
  },
  ar: {
    title: 'أسعار العملات',
    lastUpdate: 'آخر تحديث',
    // ... rest of translations
  }
};

// Language state
const currentLang = ref('ku');
const languages = [
  { code: 'ku', name: 'کوردی', flag: 'iq' },
  { code: 'en', name: 'English', flag: 'gb' },
  { code: 'ar', name: 'العربية', flag: 'sa' }
];

// Computed translation
const t = computed(() => translations[currentLang.value]);

// Direction computed
const isRTL = computed(() => currentLang.value === 'ku' || currentLang.value === 'ar');

// Add isMobile detection
const isMobile = ref(false);

// Function to check if device is mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768 ||
                   ('ontouchstart' in window) ||
                   (navigator.maxTouchPoints > 0);
};

// Update notification message
const showNotification = (message) => {
  $q.notify({
    message: translations[currentLang.value][message],
    color: 'negative',
    icon: 'error',
    position: 'top'
  });
};

// Computed properties
const usdRate = computed(() => {
  const usd = currencies.value.find(c => c.currency_code === 'USD');
  return usd ? usd.buy : 1;
});

// Function to convert to USD
const convertToUSD = (amount, rate) => {
  return amount / rate;
};

// Function to format currency
const formatCurrency = (value, currency = 'IQD') => {
  if (currency === 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  }
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value) + ' د.ع';
};

const setAmount = (value) => {
  amount.value = value;
  calculateExchange();
};

const calculateExchange = () => {
  if (!selectedCurrency.value || !amount.value) return;

  const buyResult = amount.value * selectedCurrency.value.buy;
  const saleResult = amount.value * selectedCurrency.value.sale;

  result.value = {
    buy: {
      iqd: formatCurrency(buyResult),
      usd: formatCurrency(convertToUSD(buyResult, usdRate.value), 'USD')
    },
    sale: {
      iqd: formatCurrency(saleResult),
      usd: formatCurrency(convertToUSD(saleResult, usdRate.value), 'USD')
    },
    timestamp: new Date().toISOString()
  };

  // Add to history
  calculationHistory.value.unshift({
    currency: selectedCurrency.value,
    amount: amount.value,
    result: result.value,
    type: activeTab.value,
    timestamp: new Date().toISOString()
  });

  // Keep only last 5 calculations
  if (calculationHistory.value.length > 5) {
    calculationHistory.value.pop();
  }
};

const openCalculator = (currency) => {
  selectedCurrency.value = currency;
  amount.value = 1;
  result.value = null;
  activeTab.value = 'buy';
  showCalculator.value = true;
};

const performOperation = (op) => {
  if (!amount.value) return;

  operation.value = op;
  showSecondInput.value = true;
  secondAmount.value = null;
};

const calculateFinal = () => {
  if (!amount.value || !secondAmount.value || !operation.value) return;

  let finalAmount;
  let operationSymbol;

  switch (operation.value) {
    case '+':
      finalAmount = amount.value + secondAmount.value;
      operationSymbol = '+';
      break;
    case '-':
      finalAmount = amount.value - secondAmount.value;
      operationSymbol = '-';
      break;
    case '*':
      finalAmount = amount.value * secondAmount.value;
      operationSymbol = '×';
      break;
    case '/':
      if (secondAmount.value === 0) {
        showNotification('divideByZero');
        return;
      }
      finalAmount = amount.value / secondAmount.value;
      operationSymbol = '÷';
      break;
    default:
      return;
  }

  amount.value = finalAmount;
  calculateExchange();
  showSecondInput.value = false;
  operation.value = null;
  secondAmount.value = null;
};

const clearOperation = () => {
  operation.value = null;
  showSecondInput.value = false;
  secondAmount.value = null;
};

const clearAll = () => {
  amount.value = 1;
  clearOperation();
  calculateExchange();
};

// Update the clock
const updateClock = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  digitalTime.value = `${hours}:${minutes}:${seconds}`;

  // Update analog clock hands
  const hoursDeg = (now.getHours() % 12) * 30 + now.getMinutes() * 0.5;
  const minutesDeg = now.getMinutes() * 6;
  const secondsDeg = now.getSeconds() * 6;

  document.documentElement.style.setProperty('--hours-rotation', `${hoursDeg}deg`);
  document.documentElement.style.setProperty('--minutes-rotation', `${minutesDeg}deg`);
  document.documentElement.style.setProperty('--seconds-rotation', `${secondsDeg}deg`);
};

// Add mouseX and mouseY refs for parallax
const mouseX = ref(0);
const mouseY = ref(0);
const parallaxActive = ref(false);

// Add parallax effect
const handleParallax = (event) => {
  if (!parallaxActive.value) return;

  const header = event.currentTarget;
  const rect = header.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  // Calculate the position relative to the center of the header (values between -1 and 1)
  mouseX.value = (event.clientX - centerX) / (rect.width / 2);
  mouseY.value = (event.clientY - centerY) / (rect.height / 2);

  // Calculate distance from center for lighting effect (0 to 1)
  const distance = Math.sqrt(mouseX.value * mouseX.value + mouseY.value * mouseY.value);
  const brightness = 1 + (0.1 * (1 - distance));
  const saturation = 1 + (0.2 * (1 - distance));

  // Update CSS properties for advanced wave effects
  document.documentElement.style.setProperty('--mouse-x', mouseX.value);
  document.documentElement.style.setProperty('--mouse-y', mouseY.value);
  document.documentElement.style.setProperty('--wave-brightness', brightness.toString());
  document.documentElement.style.setProperty('--wave-saturation', saturation.toString());
};

// Enable parallax with a smooth transition
const enableParallax = () => {
  parallaxActive.value = true;
  document.documentElement.style.setProperty('--wave-transition', '0.5s');
};

// Disable parallax with a smooth reset
const disableParallax = () => {
  document.documentElement.style.setProperty('--wave-transition', '1s');
  setTimeout(() => {
    parallaxActive.value = false;
    mouseX.value = 0;
    mouseY.value = 0;
    document.documentElement.style.setProperty('--mouse-x', 0);
    document.documentElement.style.setProperty('--mouse-y', 0);
  }, 50);
};

// Add scroll position tracking for waves
const scrollY = ref(0);

// Update scroll position for wave effects
const handleScroll = () => {
  scrollY.value = window.scrollY;

  // Update wave effect based on scroll position
  const waveOffset = Math.min(scrollY.value / 10, 20);
  document.documentElement.style.setProperty('--wave-scroll-offset', `${waveOffset}px`);
};

// Weather data
const weatherData = ref({
  temperature: null,
  condition: 'cloudy',
  location: '',
  loading: true,
  error: null,
  coords: {
    latitude: null,
    longitude: null
  }
});

const weatherIcons = {
  'sunny': 'wb_sunny',
  'partly-cloudy': 'partly_cloudy_day',
  'cloudy': 'cloud',
  'rainy': 'water_drop',
  'stormy': 'thunderstorm',
  'snowy': 'ac_unit',
  'foggy': 'filter_drama'
};

const weatherColors = {
  'sunny': 'orange',
  'partly-cloudy': 'light-blue',
  'cloudy': 'blue-grey',
  'rainy': 'blue',
  'stormy': 'deep-purple',
  'snowy': 'cyan',
  'foggy': 'grey'
};

// Function to get user's current location
const getUserLocation = () => {
  weatherData.value.loading = true;
  weatherData.value.error = null;

  // Check if geolocation is supported by the browser
  if (!navigator.geolocation) {
    weatherData.value.error = t.value.weather.error_not_supported;
    weatherData.value.loading = false;
    return;
  }

  // Get current position
  navigator.geolocation.getCurrentPosition(
    // Success callback
    (position) => {
      weatherData.value.coords = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude
      };
      // After getting coordinates, fetch the weather
      getLocationName(position.coords.latitude, position.coords.longitude);
      fetchWeatherData(position.coords.latitude, position.coords.longitude);
    },
    // Error callback
    (error) => {
      console.error('Error getting location:', error);

      // Different error messages based on error code
      switch(error.code) {
        case error.PERMISSION_DENIED:
          weatherData.value.error = t.value.weather.error_permission_denied;
          break;
        case error.POSITION_UNAVAILABLE:
          weatherData.value.error = t.value.weather.error_unavailable;
          break;
        case error.TIMEOUT:
          weatherData.value.error = t.value.weather.error_timeout;
          break;
        default:
          weatherData.value.error = t.value.weather.error_unknown;
      }

      weatherData.value.loading = false;
    },
    // Options
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    }
  );
};

// Function to get location name from coordinates using reverse geocoding
const getLocationName = (latitude, longitude) => {
  // In a real app, you would use a geocoding service API here
  // For this demo, we'll simulate a few locations based on coordinates

  // Some hardcoded locations for demo purposes
  const locations = [
    { lat: 35.5569, lng: 45.4329, name: 'Slemani' },
    { lat: 36.1911, lng: 44.0091, name: 'Erbil' },
    { lat: 37.0782, lng: 43.3197, name: 'Duhok' },
    { lat: 35.4679, lng: 44.3927, name: 'Kirkuk' }
  ];

  // Find closest match or use a default
  let closestLocation = null;
  let minDistance = Infinity;

  locations.forEach(loc => {
    const distance = Math.sqrt(
      Math.pow(loc.lat - latitude, 2) +
      Math.pow(loc.lng - longitude, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestLocation = loc;
    }
  });

  if (closestLocation && minDistance < 1) { // Within ~100km
    weatherData.value.location = closestLocation.name;
  } else {
    // If no close match, just display the coordinates in a friendly way
    const lat = Math.abs(latitude).toFixed(2) + (latitude >= 0 ? '°N' : '°S');
    const lng = Math.abs(longitude).toFixed(2) + (longitude >= 0 ? '°E' : '°W');
    weatherData.value.location = `${lat}, ${lng}`;
  }
};

// Function to fetch weather data based on coordinates
const fetchWeatherData = (latitude, longitude) => {
  // In a real app, you would call a weather API here with the coordinates
  // For this demo, we'll just simulate it with random data

  setTimeout(() => {
    const conditions = ['sunny', 'partly-cloudy', 'cloudy', 'rainy', 'stormy', 'snowy', 'foggy'];

    // Use deterministic "random" based on coordinates to get consistent weather for same location
    const locationSeed = (latitude * 10 + longitude) % 100;
    const dayOfYear = Math.floor((new Date() - new Date(new Date().getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
    const seed = locationSeed + dayOfYear;

    // Get deterministic condition and temperature based on seed
    const conditionIndex = Math.floor(seed % conditions.length);
    const condition = conditions[conditionIndex];

    // Temperature based on condition and location
    let baseTemp;
    switch(condition) {
      case 'sunny': baseTemp = 30; break;
      case 'partly-cloudy': baseTemp = 25; break;
      case 'cloudy': baseTemp = 22; break;
      case 'rainy': baseTemp = 18; break;
      case 'stormy': baseTemp = 16; break;
      case 'snowy': baseTemp = 0; break;
      case 'foggy': baseTemp = 15; break;
      default: baseTemp = 20;
    }

    // Add some variation based on the seed
    const tempVariation = (seed % 10) - 5;
    const finalTemp = Math.round(baseTemp + tempVariation);

    weatherData.value = {
      ...weatherData.value,
      temperature: finalTemp,
      condition: condition,
      loading: false
    };
  }, 800);
};

// Function to refresh weather data
const refreshWeather = () => {
  if (weatherData.value.coords.latitude && weatherData.value.coords.longitude) {
    weatherData.value.loading = true;
    fetchWeatherData(weatherData.value.coords.latitude, weatherData.value.coords.longitude);
  } else {
    getUserLocation();
  }
};

// Add touch event handlers
const handleTouchStart = (event) => {
  // Disable parallax effects on touch devices
  disableParallax();
};

// Add sea wave animation refs
const waveHeight = ref(0);
const wavePhase = ref(0);

// Add wave animation
const animateWaves = () => {
  wavePhase.value = (wavePhase.value + 0.02) % (2 * Math.PI);
  waveHeight.value = Math.sin(wavePhase.value) * 5;
  requestAnimationFrame(animateWaves);
};

// Add floating elements animation refs
const floatingElements = ref([]);

// Generate floating elements
const generateFloatingElements = () => {
  const elements = [];
  const types = ['bubble', 'seaweed', 'fish', 'starfish'];

  // Generate fewer elements on mobile
  const count = isMobile.value ? 5 : 12;

  for (let i = 0; i < count; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    elements.push({
      id: i,
      type,
      size: Math.random() * 20 + 10,
      x: Math.random() * 100,
      y: Math.random() * 100,
      speed: Math.random() * 10 + 5,
      delay: Math.random() * 5,
      direction: Math.random() > 0.5 ? 'left' : 'right',
      rotate: Math.random() * 360
    });
  }

  floatingElements.value = elements;
};

// Add dark mode ref
const darkMode = ref(false);

// Function to toggle dark mode
const toggleDarkMode = () => {
  darkMode.value = !darkMode.value;
  document.documentElement.classList.toggle('dark-theme', darkMode.value);

  // Store preference in localStorage
  localStorage.setItem('darkMode', darkMode.value ? 'true' : 'false');
};

onMounted(() => {
  // Check if device is mobile
  checkMobile();
  window.addEventListener('resize', checkMobile);

  lastUpdatedTime.value = new Date();

  // Define the formatTimeAgo function inside onMounted
  const formatTimeAgo = (date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 5) {
      return t.value.timeAgo.justNow;
    }

    if (diffInSeconds < 60) {
      return t.value.timeAgo.seconds.replace('{s}', diffInSeconds);
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes === 1) {
      return t.value.timeAgo.minute;
    }
    if (diffInMinutes < 60) {
      return t.value.timeAgo.minutes.replace('{s}', diffInMinutes);
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours === 1) {
      return t.value.timeAgo.hour;
    }
    if (diffInHours < 24) {
      return t.value.timeAgo.hours.replace('{s}', diffInHours);
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) {
      return t.value.timeAgo.day;
    }
    return t.value.timeAgo.days.replace('{s}', diffInDays);
  };

  // Function to update the time ago display
  const updateTimeAgo = () => {
    lastUpdated.value = formatTimeAgo(lastUpdatedTime.value);
  };

  // Initial update
  updateTimeAgo();

  updateClock();
  setInterval(updateClock, 1000);

  // Update time every 10 seconds
  updateInterval.value = window.setInterval(() => {
    lastUpdatedTime.value = new Date();
    updateTimeAgo();
  }, 10000);

  animateWaves();
  generateFloatingElements();

  // Set default CSS variables for waves
  document.documentElement.style.setProperty('--mouse-x', '0');
  document.documentElement.style.setProperty('--mouse-y', '0');
  document.documentElement.style.setProperty('--wave-transition', '0.5s');
  document.documentElement.style.setProperty('--wave-brightness', '1');
  document.documentElement.style.setProperty('--wave-saturation', '1');
  document.documentElement.style.setProperty('--wave-scroll-offset', '0px');

  // Add scroll event listener for wave animations
  window.addEventListener('scroll', handleScroll);

  // Add dark mode check
  const savedDarkMode = localStorage.getItem('darkMode');
  if (savedDarkMode === 'true') {
    darkMode.value = true;
    document.documentElement.classList.add('dark-theme');
  } else {
    // Check system preference if no saved preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      darkMode.value = true;
      document.documentElement.classList.add('dark-theme');
    }
  }

  // Get weather by user location
  getUserLocation();

  // Add touch event listeners for mobile
  if (isMobile.value) {
    disableParallax();
    document.documentElement.style.setProperty('--wave-transition', '0s');
  }
});

onUnmounted(() => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value);
  }

  // Remove scroll event listener
  window.removeEventListener('scroll', handleScroll);

  // Remove resize event listener
  window.removeEventListener('resize', checkMobile);
});
</script>
<script>
import UserLayout from '@/Layouts/UserLayout.vue';

export default {
    layout: UserLayout,
};
</script>
