<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useQuasar } from 'quasar';

const $q = useQuasar();

// Set viewport meta tag for mobile responsiveness

const currencies = ref([
  {
    "id": 1,
    "name": "دۆلار",
    "code": "us",
    "currency_code": "USD",
    "order": 1,
    "sale": 142700,
        'sale_status': 'up',
    "buy": 142300,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-05-2025 15:44"
  },
  {
    "id": 2,
    "name": "یۆرۆ",
    "code": "eu",
    "currency_code": "EUR",
    "order": 2,
    "sale": 112.25,
    "sale_status": 'up',
    "buy": 111.25,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-05-2025 13:35"
  },
  {
    "id": 3,
    "name": "پاوەند",
    "code": "gb",
    "currency_code": "GBP",
    "order": 3,
    "sale": 131.25,
    "sale_status": 'up',
    "buy": 130.25,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-05-2025 10:09"
  },
  {
    "id": 4,
    "name": "کرۆنی سویدی",
    "code": "se",
    "currency_code": "SEK",
    "order": 4,
    "sale": 87,
    "sale_status": 'up',
    "buy": 84,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "07-05-2025 21:53"
  },
  {
    "id": 5,
    "name": "کرۆنی نەرویژی",
    "code": "no",
    "currency_code": "NOK",
    "order": 5,
    "sale": 79,
    "sale_status": 'up',
    "buy": 76,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "06-05-2025 10:05"
  },
  {
    "id": 6,
    "name": "کرۆنی دانیمارکی",
    "code": "dk",
    "currency_code": "DKK",
    "order": 6,
    "sale": 130,
    "sale_status": 'up',
    "buy": 124,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "05-05-2025 18:57"
  },
  {
    "id": 7,
    "name": "ئۆسترالی",
    "code": "au",
    "currency_code": "AUD",
    "order": 7,
    "sale": 62.75,
    "sale_status": 'up',
    "buy": 61.75,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "06-05-2025 10:06"
  },
  {
    "id": 8,
    "name": "فرانکی سویسی",
    "code": "ch",
    "currency_code": "CHF",
    "order": 8,
    "sale": 119.5,
    "sale_status": 'up',
    "buy": 118,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-05-2025 10:10"
  },
  {
    "id": 9,
    "name": "کەنەدی",
    "code": "ca",
    "currency_code": "CAD",
    "order": 9,
    "sale": 71,
    "sale_status": 'up',
    "buy": 69.5,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "07-05-2025 10:52"
  },
  {
    "id": 10,
    "name": "لیرەی تورکی",
    "code": "tr",
    "currency_code": "TRY",
    "order": 10,
    "sale": 3830,
    "sale_status": 'up',
    "buy": 3950,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-05-2025 10:09"
  },
  {
    "id": 11,
    "name": "تۆمەن",
    "code": "ir",
    "currency_code": "IRR",
    "order": 11,
    "sale": 7600000,
    "sale_status": 'up',
    "buy": 8800000,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-05-2025 10:10"
  },
  {
    "id": 12,
    "name": "درەهمی ئیماراتی",
    "code": "ae",
    "currency_code": "AED",
    "order": 12,
    "sale": 366,
    "sale_status": 'up',
    "buy": 373,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "08-04-2025 14:07"
  },
  {
    "id": 13,
    "name": "ڕیال سعودی",
    "code": "sa",
    "currency_code": "SAR",
    "order": 13,
    "sale": 371,
    "sale_status": 'up',
    "buy": 383,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "13-04-2025 10:54"
  },
  {
    "id": 14,
    "name": "دیناری ئوردن",
    "code": "jo",
    "currency_code": "JOD",
    "order": 14,
    "sale": 69,
    "sale_status": 'up',
    "buy": 72,
    "buy_status": 'down',
    "created_at": "28-03-2025 15:41",
    "updated_at": "05-05-2025 19:03"
  }
]);
const updateInterval = ref(null);
const lastUpdatedTime = ref(new Date());
const lastUpdated = ref('');
const showCalculator = ref(false);
const selectedCurrency = ref(null);
const amount = ref(1);
const operation = ref(null);
const secondAmount = ref(null);
const showSecondInput = ref(false);
const result = ref(null);
const activeTab = ref('buy');
const calculationHistory = ref([]);
const digitalTime = ref('');

// Language translations
const translations = {
  ku: {
    title: 'نرخی دراوەکان',
    lastUpdate: 'دوایین نوێکردنەوە',
    weather: {
      title: 'کەش و هەوا',
      humidity: 'شێ',
      wind: 'با',
      temperature: 'پلەی گەرمی',
      loading: 'بارکردن...',
      error: 'هەڵە ڕوویدا',
      error_not_supported: 'خزمەتگوزاری شوێن لە وێبگەڕەکەت پشتگیری ناکرێت',
      error_permission_denied: 'دەستپێگەیشتن بە شوێن ڕەتکرایەوە',
      error_unavailable: 'زانیاری شوێن بەردەست نییە',
      error_timeout: 'داواکاری شوێن کاتی بەسەرچوو',
      error_unknown: 'هەڵەیەکی نەناسراو ڕوویدا',
      refresh: 'نوێکردنەوە',
      tap_refresh: 'کلیک بکە بۆ نوێکردنەوە'
    },
    currency: 'دراو',
    code: 'کۆد',
    sale: 'فرۆشتن',
    buy: 'کڕین',
    close: 'داخستن',
    amount: 'بڕی دراو',
    buyAmount: 'بڕی دراو بۆ کڕین',
    sellAmount: 'بڕی دراو بۆ فرۆشتن',
    buyRate: 'نرخی کڕین',
    sellRate: 'نرخی فرۆشتن',
    iqdAmount: 'بڕی دینار',
    usdAmount: 'بڕی دۆلار',
    secondAmount: 'بڕی دووەم',
    divideByZero: 'ناتوانرێت دابەش بکرێت بە سفر',
    timeAgo: {
      justNow: 'ئێستا',
      seconds: '{s} چرکە پێش',
      minute: '1 خولەک پێش',
      minutes: '{s} خولەک پێش',
      hour: '1 کاتژمێر پێش',
      hours: '{s} کاتژمێر پێش',
      day: '1 ڕۆژ پێش',
      days: '{s} ڕۆژ پێش'
    }
  },
  en: {
    title: 'Currency Rates',
    lastUpdate: 'Last Update',
    weather: {
      title: 'Weather',
      humidity: 'Humidity',
      wind: 'Wind',
      temperature: 'Temperature',
      loading: 'Loading...',
      error: 'Error loading data',
      error_not_supported: 'Geolocation is not supported by your browser',
      error_permission_denied: 'Location access was denied',
      error_unavailable: 'Location information is unavailable',
      error_timeout: 'Location request timed out',
      error_unknown: 'An unknown error occurred',
      refresh: 'Refresh',
      tap_refresh: 'Tap to refresh'
    },
    currency: 'Currency',
    code: 'Code',
    sale: 'Sale',
    buy: 'Buy',
    close: 'Close',
    amount: 'Amount',
    buyAmount: 'Amount to Buy',
    sellAmount: 'Amount to Sell',
    buyRate: 'Buy Rate',
    sellRate: 'Sell Rate',
    iqdAmount: 'IQD Amount',
    usdAmount: 'USD Amount',
    secondAmount: 'Second Amount',
    divideByZero: 'Cannot divide by zero',
    timeAgo: {
      justNow: 'just now',
      seconds: '{s} seconds ago',
      minute: '1 minute ago',
      minutes: '{s} minutes ago',
      hour: '1 hour ago',
      hours: '{s} hours ago',
      day: '1 day ago',
      days: '{s} days ago'
    }
  },
  ar: {
    title: 'أسعار العملات',
    lastUpdate: 'آخر تحديث',
    weather: {
      title: 'الطقس',
      humidity: 'الرطوبة',
      wind: 'الرياح',
      temperature: 'درجة الحرارة',
      loading: 'جار التحميل...',
      error: 'خطأ في تحميل البيانات',
      error_not_supported: 'متصفحك لا يدعم خدمة تحديد الموقع',
      error_permission_denied: 'تم رفض الوصول إلى الموقع',
      error_unavailable: 'معلومات الموقع غير متوفرة',
      error_timeout: 'انتهت مهلة طلب الموقع',
      error_unknown: 'حدث خطأ غير معروف',
      refresh: 'تحديث',
      tap_refresh: 'انقر للتحديث'
    },
    currency: 'العملة',
    code: 'الرمز',
    sale: 'بيع',
    buy: 'شراء',
    close: 'إغلاق',
    amount: 'المبلغ',
    buyAmount: 'المبلغ للشراء',
    sellAmount: 'المبلغ للبيع',
    buyRate: 'سعر الشراء',
    sellRate: 'سعر البيع',
    iqdAmount: 'المبلغ بالدينار',
    usdAmount: 'المبلغ بالدولار',
    secondAmount: 'المبلغ الثاني',
    divideByZero: 'لا يمكن القسمة على صفر',
    timeAgo: {
      justNow: 'الآن',
      seconds: 'منذ {s} ثانية',
      minute: 'منذ دقيقة',
      minutes: 'منذ {s} دقائق',
      hour: 'منذ ساعة',
      hours: 'منذ {s} ساعات',
      day: 'منذ يوم',
      days: 'منذ {s} أيام'
    }
  }
};

// Language state
const currentLang = ref('ku');
const languages = [
  { code: 'ku', name: 'کوردی', flag: 'iq' },
  { code: 'en', name: 'English', flag: 'gb' },
  { code: 'ar', name: 'العربية', flag: 'sa' }
];

// Computed translation
const t = computed(() => translations[currentLang.value]);

// Direction computed
const isRTL = computed(() => currentLang.value === 'ku' || currentLang.value === 'ar');

// Update notification message
const showNotification = (message) => {
  $q.notify({
    message: translations[currentLang.value][message],
    color: 'negative',
    icon: 'error',
    position: 'top'
  });
};

// Computed properties
const usdRate = computed(() => {
  const usd = currencies.value.find(c => c.currency_code === 'USD');
  return usd ? usd.buy : 1;
});

// Function to convert to USD
const convertToUSD = (amount, rate) => {
  return amount / rate;
};

// Function to format currency
const formatCurrency = (value, currency = 'IQD') => {
  if (currency === 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  }
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value) + ' د.ع';
};

const setAmount = (value) => {
  amount.value = value;
  calculateExchange();
};

const calculateExchange = () => {
  if (!selectedCurrency.value || !amount.value) return;

  const buyResult = amount.value * selectedCurrency.value.buy;
  const saleResult = amount.value * selectedCurrency.value.sale;

  result.value = {
    buy: {
      iqd: formatCurrency(buyResult),
      usd: formatCurrency(convertToUSD(buyResult, usdRate.value), 'USD')
    },
    sale: {
      iqd: formatCurrency(saleResult),
      usd: formatCurrency(convertToUSD(saleResult, usdRate.value), 'USD')
    },
    timestamp: new Date().toISOString()
  };

  // Add to history
  calculationHistory.value.unshift({
    currency: selectedCurrency.value,
    amount: amount.value,
    result: result.value,
    type: activeTab.value,
    timestamp: new Date().toISOString()
  });

  // Keep only last 5 calculations
  if (calculationHistory.value.length > 5) {
    calculationHistory.value.pop();
  }
};

const openCalculator = (currency) => {
  selectedCurrency.value = currency;
  amount.value = 1;
  result.value = null;
  activeTab.value = 'buy';
  showCalculator.value = true;
};

const performOperation = (op) => {
  if (!amount.value) return;

  operation.value = op;
  showSecondInput.value = true;
  secondAmount.value = null;
};

const calculateFinal = () => {
  if (!amount.value || !secondAmount.value || !operation.value) return;

  let finalAmount;
  let operationSymbol;

  switch (operation.value) {
    case '+':
      finalAmount = amount.value + secondAmount.value;
      operationSymbol = '+';
      break;
    case '-':
      finalAmount = amount.value - secondAmount.value;
      operationSymbol = '-';
      break;
    case '*':
      finalAmount = amount.value * secondAmount.value;
      operationSymbol = '×';
      break;
    case '/':
      if (secondAmount.value === 0) {
        showNotification('divideByZero');
        return;
      }
      finalAmount = amount.value / secondAmount.value;
      operationSymbol = '÷';
      break;
    default:
      return;
  }

  amount.value = finalAmount;
  calculateExchange();
  showSecondInput.value = false;
  operation.value = null;
  secondAmount.value = null;
};

const clearOperation = () => {
  operation.value = null;
  showSecondInput.value = false;
  secondAmount.value = null;
};

const clearAll = () => {
  amount.value = 1;
  clearOperation();
  calculateExchange();
};

// Update the clock
const updateClock = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  digitalTime.value = `${hours}:${minutes}:${seconds}`;

  // Update analog clock hands
  const hoursDeg = (now.getHours() % 12) * 30 + now.getMinutes() * 0.5;
  const minutesDeg = now.getMinutes() * 6;
  const secondsDeg = now.getSeconds() * 6;

  document.documentElement.style.setProperty('--hours-rotation', `${hoursDeg}deg`);
  document.documentElement.style.setProperty('--minutes-rotation', `${minutesDeg}deg`);
  document.documentElement.style.setProperty('--seconds-rotation', `${secondsDeg}deg`);
};

// Add mouseX and mouseY refs for parallax
const mouseX = ref(0);
const mouseY = ref(0);
const parallaxActive = ref(false);

// Add parallax effect
const handleParallax = (event) => {
  if (!parallaxActive.value) return;

  const header = event.currentTarget;
  const rect = header.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  // Calculate the position relative to the center of the header (values between -1 and 1)
  mouseX.value = (event.clientX - centerX) / (rect.width / 2);
  mouseY.value = (event.clientY - centerY) / (rect.height / 2);

  // Calculate distance from center for lighting effect (0 to 1)
  const distance = Math.sqrt(mouseX.value * mouseX.value + mouseY.value * mouseY.value);
  const brightness = 1 + (0.1 * (1 - distance));
  const saturation = 1 + (0.2 * (1 - distance));

  // Update CSS properties for advanced wave effects
  document.documentElement.style.setProperty('--mouse-x', mouseX.value);
  document.documentElement.style.setProperty('--mouse-y', mouseY.value);
  document.documentElement.style.setProperty('--wave-brightness', brightness.toString());
  document.documentElement.style.setProperty('--wave-saturation', saturation.toString());
};

// Enable parallax with a smooth transition
const enableParallax = () => {
  parallaxActive.value = true;
  document.documentElement.style.setProperty('--wave-transition', '0.5s');
};

// Disable parallax with a smooth reset
const disableParallax = () => {
  document.documentElement.style.setProperty('--wave-transition', '1s');
  setTimeout(() => {
    parallaxActive.value = false;
    mouseX.value = 0;
    mouseY.value = 0;
    document.documentElement.style.setProperty('--mouse-x', 0);
    document.documentElement.style.setProperty('--mouse-y', 0);
  }, 50);
};

// Add scroll position tracking for waves
const scrollY = ref(0);

// Update scroll position for wave effects
const handleScroll = () => {
  scrollY.value = window.scrollY;

  // Update wave effect based on scroll position
  const waveOffset = Math.min(scrollY.value / 10, 20);
  document.documentElement.style.setProperty('--wave-scroll-offset', `${waveOffset}px`);
};

// Replace the entire weatherData and fetchWeather functions with this improved version
const weatherData = ref({
  temperature: null,
  condition: 'cloudy',
  location: '',
  loading: true,
  error: null,
  coords: {
    latitude: null,
    longitude: null
  }
});

const weatherIcons = {
  'sunny': 'wb_sunny',
  'partly-cloudy': 'partly_cloudy_day',
  'cloudy': 'cloud',
  'rainy': 'water_drop',
  'stormy': 'thunderstorm',
  'snowy': 'ac_unit',
  'foggy': 'filter_drama'
};

const weatherColors = {
  'sunny': 'orange',
  'partly-cloudy': 'light-blue',
  'cloudy': 'blue-grey',
  'rainy': 'blue',
  'stormy': 'deep-purple',
  'snowy': 'cyan',
  'foggy': 'grey'
};

// Function to get user's current location
const getUserLocation = () => {
  weatherData.value.loading = true;
  weatherData.value.error = null;

  // Check if geolocation is supported by the browser
  if (!navigator.geolocation) {
    weatherData.value.error = t.value.weather.error_not_supported;
    weatherData.value.loading = false;
    return;
  }

  // Get current position
  navigator.geolocation.getCurrentPosition(
    // Success callback
    (position) => {
      weatherData.value.coords = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude
      };
      // After getting coordinates, fetch the weather
      getLocationName(position.coords.latitude, position.coords.longitude);
      fetchWeatherData(position.coords.latitude, position.coords.longitude);
    },
    // Error callback
    (error) => {
      console.error('Error getting location:', error);

      // Different error messages based on error code
      switch(error.code) {
        case error.PERMISSION_DENIED:
          weatherData.value.error = t.value.weather.error_permission_denied;
          break;
        case error.POSITION_UNAVAILABLE:
          weatherData.value.error = t.value.weather.error_unavailable;
          break;
        case error.TIMEOUT:
          weatherData.value.error = t.value.weather.error_timeout;
          break;
        default:
          weatherData.value.error = t.value.weather.error_unknown;
      }

      weatherData.value.loading = false;

      // Don't automatically fallback - let user manually retry
    },
    // Options
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    }
  );
};

// Function to get location name from coordinates using reverse geocoding
const getLocationName = (latitude, longitude) => {
  // In a real app, you would use a geocoding service API here
  // For this demo, we'll simulate a few locations based on coordinates

  // Some hardcoded locations for demo purposes
  const locations = [
    { lat: 35.5569, lng: 45.4329, name: 'Slemani' },
    { lat: 36.1911, lng: 44.0091, name: 'Erbil' },
    { lat: 37.0782, lng: 43.3197, name: 'Duhok' },
    { lat: 35.4679, lng: 44.3927, name: 'Kirkuk' }
  ];

  // Find closest match or use a default
  let closestLocation = null;
  let minDistance = Infinity;

  locations.forEach(loc => {
    const distance = Math.sqrt(
      Math.pow(loc.lat - latitude, 2) +
      Math.pow(loc.lng - longitude, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      closestLocation = loc;
    }
  });

  if (closestLocation && minDistance < 1) { // Within ~100km
    weatherData.value.location = closestLocation.name;
  } else {
    // If no close match, just display the coordinates in a friendly way
    const lat = Math.abs(latitude).toFixed(2) + (latitude >= 0 ? '°N' : '°S');
    const lng = Math.abs(longitude).toFixed(2) + (longitude >= 0 ? '°E' : '°W');
    weatherData.value.location = `${lat}, ${lng}`;
  }
};

// Function to fetch weather data based on coordinates
const fetchWeatherData = (latitude, longitude) => {
  // In a real app, you would call a weather API here with the coordinates
  // For this demo, we'll just simulate it with random data

  setTimeout(() => {
    const conditions = ['sunny', 'partly-cloudy', 'cloudy', 'rainy', 'stormy', 'snowy', 'foggy'];

    // Use deterministic "random" based on coordinates to get consistent weather for same location
    const locationSeed = (latitude * 10 + longitude) % 100;
    const dayOfYear = Math.floor((new Date() - new Date(new Date().getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
    const seed = locationSeed + dayOfYear;

    // Get deterministic condition and temperature based on seed
    const conditionIndex = Math.floor(seed % conditions.length);
    const condition = conditions[conditionIndex];

    // Temperature based on condition and location
    let baseTemp;
    switch(condition) {
      case 'sunny': baseTemp = 30; break;
      case 'partly-cloudy': baseTemp = 25; break;
      case 'cloudy': baseTemp = 22; break;
      case 'rainy': baseTemp = 18; break;
      case 'stormy': baseTemp = 16; break;
      case 'snowy': baseTemp = 0; break;
      case 'foggy': baseTemp = 15; break;
      default: baseTemp = 20;
    }

    // Add some variation based on the seed
    const tempVariation = (seed % 10) - 5;
    const finalTemp = Math.round(baseTemp + tempVariation);

    weatherData.value = {
      ...weatherData.value,
      temperature: finalTemp,
      condition: condition,
      loading: false
    };
  }, 800);
};

// Function to refresh weather data
const refreshWeather = () => {
  if (weatherData.value.coords.latitude && weatherData.value.coords.longitude) {
    weatherData.value.loading = true;
    fetchWeatherData(weatherData.value.coords.latitude, weatherData.value.coords.longitude);
  } else {
    getUserLocation();
  }
};

// Add isMobile detection
const isMobile = ref(false);

// Function to check if device is mobile
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768 ||
                   ('ontouchstart' in window) ||
                   (navigator.maxTouchPoints > 0);
};

// Add touch event handlers
const handleTouchStart = (event) => {
  // Disable parallax effects on touch devices
  disableParallax();
};

// Add sea wave animation refs
const waveHeight = ref(0);
const wavePhase = ref(0);

// Add wave animation
const animateWaves = () => {
  wavePhase.value = (wavePhase.value + 0.02) % (2 * Math.PI);
  waveHeight.value = Math.sin(wavePhase.value) * 5;
  requestAnimationFrame(animateWaves);
};

// Add floating elements animation refs
const floatingElements = ref([]);

// Optimize floating elements generation for mobile
const generateFloatingElements = () => {
  const elements = [];
  const types = ['bubble', 'seaweed', 'fish', 'starfish'];

  // Generate fewer elements on mobile
  const count = isMobile.value ? 5 : 12;

  for (let i = 0; i < count; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    elements.push({
      id: i,
      type,
      size: Math.random() * 20 + 10,
      x: Math.random() * 100,
      y: Math.random() * 100,
      speed: Math.random() * 10 + 5,
      delay: Math.random() * 5,
      direction: Math.random() > 0.5 ? 'left' : 'right',
      rotate: Math.random() * 360
    });
  }

  floatingElements.value = elements;
};

// Add dark mode ref
const darkMode = ref(false);

// Function to toggle dark mode
const toggleDarkMode = () => {
  darkMode.value = !darkMode.value;
  document.documentElement.classList.toggle('dark-theme', darkMode.value);

  // Store preference in localStorage
  localStorage.setItem('darkMode', darkMode.value ? 'true' : 'false');
};

onMounted(() => {
  // Check if device is mobile
  checkMobile();
  window.addEventListener('resize', checkMobile);

  lastUpdatedTime.value = new Date();

  // Define the formatTimeAgo function inside onMounted
  const formatTimeAgo = (date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 5) {
      return t.value.timeAgo.justNow;
    }

    if (diffInSeconds < 60) {
      return t.value.timeAgo.seconds.replace('{s}', diffInSeconds);
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes === 1) {
      return t.value.timeAgo.minute;
    }
    if (diffInMinutes < 60) {
      return t.value.timeAgo.minutes.replace('{s}', diffInMinutes);
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours === 1) {
      return t.value.timeAgo.hour;
    }
    if (diffInHours < 24) {
      return t.value.timeAgo.hours.replace('{s}', diffInHours);
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) {
      return t.value.timeAgo.day;
    }
    return t.value.timeAgo.days.replace('{s}', diffInDays);
  };

  // Function to update the time ago display
  const updateTimeAgo = () => {
    lastUpdated.value = formatTimeAgo(lastUpdatedTime.value);
  };

  // Initial update
  updateTimeAgo();

  updateClock();
  setInterval(updateClock, 1000);

  // Update time every 10 seconds
  updateInterval.value = window.setInterval(() => {
    lastUpdatedTime.value = new Date();
    updateTimeAgo();
  }, 10000);

  animateWaves();
  generateFloatingElements();

  // Set default CSS variables for waves
  document.documentElement.style.setProperty('--mouse-x', '0');
  document.documentElement.style.setProperty('--mouse-y', '0');
  document.documentElement.style.setProperty('--wave-transition', '0.5s');
  document.documentElement.style.setProperty('--wave-brightness', '1');
  document.documentElement.style.setProperty('--wave-saturation', '1');
  document.documentElement.style.setProperty('--wave-scroll-offset', '0px');

  // Add scroll event listener for wave animations
  window.addEventListener('scroll', handleScroll);

  // Add dark mode check
  const savedDarkMode = localStorage.getItem('darkMode');
  if (savedDarkMode === 'true') {
    darkMode.value = true;
    document.documentElement.classList.add('dark-theme');
  } else {
    // Check system preference if no saved preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      darkMode.value = true;
      document.documentElement.classList.add('dark-theme');
    }
  }

  // Get weather by user location
  getUserLocation();

  // Add touch event listeners for mobile
  if (isMobile.value) {
    disableParallax();
    document.documentElement.style.setProperty('--wave-transition', '0s');
  }
});

onUnmounted(() => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value);
  }

  // Remove scroll event listener
  window.removeEventListener('scroll', handleScroll);

  // Remove resize event listener
  window.removeEventListener('resize', checkMobile);
});


</script>
<script>
import UserLayout from '@/Layouts/UserLayout.vue';

export default {
    layout: UserLayout,

};
</script>
<template>
  <q-page class="currency-page" :dir="isRTL ? 'rtl' : 'ltr'">
    <div class="page-container">
      <div class="modern-header"
           @mousemove="handleParallax"
           @mouseenter="enableParallax"
           @mouseleave="disableParallax"
           @touchstart="handleTouchStart"> <!-- Disable parallax on touch devices -->
        <div class="header-waves">
          <!-- Add floating elements -->
          <div class="floating-elements">
            <div
              v-for="element in floatingElements"
              :key="element.id"
              :class="['floating-element', element.type]"
              :style="{
                left: `${element.x}%`,
                top: `${element.y}%`,
                width: `${element.size}px`,
                height: `${element.size}px`,
                animationDuration: `${element.speed}s`,
                animationDelay: `${element.delay}s`,
                transform: `rotate(${element.rotate}deg) translateX(calc(var(--mouse-x, 0) * ${-element.size/2}px)) translateY(calc(var(--mouse-y, 0) * ${-element.size/2}px))`,
                animationName: element.direction === 'left' ? 'floatLeft' : 'floatRight'
              }"
            ></div>
          </div>

          <!-- Add bubbles -->
          <div class="bubbles">
            <div class="bubble b1"></div>
            <div class="bubble b2"></div>
            <div class="bubble b3"></div>
            <div class="bubble b4"></div>
            <div class="bubble b5"></div>
          </div>
        </div>
        <div class="header-gradient" :style="{ background: `radial-gradient(circle at ${50 + mouseX * 30}% ${50 + mouseY * 30}%, rgba(37,99,235,0.19), transparent 60%), radial-gradient(circle at ${10 - mouseX * 10}% ${90 - mouseY * 10}%, rgba(37,99,235,0.13), transparent 50%)` }"></div>
        <div class="header-content" :class="{ 'rtl-layout': isRTL }">
          <div class="header-left" :class="{ 'rtl-layout': isRTL }">
            <div class="logo-container" :class="{ 'rtl-layout': isRTL }">
              <img src="logo.png" alt="Logo" class="logo-image" width="120" />
              <div class="logo-text" :class="{ 'rtl-text': isRTL }">کۆمپانیای دەریای باوەر</div>
            </div>
            <div class="header-actions mobile-actions" :class="{ 'rtl-layout': isRTL }">
              <div class="last-update-info">
                <q-chip icon="update" color="primary" text-color="white" class="update-chip animate-fade" :class="{ 'rtl-text': isRTL }">
                  {{ lastUpdated }}
                </q-chip>
              </div>

              <div class="dark-mode-toggle" :class="{ 'rtl-layout': isRTL }">
                <q-toggle
                  v-model="darkMode"
                  icon="dark_mode"
                  color="primary"
                  size="md"
                  @update:model-value="toggleDarkMode"
                >
                  <div class="toggle-label" :class="{ 'rtl-text': isRTL }">{{ darkMode ? 'Dark' : 'Light' }}</div>
                </q-toggle>
              </div>

              <div class="language-selector">
                <q-btn-dropdown
                  flat
                  :icon="`flag_${languages.find(lang => lang.code === currentLang)?.flag}`"
                  class="lang-dropdown"
                  :class="{ 'rtl-layout': isRTL }"
                >
                  <q-list class="language-list">
                    <q-item
                      v-for="lang in languages"
                      :key="lang.code"
                      clickable
                      v-close-popup
                      @click="currentLang = lang.code"
                      :active="currentLang === lang.code"
                      :class="{ 'rtl-layout': lang.code === 'ku' || lang.code === 'ar' }"
                    >
                      <q-item-section avatar>
                        <q-img
                          :src="`https://flagcdn.com/w20/${lang.flag}.png`"
                          :ratio="16/9"
                          class="lang-flag"
                        />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label :class="{ 'rtl-text': lang.code === 'ku' || lang.code === 'ar' }">{{ lang.name }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </div>
            </div>
          </div>

          <div class="header-right" :class="{ 'rtl-layout': isRTL }">
            <div class="title-section">
              <div class="text-h4" :class="{ 'rtl-text': isRTL }">{{ t.title }}</div>
              <div class="clock-container">
                <div class="clock">
                  <div class="logo-background">
                    <img src="logo.png" alt="Logo" class="logo-bg-image" />
                  </div>
                  <div class="clock-face">
                    <div class="clock-hand hours"></div>
                    <div class="clock-hand minutes"></div>
                    <div class="clock-hand seconds"></div>
                    <div class="clock-center"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <q-card class="currency-card" flat bordered>
        <q-card-section>
          <!-- Header Row -->
          <div class="currency-row header">
            <div class="currency-col name-col">{{ t.currency }}</div>
            <div class="currency-col code-col">{{ t.code }}</div>
            <div class="currency-col rate-col">{{ t.sale }}</div>
            <div class="currency-col rate-col">{{ t.buy }}</div>
          </div>

          <!-- Currency Rows -->
          <div class="currency-list">
            <q-slide-transition v-for="(currency, index) in currencies" :key="currency.id">
              <div class="currency-row" :style="{ animationDelay: `${index * 0.05}s` }" @click="openCalculator(currency)">
                <div class="currency-col name-col">
                  <div class="currency-name">
                    <div class="flag-container">
                      <q-img :src="`https://flagcdn.com/w80/${currency.code}.png`" :ratio="16 / 9" class="flag-image"
                        spinner-color="primary" spinner-size="24px">
                        <template v-slot:loading>
                          <q-spinner-dots color="primary" />
                        </template>
                      </q-img>
                      <div class="flag-shadow"></div>
                    </div>
                    <span>{{ currency.name }}</span>
                  </div>
                </div>
                <div class="currency-col code-col">
                  <q-chip dense class="currency-code" color="grey-2" text-color="grey-8">
                    {{ currency.currency_code }}
                  </q-chip>
                </div>
                <div class="currency-col rate-col">
                  <div class="rate-container">
                    <q-chip class="rate-value sale" dense :color="currency.sale > currency.buy ? 'green-1' : 'grey-2'"
                      text-color="green-8">
                      <q-icon name="arrow_upward" size="xs" />
                      {{ formatCurrency(currency.sale) }}
                    </q-chip>

                  </div>
                </div>
                <div class="currency-col rate-col">
                  <div class="rate-container">
                    <q-chip class="rate-value buy" dense :color="currency.buy < currency.sale ? 'red-1' : 'grey-2'"
                      text-color="red-8">
                      <q-icon name="arrow_downward" size="xs" />
                      {{ formatCurrency(currency.buy) }}
                    </q-chip>

                  </div>
                </div>
              </div>
            </q-slide-transition>
          </div>
        </q-card-section>
      </q-card>

        <!-- Currency Calculator Dialog -->
        <q-dialog v-model="showCalculator" persistent>
          <q-card class="calculator-card">
            <q-card-section class="calculator-header">
              <div class="currency-info">
                <div class="flag-container">
                  <q-img :src="`https://flagcdn.com/w80/${selectedCurrency?.code}.png`" :ratio="16 / 9" class="flag-image"
                    spinner-color="primary" spinner-size="24px">
                    <template v-slot:loading>
                      <q-spinner-dots color="primary" />
                    </template>
                  </q-img>
                </div>
                <div class="currency-details">
                  <div class="text-h6">{{ selectedCurrency?.name }}</div>
                  <q-chip dense class="currency-code" color="grey-2" text-color="grey-8">
                    {{ selectedCurrency?.currency_code }}
                  </q-chip>
                </div>
              </div>
              <q-btn icon="close" flat round dense v-close-popup class="close-btn" />
            </q-card-section>

            <q-card-section class="calculator-body">
              <q-tabs
                v-model="activeTab"
                class="calculator-tabs"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
              >
                <q-tab name="buy" :label="t.buy" icon="arrow_downward" />
                <q-tab name="sell" :label="t.sale" icon="arrow_upward" />
              </q-tabs>

              <q-tab-panels v-model="activeTab" animated>
                <q-tab-panel name="buy">
                  <div class="rate-info">
                    <div class="rate-label">{{ t.buyRate }}</div>
                    <div class="rate-value">{{ formatCurrency(selectedCurrency?.buy) }}</div>
                    <div class="usd-rate">
                      {{ formatCurrency(convertToUSD(selectedCurrency?.buy, usdRate), 'USD') }}
                    </div>
                  </div>
                </q-tab-panel>

                <q-tab-panel name="sell">
                  <div class="rate-info">
                    <div class="rate-label">{{ t.sellRate }}</div>
                    <div class="rate-value">{{ formatCurrency(selectedCurrency?.sale) }}</div>
                    <div class="usd-rate">
                      {{ formatCurrency(convertToUSD(selectedCurrency?.sale, usdRate), 'USD') }}
                    </div>
                  </div>
                </q-tab-panel>
              </q-tab-panels>

              <div class="calculator-inputs">
                <q-input
                  v-model.number="amount"
                  type="number"
                  :label="activeTab === 'buy' ? t.buyAmount : t.sellAmount"
                  outlined
                  class="amount-input"
                  @update:model-value="calculateExchange"
                >
                  <template v-slot:prepend>
                    <q-icon :name="activeTab === 'buy' ? 'arrow_downward' : 'arrow_upward'" color="primary" />
                  </template>
                  <template v-slot:append>
                    <q-btn flat dense icon="refresh" @click="clearAll" class="refresh-btn" />
                  </template>
                </q-input>

                <div v-if="showSecondInput" class="operation-inputs">
                  <div class="operation-display">
                    <span class="first-number">{{ amount }}</span>
                    <span class="operation">{{ operation }}</span>
                    <span class="second-number">{{ secondAmount || '?' }}</span>
                  </div>

                  <q-input
                    v-model.number="secondAmount"
                    type="number"
                    :label="t.secondAmount"
                    outlined
                    class="second-input"
                    @keyup.enter="calculateFinal"
                  >
                    <template v-slot:append>
                      <q-btn flat dense icon="check" @click="calculateFinal" color="positive" />
                      <q-btn flat dense icon="close" @click="clearOperation" color="negative" />
                    </template>
                  </q-input>
                </div>
              </div>

              <div class="math-operations">
                <q-btn-group flat class="operation-buttons">
                  <q-btn
                    v-for="op in ['+', '-', '*', '/']"
                    :key="op"
                    :label="op === '*' ? '×' : op === '/' ? '÷' : op"
                    class="operation-btn"
                    @click="performOperation(op)"
                  />
                </q-btn-group>
              </div>

              <div class="results-container" v-if="result">
                <div class="result-item">
                  <div class="result-section">
                    <div class="result-label">{{ t.iqdAmount }}</div>
                  <div class="result-value" :class="activeTab">
                    {{ activeTab === 'buy' ? result.buy.iqd : result.sale.iqd }}
                  </div>
                  </div>
                  <div class="result-section">
                    <div class="result-label">{{ t.usdAmount }}</div>
                  <div class="result-value" :class="activeTab">
                    {{ activeTab === 'buy' ? result.buy.usd : result.sale.usd }}
                  </div>
                </div>
              </div>
                      </div>
            </q-card-section>

            <q-card-actions align="right" class="calculator-footer">
              <q-btn flat :label="t.close" color="primary" v-close-popup />
            </q-card-actions>
          </q-card>
        </q-dialog>
    </div>
    <div class="bottom-ocean">
      <!-- Bottom floating elements -->
      <div class="bottom-floating-elements">
        <div class="bottom-bubble b1"></div>
        <div class="bottom-bubble b2"></div>
        <div class="bottom-bubble b3"></div>
        <div class="bottom-fish f1"></div>
        <div class="bottom-seaweed s1"></div>
        <div class="bottom-seaweed s2"></div>
      </div>
    </div>
  </q-page>
</template>

<style scoped lang="scss">
.currency-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #e0edfa 0%, #f0f6fd 100%);
  padding: 2.5rem 1.5rem 2.5rem 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 0 1px rgba(59,130,246,0.03);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 320px;
    background: linear-gradient(120deg, rgba(37,99,235,0.16) 0%, rgba(37,99,235,0.09) 100%);
    z-index: 0;
    filter: blur(2px);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 220px;
    background: linear-gradient(to top, rgba(37,99,235,0.09), transparent);
    z-index: 0;
    filter: blur(1px);
  }
}


.page-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;

  &::after {
    content: '';
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%232563eb' fill-opacity='0.08' d='M0,128L48,122.7C96,117,192,107,288,133.3C384,160,480,224,576,224C672,224,768,160,864,149.3C960,139,1056,181,1152,208C1248,235,1344,245,1392,250.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E"),
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%232563eb' fill-opacity='0.12' d='M0,288L48,277.3C96,267,192,245,288,213.3C384,181,480,139,576,144C672,149,768,203,864,218.7C960,235,1056,213,1152,181.3C1248,149,1344,107,1392,85.3L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E"),
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%232563eb' fill-opacity='0.16' d='M0,224L48,229.3C96,235,192,245,288,229.3C384,213,480,171,576,165.3C672,160,768,192,864,197.3C960,203,1056,181,1152,192C1248,203,1344,245,1392,266.7L1440,288L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    background-size: 100% 40px, 100% 40px, 100% 40px;
    background-position: 0 calc(0px - var(--wave-scroll-offset, 0px)),
                         0 calc(20px - var(--wave-scroll-offset, 0px)),
                         0 calc(40px - var(--wave-scroll-offset, 0px));
    background-repeat: repeat-x;
    z-index: -1;
    pointer-events: none;
    animation: bottomWave 20s linear infinite;
    opacity: 0.7;
    transform: translateY(calc(var(--wave-scroll-offset, 0px) * 0.3));
    transition: transform 0.2s ease-out;
  }
}

// Add bottom ocean effect
.bottom-ocean {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  background: linear-gradient(to top, rgba(37,99,235,0.08), transparent);
  z-index: -2;
  pointer-events: none;
}

@keyframes bottomWave {
  0% {
    background-position: 0 calc(0px - var(--wave-scroll-offset, 0px)),
                         0 calc(20px - var(--wave-scroll-offset, 0px)),
                         0 calc(40px - var(--wave-scroll-offset, 0px));
  }
  100% {
    background-position: 1440px calc(0px - var(--wave-scroll-offset, 0px)),
                         -1440px calc(20px - var(--wave-scroll-offset, 0px)),
                         1440px calc(40px - var(--wave-scroll-offset, 0px));
  }
}

@media (prefers-color-scheme: dark) {
  .page-container {
    &::after {
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.12' d='M0,128L48,122.7C96,117,192,107,288,133.3C384,160,480,224,576,224C672,224,768,160,864,149.3C960,139,1056,181,1152,208C1248,235,1344,245,1392,250.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E"),
                  url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.16' d='M0,288L48,277.3C96,267,192,245,288,213.3C384,181,480,139,576,144C672,149,768,203,864,218.7C960,235,1056,213,1152,181.3C1248,149,1344,107,1392,85.3L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E"),
                  url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.2' d='M0,224L48,229.3C96,235,192,245,288,229.3C384,213,480,171,576,165.3C672,160,768,192,864,197.3C960,203,1056,181,1152,192C1248,203,1344,245,1392,266.7L1440,288L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    }
  }

  .bottom-ocean {
    background: linear-gradient(to top, rgba(99,102,241,0.12), transparent);
    height: 150px;
  }
}

.modern-header {
  background: linear-gradient(135deg, rgba(222,239,255,0.92) 0%, rgba(230,244,255,0.86) 100%);
  backdrop-filter: blur(16px);
  border-radius: 36px;
  box-shadow:
    0 16px 60px rgba(37, 99, 235, 0.16),
    0 2px 10px rgba(37, 99, 235, 0.08),
    inset 0 1px 3px rgba(255, 255, 255, 0.6);
  margin-bottom: 3rem;
  border: 1px solid rgba(37, 99, 235, 0.25);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.2, 0, 0.4, 1);
  perspective: 1000px;
  transform-style: preserve-3d;
  animation: headerAppear 0.8s ease-out forwards;

  // Add subtle noise pattern for texture
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent,
      #2563eb,
      #1d4ed8,
      #2563eb,
      transparent
    );
    animation: borderFlow 3.5s linear infinite;
    z-index: 5;
    opacity: 0.7;
  }

  // Add glow effect
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 150%;
    height: 150%;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.15) 0%, transparent 70%);
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.6s ease, transform 0.6s ease;
    z-index: 0;
  }

  &:hover {
    transform: translateY(-8px) scale(1.015);
    box-shadow:
      0 24px 90px rgba(37, 99, 235, 0.16),
      0 8px 24px rgba(37, 99, 235, 0.12);

    .header-content::before {
      opacity: 0.55;
    }

    &::after {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }
}

@keyframes headerAppear {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes borderFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-150%) skewX(-15deg);
  }
  100% {
    transform: translateX(150%) skewX(-15deg);
  }
}

@keyframes rotateLogo {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes titleFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.header-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  align-items: center;
  padding: 2.5rem;
  position: relative;
  gap: 2.5rem;
  z-index: 1;

  // Apply a subtle glass effect to the inner content
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    bottom: 12px;
    border-radius: 28px;
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    opacity: 0.35;
    z-index: -1;
    transition: opacity 0.4s ease;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1.2rem;
  }
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  direction: rtl;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.06);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(37, 99, 235, 0.15);
  transform: none;

  &:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.12);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.logo-circle {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  background: linear-gradient(145deg, #2563eb 0%, #3b82f6 100%);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 6px 16px rgba(37, 99, 235, 0.35),
    0 2px 4px rgba(37, 99, 235, 0.2),
    inset 0 2px 6px rgba(255, 255, 255, 0.15);
  transition: all 0.4s ease;

  &:hover {
    transform: scale(1.08) rotate(5deg);
    box-shadow:
      0 10px 28px rgba(37, 99, 235, 0.45),
      0 4px 8px rgba(37, 99, 235, 0.25),
      inset 0 2px 6px rgba(255, 255, 255, 0.25);
  }

  .logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scale(0.85);
    transition: transform 0.4s ease;
  }

  &:hover .logo-image {
    transform: scale(0.92);
  }

  .logo-glow {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.85) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0.35;
    animation: rotateLogo 10s linear infinite;
  }
}

.logo-text {
  font-size: 2.15rem;
  font-weight: 700;
  background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
  transition: all 0.4s ease;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -4px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.6), transparent);
    opacity: 0;
    transform: scaleX(0.7);
    transition: all 0.4s ease;
  }

  &:hover {
    transform: translateY(-2px);
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);

    &::after {
      opacity: 1;
      transform: scaleX(1);
    }
  }
}

.title-section {
  text-align: center;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 24px;
  padding: 15px;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.08);
  border: 1px solid rgba(37, 99, 235, 0.15);
  transform: none;
  transition: all 0.4s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.12);
  }
}

.header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  background: var(--panel-bg);
  border-radius: 24px;
  padding: 12px 16px;
  backdrop-filter: blur(8px);
  box-shadow: 0 8px 24px var(--shadow-color);
  border: 1px solid var(--border-color);
  transform: translateZ(5px);
  transition: all 0.4s ease;
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;

  &.rtl-layout {
    flex-direction: row-reverse;
  }

  &::before {
    content: '';
    position: absolute;
    inset: 1px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 23px;
    transform: translateZ(2px);
    filter: blur(0);
    opacity: 0;
    transition: all 0.4s ease;
  }

  &::after {
    content: '';
    position: absolute;
    inset: -0.5px;
    border-radius: 24.5px;
    background: linear-gradient(
      45deg,
      var(--primary-color-light),
      transparent,
      var(--primary-color-light),
      transparent
    );
    filter: blur(1px);
    z-index: -1;
    opacity: 0.4;
    transform: translateZ(-2px);
    transition: all 0.4s ease;
  }

  &:hover {
    transform: translateZ(10px) scale(1.02);
    box-shadow: 0 15px 35px var(--shadow-color);
    border-color: var(--primary-color);

    &::before {
      opacity: 0.4;
    }

    &::after {
      opacity: 0.7;
      filter: blur(1.5px);
    }
  }

  @media (max-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
    padding: 8px;
    gap: 8px;
    border-radius: 16px;

    &.rtl-layout {
      flex-direction: row-reverse;
      flex-wrap: wrap;
    }

    &::before {
      border-radius: 15px;
    }

    &::after {
      border-radius: 16.5px;
    }
  }
}

.last-update-info {
  flex: 1;
}

.update-chip {
  font-size: 0.95rem;
  padding: 12px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--primary-color-light) 0%, var(--primary-color-hover) 100%) !important;
  color: var(--text-primary) !important;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  box-shadow:
    0 6px 16px var(--shadow-color),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.2, 0, 0.4, 1);
  position: relative;
  overflow: hidden;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  transform: translateZ(8px);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 150%;
    height: 100%;
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 0.2),
      rgba(255, 255, 255, 0));
    transform: skewX(-15deg);
    animation: shimmer 3s infinite;
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  &:hover {
    background: linear-gradient(135deg, var(--primary-color-hover) 0%, var(--primary-color-light) 100%) !important;
    transform: translateY(-3px) translateZ(12px);
    box-shadow:
      0 10px 24px var(--shadow-color),
      inset 0 1px 2px rgba(255, 255, 255, 0.3);

    &::before {
      opacity: 1;
    }
  }
}

.dark-mode-toggle {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 10px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  white-space: nowrap;
  transform: translateZ(6px);

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px) translateZ(12px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.06);
  }

  .toggle-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-left: 6px;
    transition: color 0.3s ease;
  }
}

.language-selector {
  transform: translateZ(6px);

  .lang-dropdown {
    border-radius: 16px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;

    &:hover {
      transform: translateY(-2px);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.06);
    }

    .q-icon {
      font-size: 1.2rem;
    }
  }
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    padding: 12px;
    gap: 10px;
  }

  .last-update-info, .dark-mode-toggle, .language-selector {
    width: 100%;
  }

  .update-chip {
    font-size: 0.9rem;
    padding: 10px;
  }

  .dark-mode-toggle {
    padding: 8px;
  }

  .language-selector .lang-dropdown {
    width: 100%;
    padding: 8px;
  }
}

// Dark mode enhancements
@media (prefers-color-scheme: dark) {
  .modern-header {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(30, 41, 59, 0.85) 100%);
    border-color: rgba(99, 102, 241, 0.25);
  }

  .logo-container {
    background: rgba(59, 130, 246, 0.08);
    border-color: rgba(99, 102, 241, 0.2);
  }

  .title-section {
    background: rgba(59, 130, 246, 0.08);
    border-color: rgba(99, 102, 241, 0.2);
  }

  .header-actions {
    background: rgba(59, 130, 246, 0.08);
    border-color: rgba(99, 102, 241, 0.2);
  }
}

// Media queries
@media (max-width: 768px) {
  .modern-header {
    margin-bottom: 1rem;
    border-radius: 16px;
    padding: 0.5rem;
  }

  .header-content {
    grid-template-columns: 1fr;
    gap: 0.8rem;
    padding: 1rem;
  }

  .header-left, .header-right {
    width: 100%;
  }

  .logo-container {
    justify-content: center;
    padding: 8px;
    border-radius: 16px;
  }

  .logo-circle {
    width: 42px;
    height: 42px;
  }

  .logo-text {
    font-size: 1.1rem;
  }

  .header-actions {
    flex-direction: row;
    flex-wrap: wrap;
    padding: 6px;
    gap: 6px;
    border-radius: 12px;
  }

  .last-update-info {
    width: 100%;
  }

  .update-chip {
    font-size: 0.75rem;
    padding: 6px;
    height: auto;
    width: 100%;
  }

  .dark-mode-toggle, .language-selector {
    width: 48%;
  }

  .dark-mode-toggle {
    padding: 4px;
    border-radius: 10px;
  }

  .toggle-label {
    font-size: 0.7rem;
  }

  .language-selector .lang-dropdown {
    padding: 4px 8px;
    width: 100%;
    border-radius: 10px;
  }

  .title-section {
    padding: 8px;
    border-radius: 14px;
  }

  .text-h4 {
    font-size: 1.5rem;
    margin-bottom: 0.6rem;
  }

  .clock-container {
    transform: scale(0.7);
    margin-top: -15px;
    margin-bottom: -15px;
  }

  .clock {
    width: 100px;
    height: 100px;
    border-width: 4px;
  }

  .currency-row {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    padding: 0.5rem 0.15rem;
    font-size: 0.8rem;
    gap: 0.15rem;
  }

  .currency-col {
    padding: 0 0.1rem;
  }

  .name-col .currency-name {
    gap: 0.25rem;
  }

  .name-col .currency-name .flag-container {
    width: 30px;
    height: 22px;
    border-radius: 6px;
  }

  .name-col .currency-name span {
    font-size: 0.8rem;
  }

  .currency-code {
    font-size: 0.7rem;
    padding: 4px 6px;
  }

  .rate-value {
    font-size: 0.75rem;
    padding: 4px 6px;
  }

  .calculator-card {
    width: 95vw;
    max-width: 95vw;
    border-radius: 12px;
  }

  .calculator-header {
    padding: 0.8rem;
  }

  .calculator-body {
    padding: 0.8rem;
  }

  .currency-info {
    gap: 0.5rem;
  }

  .flag-container {
    width: 40px;
    height: 30px;
  }

  .currency-details .text-h6 {
    font-size: 1rem;
  }

  .rate-info {
    padding: 0.8rem;
  }

  .rate-info .rate-value {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .currency-page {
    padding: 0.3rem;
  }

  .modern-header {
    margin-bottom: 0.8rem;
    border-radius: 12px;
  }

  .header-content {
    padding: 0.6rem;
    gap: 0.6rem;
  }

  .logo-container {
    padding: 6px;
  }

  .logo-text {
    font-size: 1rem;
  }

  .title-section {
    padding: 6px;
  }

  .text-h4 {
    font-size: 1.3rem;
  }

  .clock-container {
    transform: scale(0.6);
    margin-top: -20px;
    margin-bottom: -20px;
  }

  .currency-row {
    padding: 0.4rem 0.1rem;
    font-size: 0.75rem;
  }

  .name-col .currency-name .flag-container {
    width: 25px;
    height: 18px;
  }

  .name-col .currency-name span {
    font-size: 0.75rem;
  }

  .rate-value {
    padding: 3px 5px;
  }

  .calculator-card {
    width: 98vw;
    max-width: 98vw;
    border-radius: 10px;
  }

  .calculator-header {
    padding: 0.6rem;
  }

  .calculator-body {
    padding: 0.6rem;
  }
}

.currency-card {
  border-radius: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  overflow: hidden;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.currency-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);

  .currency-header-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .currency-icon {
      font-size: 1.75rem;
      color: #4f46e5;
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
      color: #1e293b;
    }
  }

  .currency-search {
    width: 250px;
  }
}

.currency-list {
  max-height: 600px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(99, 102, 241, 0.3);
    border-radius: 10px;
  }
}

.currency-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  animation: slideIn 0.5s ease forwards;
  opacity: 0;
  cursor: pointer;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: rgba(248, 250, 252, 0.8);
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  }

  &.header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: rgba(248, 250, 252, 0.95);
    backdrop-filter: blur(8px);
    font-weight: 600;
    color: #475569;
    border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    padding: 1.25rem 2rem;
  }
}

.currency-col {
  padding: 0 1rem;
}

.name-col {
  .currency-name {
    display: flex;
    align-items: center;
    gap: 1.25rem;

    .flag-container {
      position: relative;
      width: 56px;
      height: 40px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.15) rotate(3deg);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
      }

      .flag-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .flag-shadow {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
        pointer-events: none;
      }
    }

    span {
      font-weight: 600;
      color: #1e293b;
      font-size: 1.15rem;
    }
  }
}

.currency-code {
  font-family: 'JetBrains Mono', monospace;
  font-size: 1rem;
  padding: 6px 12px;
  border-radius: 8px;
  background: rgba(248, 250, 252, 0.9) !important;
  color: #475569 !important;
}

.rate-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.rate-value {
  display: inline-flex;
  align-items: center;
  gap: 0.55rem;
  font-weight: 700;
  font-size: 1.16rem;
  padding: 25px 35px;
  border-radius: 14px;
  box-shadow: 0 2px 8px #2563eb0a;
  border: 1.5px solid #2563eb22;
  transition: all 0.26s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  .q-icon {
    opacity: 0.96;
    font-size: 1.35em;
  }

  &.sale {
    background: linear-gradient(90deg, rgba(34,197,94,0.13), rgba(34,197,94,0.07));
    color: #15803d !important;
    border-color: rgba(34,197,94,0.16); // keep green for sale, but blue is main elsewhere
    box-shadow: 0 2px 10px rgba(34,197,94,0.08);
    &:hover, &:focus {
      transform: translateY(-2px) scale(1.045);
      box-shadow: 0 6px 18px rgba(34,197,94,0.16);
      background: linear-gradient(90deg, rgba(34,197,94,0.17), rgba(34,197,94,0.10));
    }
  }

  &.buy {
    background: linear-gradient(90deg, rgba(239,68,68,0.13), rgba(239,68,68,0.07));
    color: #b91c1c !important;
    border-color: rgba(239,68,68,0.16); // keep red for buy, but blue is main elsewhere
    box-shadow: 0 2px 10px rgba(239,68,68,0.08);
    &:hover, &:focus {
      transform: translateY(2px) scale(1.045);
      box-shadow: 0 6px 18px rgba(239,68,68,0.16);
      background: linear-gradient(90deg, rgba(239,68,68,0.17), rgba(239,68,68,0.10));
    }
  }
}


@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-title {
  animation: fadeInDown 0.8s ease forwards;
}

.animate-fade {
  animation: fadeIn 1s ease forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .currency-page {
    padding: 0.5rem;
  }

  .modern-header {
    margin-bottom: 1.2rem;
    border-radius: 18px;
    padding: 0.5rem 0.5rem 0.2rem 0.5rem;
  }

  .header-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .currency-card {
    border-radius: 14px;
    margin-bottom: 1.2rem;
  }

  .currency-row {
    grid-template-columns: 2fr 1fr 1fr 1fr;
    padding: 0.7rem 0.2rem;
    font-size: 0.83rem;
    gap: 0.25rem;

    &.header {
      padding: 0.5rem 0.2rem;
      font-size: 0.77rem;
    }
  }

  .currency-col {
    padding: 0 0.12rem;
  }

  .name-col {
    .currency-name {
      gap: 0.3rem;

      .flag-container {
        width: 34px;
        height: 24px;
      }

      span {
        font-size: 0.86rem;
      }
    }
  }

  .rate-value {
    font-size: 0.8rem;
    padding: 4px 8px;
    border-radius: 8px;
  }

  .calculator-card {
    min-width: unset;
    width: 100%;
    margin: 0;
    border-radius: 13px;
    border: 1.5px solid #2563eb33;
    box-shadow: 0 4px 24px #2563eb18;
  }

  .currency-info {
    padding: 0.7rem;
    gap: 0.5rem;

    .flag-container {
      width: 40px;
      height: 30px;
    }

    .currency-details {
      .text-h6 {
        font-size: 1.05rem;
      }
    }
  }

  .rate-info {
    padding: 0.7rem;

    .rate-label {
      font-size: 0.73rem;
    }

    .rate-value {
      font-size: 1.12rem;
    }

    .usd-rate {
      font-size: 0.7rem;
    }
  }

  .operation-inputs {
    padding: 0.7rem;

    .operation-display {
      font-size: 1.1rem;
      padding: 0.5rem;
    }
  }

  .math-operations {
    .operation-btn {
      min-width: 40px;
      height: 40px;
      font-size: 1rem;
      border-radius: 8px;
    }
  }

  .results-container {
    .result-item {
      padding: 0.7rem;

      .result-label {
        font-size: 0.8rem;
      }

      .result-value {
        font-size: 1.12rem;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .currency-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);

    &::before {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(59, 130, 246, 0.05) 100%);
    }

    &::after {
      background: linear-gradient(to top, rgba(59, 130, 246, 0.1), transparent);
    }
  }

  .header-card {
    background: rgba(30, 41, 59, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    .text-h4 {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .update-chip {
      background: rgba(59, 130, 246, 0.2) !important;
      color: #93c5fd !important;
    }
  }

  .currency-card {
    background: rgba(30, 41, 59, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .currency-row {
    border-bottom-color: rgba(51, 65, 85, 0.8);

    &:hover {
      background-color: rgba(51, 65, 85, 0.9);
    }

    &.header {
      background: rgba(51, 65, 85, 0.9);
      color: #e2e8f0;
      border-bottom-color: rgba(71, 85, 105, 0.8);
    }
  }

  .name-col {
    .currency-name span {
      color: #f8fafc;
    }
  }

  .currency-code {
    background: rgba(51, 65, 85, 0.9) !important;
    color: #cbd5e1 !important;
  }

  .rate-value {
    &.sale {
      background: rgba(34, 197, 94, 0.15) !important;
      color: #4ade80 !important;
    }

    &.buy {
      background: rgba(239, 68, 68, 0.15) !important;
      color: #f87171 !important;
    }
  }

  .currency-info {
    background: rgba(51, 65, 85, 0.8);

    .currency-details .text-h6 {
      color: #f8fafc;
    }
  }

  .rate-info {
    background: rgba(51, 65, 85, 0.8);

    .rate-label {
      color: #94a3b8;
    }

    .rate-value {
      color: #f8fafc;
    }

    .usd-rate {
      color: #94a3b8;
    }
  }

  .calculator-inputs {
    .q-input .q-field__control {
      background: rgba(51, 65, 85, 0.8);
    }
  }

  .operation-inputs {
    background: rgba(51, 65, 85, 0.8);

    .operation-display {
      background: rgba(30, 41, 59, 0.9);
      color: #f8fafc;

      .first-number, .second-number {
        color: #60a5fa;
      }

      .operation {
        color: #94a3b8;
      }
    }
  }

  .math-operations {
    .operation-btn {
      background: rgba(51, 65, 85, 0.8);
      color: #f8fafc;

  &:hover {
        background: rgba(59, 130, 246, 0.2);
      }
  }
}

.results-container {
  .result-item {
      background: rgba(51, 65, 85, 0.8);
      border-color: rgba(59, 130, 246, 0.2);
    }
  }

  .result-section {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(59, 130, 246, 0.2);

    .result-value {
      &.buy {
        color: #f87171;
      }

      &.sale {
        color: #4ade80;
      }
    }
  }
}

.language-section {
  position: relative;
  z-index: 1;
}

.lang-dropdown {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 10px 20px;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 140px;
  text-align: center;
  font-weight: 500;

  &.rtl-layout {
    direction: rtl;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.98);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
  }
}

.lang-flag {
  width: 24px;
  height: 18px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .lang-dropdown {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(59, 130, 246, 0.3);
    color: #f8fafc;

      &:hover {
      background: rgba(30, 41, 59, 0.98);
      border-color: rgba(59, 130, 246, 0.4);
    }
  }

  .wave {
    background: rgba(255, 255, 255, 0.1);
  }
}

.q-item {
  &.rtl-layout {
    flex-direction: row-reverse;
  }
}

// Update responsive styles
@media (max-width: 768px) {
  .header-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .header-left,
  .header-center,
  .header-right {
    justify-content: center;
  }

  .logo-container {
    justify-content: center;
  }

  .logo-circle {
    width: 48px;
    height: 48px;
  }

  .logo-text {
    font-size: 1.5rem;
  }

  .clock {
    width: 120px;
    height: 120px;
  }

  .digital-time {
    font-size: 1rem;
  }

  .title-section {
        text-align: center;
  }

  .language-section {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .lang-dropdown {
    width: 100%;
    max-width: 200px;
  }
}

.calculator-card {
  min-width: 400px;
  max-width: 90vw;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.1);
  overflow: hidden;
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  50% {
    opacity: 0.5;
    transform: translateY(15px) scale(0.97);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.calculator-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(29, 78, 216, 0.05) 100%);
  border-bottom: 1px solid rgba(37, 99, 235, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.2), transparent);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent,
      rgba(37, 99, 235, 0.5),
      rgba(29, 78, 216, 0.5),
      rgba(37, 99, 235, 0.5),
      transparent
    );
    animation: borderFlow 3s linear infinite;
  }
}

@keyframes borderFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.currency-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.flag-container {
  width: 64px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 8px 24px rgba(37, 99, 235, 0.2);
  }
}

.currency-details {
  .text-h6 {
    font-size: 1.25rem;
    font-weight: 600;
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientFlow 3s ease infinite;
  }
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.close-btn {
  color: #64748b;
      transition: all 0.3s ease;
  position: relative;
  z-index: 1;

      &:hover {
    color: #ef4444;
    transform: rotate(90deg);
  }
}

.calculator-body {
  padding: 1.5rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.2), transparent);
  }
}

.calculator-tabs {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  background: rgba(248, 250, 252, 0.8);
  padding: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(37, 99, 235, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
  }
}

.rate-info {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(37, 99, 235, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #2563eb, #3b82f6, #2563eb);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  .rate-value {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2563eb;
    margin-bottom: 0.25rem;
    font-family: 'JetBrains Mono', monospace;
    text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
  }
}

.calculator-inputs {
  margin-bottom: 1.5rem;
}

.amount-input {
  .q-field__control {
    border-radius: 12px;
    background: rgba(248, 250, 252, 0.8);
    transition: all 0.3s ease;
    border: 1px solid rgba(37, 99, 235, 0.1);

    &:hover {
      background: rgba(248, 250, 252, 0.95);
      border-color: rgba(37, 99, 235, 0.2);
    }

    &:focus-within {
      background: rgba(248, 250, 252, 0.98);
      border-color: rgba(37, 99, 235, 0.3);
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
    }
  }
}

.operation-inputs {
  margin-top: 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 1.25rem;
  border: 1px solid rgba(37, 99, 235, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

  .operation-display {
  font-size: 1.25rem;
  font-weight: 500;
  color: #1e293b;
    text-align: center;
    margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(37, 99, 235, 0.1);
  font-family: 'JetBrains Mono', monospace;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

    .first-number, .second-number {
    color: #3b82f6;
    font-weight: 600;
    }

    .operation {
      color: #64748b;
    margin: 0 0.5rem;
    font-weight: 500;
  }
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

  .operation-btn {
    min-width: 48px;
    height: 48px;
  font-size: 1.25rem;
  font-weight: 500;
  border-radius: 12px;
    background: rgba(248, 250, 252, 0.8);
  color: #2563eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(37, 99, 235, 0.1);

    &:hover {
    background: rgba(37, 99, 235, 0.1);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    }

  &:active {
    transform: translateY(0) scale(0.95);
  }
}

.results-container {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 1.25rem;
  border: 1px solid rgba(37, 99, 235, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

.result-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.result-section {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(37, 99, 235, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
  }

  .result-label {
    font-size: 0.875rem;
        color: #64748b;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .result-value {
    font-size: 1.25rem;
    font-weight: 600;
    font-family: 'JetBrains Mono', monospace;

    &.buy {
      color: #ef4444;
      text-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
    }

    &.sale {
      color: #22c55e;
      text-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);
    }
  }
}

.calculator-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(37, 99, 235, 0.1);
  background: rgba(248, 250, 252, 0.8);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.2), transparent);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .calculator-card {
    background: rgba(30, 41, 59, 0.98);
  }

  .calculator-header {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.2) 0%, rgba(29, 78, 216, 0.1) 100%);
    border-bottom-color: rgba(37, 99, 235, 0.2);

    &::before {
      background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    }
  }

  .currency-details .text-h6 {
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .close-btn {
          color: #94a3b8;

    &:hover {
      color: #f87171;
    }
  }

  .rate-info {
    background: rgba(51, 65, 85, 0.8);
    border-color: rgba(37, 99, 235, 0.2);

    .rate-value {
      color: #818cf8;
    }
  }

  .operation-btn {
    background: rgba(51, 65, 85, 0.8);
    color: #818cf8;
    border-color: rgba(37, 99, 235, 0.2);

    &:hover {
      background: rgba(37, 99, 235, 0.2);
    }
  }

  .result-section {
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(37, 99, 235, 0.2);

    .result-value {
      &.buy {
        color: #f87171;
      }

      &.sale {
        color: #4ade80;
      }
    }
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

// Add RTL support
[dir="rtl"] {
  .currency-row {
    &:hover {
      transform: translateX(-8px);
    }
  }

  .operation-display {
    direction: ltr;
  }

  .result-value {
    direction: ltr;
  }

  .digital-time {
    direction: ltr;
  }
}

.digital-time-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.digital-time {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  font-family: 'JetBrains Mono', monospace;
  padding: 0.5rem 1rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(37, 99, 235, 0.1);
  transition: all 0.3s ease;
}

.logo-mini {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-circle-mini {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
}

.wave-animation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  transition: transform 0.3s ease-out;

  .wave {
    position: absolute;
    width: 200%;
    height: 10px;
    background-size: 72px 10px;
    animation: miniWave 8s linear infinite;

    &.wave1 {
      bottom: 5px;
      opacity: 0.7;
      height: 8px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='0.7' d='M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    }

    &.wave2 {
      bottom: 0;
      opacity: 0.5;
      height: 6px;
      animation-duration: 12s;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='0.8' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,229.3C672,224,768,192,864,165.3C960,139,1056,117,1152,133.3C1248,149,1344,203,1392,229.3L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    }
  }
}

@keyframes miniWave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.clock {
  position: relative;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              inset 0 2px 4px rgba(255, 255, 255, 0.8),
              inset 0 -2px 4px rgba(0, 0, 0, 0.2);
  border: 8px solid #f8fafc;
  margin: 0 auto;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    z-index: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0.5;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15),
                inset 0 2px 4px rgba(255, 255, 255, 0.8),
                inset 0 -2px 4px rgba(0, 0, 0, 0.2);
  }

  .logo-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 0;
    opacity: 0.2;
    transition: opacity 0.3s ease;

    .logo-bg-image {
      width: 80%;
      height: 80%;
      object-fit: contain;
      filter: grayscale(0.2) contrast(0.9);
      animation: pulse 3s infinite alternate;
    }
  }

  &:hover .logo-background {
    opacity: 0.3;

    .logo-bg-image {
      filter: grayscale(0) contrast(1);
    }
  }

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1.02);
  }
}
}

.clock-face {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  z-index: 1;
}

.clock-hand {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: left center;
  z-index: 2;

  &.hours {
    width: 30%;
    height: 4px;
    background: #1e293b;
    border-radius: 2px;
    transform: translateY(-50%) rotate(var(--hours-rotation, 0deg));
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  &.minutes {
    width: 40%;
    height: 3px;
    background: #334155;
    border-radius: 1.5px;
    transform: translateY(-50%) rotate(var(--minutes-rotation, 0deg));
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  }

  &.seconds {
    width: 45%;
    height: 2px;
    background: #2563eb;
    border-radius: 1px;
    transform: translateY(-50%) rotate(var(--seconds-rotation, 0deg));
    box-shadow: 0 1px 2px rgba(37, 99, 235, 0.2);
  }
}

.clock-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #1e293b;
  transform: translate(-50%, -50%);
  z-index: 3;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #2563eb;
    transform: translate(-50%, -50%);
  }
}

.logo-mini {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-circle-mini {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
  }
}

.wave-animation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  transition: transform 0.3s ease-out;

  .wave {
    position: absolute;
    width: 200%;
    height: 10px;
    background-size: 72px 10px;
    animation: miniWave 8s linear infinite;

    &.wave1 {
      bottom: 5px;
      opacity: 0.7;
      height: 8px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='0.7' d='M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    }

    &.wave2 {
      bottom: 0;
      opacity: 0.5;
      height: 6px;
      animation-duration: 12s;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='0.8' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,229.3C672,224,768,192,864,165.3C960,139,1056,117,1152,133.3C1248,149,1344,203,1392,229.3L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    }
  }
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.floating-element {
  position: absolute;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  will-change: transform;

  &.bubble {
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  }

  &.seaweed {
    background: rgba(37, 99, 235, 0.2);
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
    box-shadow: 0 0 8px rgba(37, 99, 235, 0.3);
    animation-name: sway !important;
  }

  &.fish {
    background: rgba(37, 99, 235, 0.25);
    border-radius: 70% 30% 30% 70% / 50% 50% 50% 50%;
    box-shadow: 0 0 8px rgba(37, 99, 235, 0.3);
  }

  &.starfish {
    background: rgba(37, 99, 235, 0.2);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    box-shadow: 0 0 8px rgba(37, 99, 235, 0.3);
    animation-name: rotate !important;
  }
}

.bubbles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.bubble {
  position: absolute;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  animation: rise linear infinite;
  will-change: transform;
  box-shadow: 0 0 3px rgba(255, 255, 255, 0.5), inset 0 0 4px rgba(255, 255, 255, 0.5);

  &::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 20%;
    height: 20%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
  }

  &.b1 {
    width: 12px;
    height: 12px;
    left: 10%;
    bottom: -20px;
    animation-duration: 8s;
    animation-delay: 1s;
  }

  &.b2 {
    width: 18px;
    height: 18px;
    left: 20%;
    bottom: -20px;
    animation-duration: 10s;
    animation-delay: 3s;
  }

  &.b3 {
    width: 10px;
    height: 10px;
    left: 30%;
    bottom: -20px;
    animation-duration: 7s;
    animation-delay: 2s;
  }

  &.b4 {
    width: 15px;
    height: 15px;
    left: 40%;
    bottom: -20px;
    animation-duration: 11s;
    animation-delay: 0s;
  }

  &.b5 {
    width: 9px;
    height: 9px;
    left: 50%;
    bottom: -20px;
    animation-duration: 9s;
    animation-delay: 4s;
  }

  &.b6 {
    width: 14px;
    height: 14px;
    left: 60%;
    bottom: -20px;
    animation-duration: 8s;
    animation-delay: 2.5s;
  }

  &.b7 {
    width: 11px;
    height: 11px;
    left: 70%;
    bottom: -20px;
    animation-duration: 12s;
    animation-delay: 1.5s;
  }

  &.b8 {
    width: 16px;
    height: 16px;
    left: 80%;
    bottom: -20px;
    animation-duration: 9s;
    animation-delay: 3.5s;
  }

  &.b9 {
    width: 10px;
    height: 10px;
    left: 85%;
    bottom: -20px;
    animation-duration: 7s;
    animation-delay: 0.5s;
  }

  &.b10 {
    width: 13px;
    height: 13px;
    left: 95%;
    bottom: -20px;
    animation-duration: 10s;
    animation-delay: 4.5s;
  }
}

@keyframes rise {
  0% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-300px);
    opacity: 0;
  }
}

@keyframes floatLeft {
  0% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(-20px) translateY(-15px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes floatRight {
  0% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(20px) translateY(-15px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes sway {
  0%, 100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.modern-header:hover .header-waves::after {
  opacity: 0.9;
  transform: translateY(calc(var(--mouse-y, 0) * 8px));
}

@media (prefers-color-scheme: dark) {
  .modern-header {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(30, 41, 59, 0.85) 100%);
    border-color: rgba(99, 102, 241, 0.2);

    .header-waves::after {
      background: linear-gradient(to top,
        rgba(99, 102, 241, 0.1) 0%,
        rgba(99, 102, 241, 0) 100%);
    }

    .wave {
      &.wave1 {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.18' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,229.3C672,224,768,192,864,165.3C960,139,1056,117,1152,133.3C1248,149,1344,203,1392,229.3L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
        filter: drop-shadow(0 0 10px rgba(99,102,241,0.2)) brightness(calc(var(--wave-brightness, 1) * 1.1)) saturate(calc(var(--wave-saturation, 1) * 1.2));
      }

      &.wave2 {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.12' d='M0,192L34.3,181.3C68.6,171,137,149,206,144C274.3,139,343,149,411,176C480,203,549,245,617,261.3C685.7,277,754,267,823,250.7C891.4,235,960,213,1029,218.7C1097.1,224,1166,256,1234,261.3C1302.9,267,1371,245,1406,234.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
        filter: drop-shadow(0 0 6px rgba(99,102,241,0.15)) brightness(var(--wave-brightness, 1)) saturate(var(--wave-saturation, 1));
      }

      &.wave3 {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.08' d='M0,160L34.3,144C68.6,128,137,96,206,106.7C274.3,117,343,171,411,202.7C480,235,549,245,617,229.3C685.7,213,754,171,823,149.3C891.4,128,960,128,1029,149.3C1097.1,171,1166,213,1234,213.3C1302.9,213,1371,171,1406,149.3L1440,128L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
        filter: drop-shadow(0 0 4px rgba(99,102,241,0.1)) brightness(calc(var(--wave-brightness, 1) * 0.9)) saturate(calc(var(--wave-saturation, 1) * 0.9));
      }
    }
}

// Bottom ocean elements
.bottom-floating-elements {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.bottom-bubble {
  position: absolute;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  animation: bottomRise linear infinite;
  will-change: transform;
  box-shadow: 0 0 3px rgba(255, 255, 255, 0.5), inset 0 0 4px rgba(255, 255, 255, 0.5);

  &::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 20%;
    height: 20%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
  }

  &.b1 {
    width: 12px;
    height: 12px;
    left: 10%;
    bottom: -20px;
    animation-duration: 8s;
    animation-delay: 1s;
  }

  &.b2 {
    width: 18px;
    height: 18px;
    left: 20%;
    bottom: -20px;
    animation-duration: 10s;
    animation-delay: 3s;
  }

  &.b3 {
    width: 10px;
    height: 10px;
    left: 70%;
    bottom: -20px;
    animation-duration: 7s;
    animation-delay: 2s;
  }

  &.b4 {
    width: 15px;
    height: 15px;
    left: 60%;
    bottom: -20px;
    animation-duration: 11s;
    animation-delay: 0s;
  }

  &.b5 {
    width: 9px;
    height: 9px;
    left: 85%;
    bottom: -20px;
    animation-duration: 9s;
    animation-delay: 4s;
  }

  &.b6 {
    width: 14px;
    height: 14px;
    left: 90%;
    bottom: -20px;
    animation-duration: 8s;
    animation-delay: 2.5s;
  }
}

.bottom-fish {
  position: absolute;
  background: rgba(99, 102, 241, 0.25);
  border-radius: 70% 30% 30% 70% / 50% 50% 50% 50%;
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.3);
  animation: bottomFishSwim 15s linear infinite;
  will-change: transform;
  transform-origin: center;

  &::before {
    content: '';
    position: absolute;
    width: 40%;
    height: 40%;
    background: rgba(99, 102, 241, 0.4);
    border-radius: 50%;
    top: 30%;
    left: 20%;
  }

  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 0 8px 15px;
    border-color: transparent transparent transparent rgba(99, 102, 241, 0.25);
    right: -15px;
    top: calc(50% - 8px);
  }

  &.f1 {
    width: 30px;
    height: 15px;
    left: -30px;
    bottom: 20%;
    animation-duration: 20s;
  }

  &.f2 {
    width: 40px;
    height: 20px;
    right: -40px;
    bottom: 30%;
    transform: scaleX(-1);
    animation-duration: 25s;
    animation-delay: 5s;
  }
}

.bottom-seaweed {
  position: absolute;
  bottom: 0;
  width: 15px;
  height: 60px;
  background: rgba(99, 102, 241, 0.2);
  border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
  transform-origin: bottom center;
  animation: bottomSway 5s ease-in-out infinite;

  &::before, &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 40%;
    background: rgba(99, 102, 241, 0.15);
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
  }

  &::before {
    top: -15%;
    left: -30%;
    transform: rotate(-15deg);
    animation: bottomSway 5s ease-in-out infinite reverse;
    animation-delay: 0.5s;
  }

  &::after {
    top: -20%;
    right: -30%;
    transform: rotate(15deg);
    animation: bottomSway 5s ease-in-out infinite;
    animation-delay: 1s;
  }

  &.s1 {
    left: 15%;
    height: 70px;
  }

  &.s2 {
    left: 45%;
    height: 50px;
  }

  &.s3 {
    right: 20%;
    height: 60px;
  }
}

@keyframes bottomRise {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-50px) translateX(10px);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) translateX(-5px);
    opacity: 0;
  }
}

@keyframes bottomFishSwim {
  0% {
    transform: translateX(0) translateY(0);
  }
  25% {
    transform: translateX(calc(100vw + 50px)) translateY(-20px);
  }
  50% {
    transform: translateX(calc(100vw + 50px)) translateY(20px);
  }
  75% {
    transform: translateX(0) translateY(-10px);
  }
  100% {
    transform: translateX(0) translateY(0);
  }
}

@keyframes bottomSway {
  0%, 100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

@media (prefers-color-scheme: dark) {
  .page-container {
    &::after {
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.12' d='M0,128L48,122.7C96,117,192,107,288,133.3C384,160,480,224,576,224C672,224,768,160,864,149.3C960,139,1056,181,1152,208C1248,235,1344,245,1392,250.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E"),
                  url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.16' d='M0,288L48,277.3C96,267,192,245,288,213.3C384,181,480,139,576,144C672,149,768,203,864,218.7C960,235,1056,213,1152,181.3C1248,149,1344,107,1392,85.3L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E"),
                  url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%236366f1' fill-opacity='0.2' d='M0,224L48,229.3C96,235,192,245,288,229.3C384,213,480,171,576,165.3C672,160,768,192,864,197.3C960,203,1056,181,1152,192C1248,203,1344,245,1392,266.7L1440,288L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
    }
  }

  .bottom-ocean {
    background: linear-gradient(to top, rgba(99,102,241,0.12), transparent);
    height: 150px;
  }

  .bottom-fish {
    background: rgba(99, 102, 241, 0.25);
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.3);

    &::before {
      background: rgba(99, 102, 241, 0.4);
    }

    &::after {
      border-color: transparent transparent transparent rgba(99, 102, 241, 0.25);
    }
  }

  .bottom-seaweed {
    background: rgba(99, 102, 241, 0.2);

    &::before, &::after {
      background: rgba(99, 102, 241, 0.15);
    }
  }
}

.modern-header:hover .header-waves::after {
  opacity: 0.9;
  transform: translateY(calc(var(--mouse-y, 0) * 8px));
}

.header-waves {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 0;
  transition: all 0.6s ease;
  perspective: 1200px;
  transform-style: preserve-3d;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60%;
    background: linear-gradient(to top,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
    z-index: 4;
    opacity: 0.75;
    transition: opacity 0.6s ease, transform 0.6s ease;
    transform: translateY(calc(var(--mouse-y, 0) * 5px));
    filter: blur(6px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 10%, rgba(37,99,235,0.19), transparent 60%),
               radial-gradient(circle at 10% 90%, rgba(37,99,235,0.13), transparent 50%);
    opacity: 0.4;
    z-index: 0;
    transition: opacity 0.6s ease;
  }
}

// Updated dark mode toggle styles
.header-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.control-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 12px 15px;
  box-shadow: 0 8px 16px rgba(37, 99, 235, 0.08);
  border: 1px solid rgba(37, 99, 235, 0.15);
  transition: all 0.3s ease;
  margin-bottom: 15px;
  transform: translateZ(5px);

  &:hover {
    transform: translateZ(10px) scale(1.02);
    box-shadow: 0 12px 24px rgba(37, 99, 235, 0.12);
  }
}

.dark-mode-toggle {
  position: relative;

  .toggle-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e293b;
    margin-left: 8px;
    transition: color 0.3s ease;
  }

  .q-toggle {
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.language-selector {
  .lang-dropdown {
    border-radius: 20px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(37, 99, 235, 0.15);
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.08);

    &:hover {
      transform: scale(1.05);
      background: rgba(255, 255, 255, 0.25);
      box-shadow: 0 6px 12px rgba(37, 99, 235, 0.12);
    }

    .q-icon {
      font-size: 1.4rem;
    }
  }
}

.language-list {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.15);
  padding: 8px;

  .q-item {
    border-radius: 12px;
    margin-bottom: 4px;
    padding: 10px 15px;

    &.active {
      background: rgba(37, 99, 235, 0.1);
    }

    &:hover {
      background: rgba(37, 99, 235, 0.05);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Update the logo container
.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  direction: rtl;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.06);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(37, 99, 235, 0.15);
  transform: none;
  width: 100%;

  &:hover {
    transform: scale(1.05) translateZ(10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.12);
  }
}

// Dark theme specific styles
:root {
  --bg-primary: linear-gradient(135deg, #e0edfa 0%, #f0f6fd 100%);
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --card-bg: rgba(255, 255, 255, 0.95);
  --header-bg: linear-gradient(135deg, rgba(222,239,255,0.92) 0%, rgba(230,244,255,0.86) 100%);
  --panel-bg: rgba(255, 255, 255, 0.1);
  --primary-color: #2563eb;
  --primary-color-light: rgba(37, 99, 235, 0.12);
  --primary-color-hover: rgba(37, 99, 235, 0.18);
  --shadow-color: rgba(37, 99, 235, 0.16);
  --border-color: rgba(37, 99, 235, 0.25);
  --wave-color: #2563eb;
}

.dark-theme {
  --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --card-bg: rgba(30, 41, 59, 0.95);
  --header-bg: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(30, 41, 59, 0.85) 100%);
  --panel-bg: rgba(15, 23, 42, 0.3);
  --primary-color: #60a5fa;
  --primary-color-light: rgba(96, 165, 250, 0.15);
  --primary-color-hover: rgba(96, 165, 250, 0.25);
  --shadow-color: rgba(15, 23, 42, 0.35);
  --border-color: rgba(99, 102, 241, 0.25);
  --wave-color: #6366f1;
}

// Use CSS variables
.currency-page {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2.5rem 1.5rem 2.5rem 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 0 1px rgba(59,130,246,0.03);
  color: var(--text-primary);
  transition: background 0.3s ease, color 0.3s ease;
}

.modern-header {
  background: var(--header-bg);
  backdrop-filter: blur(16px);
  border-radius: 36px;
  box-shadow:
    0 16px 60px var(--shadow-color),
    0 2px 10px rgba(37, 99, 235, 0.08),
    inset 0 1px 3px rgba(255, 255, 255, 0.6);
  margin-bottom: 3rem;
  border: 1px solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.2, 0, 0.4, 1), background 0.3s ease;
}

.control-panel {
  background: var(--panel-bg);
}

.dark-mode-toggle .toggle-label {
  color: var(--text-primary);
}

.text-h4 {
  color: var(--text-primary);
}

.language-list {
  background: var(--card-bg);
}

.wave.wave1, .wave.wave2, .wave.wave3 {
  background-color: var(--wave-color);
}

@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    gap: 10px;
  }

  .control-panel {
    flex-direction: column;
    padding: 10px;
    gap: 10px;
  }

  .dark-mode-toggle, .language-selector {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.header-weather {
  grid-column: span 1;
  transition: all 0.3s ease;
  transform: translateZ(6px);

  @media (max-width: 768px) {
    grid-column: span 1;
    order: 2;
  }
}

.weather-widget {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 1rem;
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.15);
  transition: all 0.3s ease;
  height: 100%;
  transform-style: preserve-3d;
  perspective: 800px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 120px;

  &:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 30px rgba(37, 99, 235, 0.15);
    border-color: rgba(37, 99, 235, 0.25);
  }

  @media (max-width: 768px) {
    padding: 0.8rem;
    min-height: auto;
  }
}

.weather-loading, .weather-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  gap: 10px;
  color: var(--text-primary);
  width: 100%;
}

.weather-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.weather-main {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;

  .weather-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    transform-style: preserve-3d;

    .q-icon {
      filter: drop-shadow(0 5px 15px rgba(37, 99, 235, 0.2));
      transform: translateZ(20px);
      transition: all 0.4s ease;

      &:hover {
        transform: translateZ(30px) scale(1.1) rotate(5deg);
      }
    }
  }

  .weather-temp {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    text-shadow: 0 2px 10px rgba(37, 99, 235, 0.15);

    span {
      font-size: 1.5rem;
      font-weight: 400;
      margin-left: 2px;
    }
  }
}

.weather-location {
  text-align: center;
  color: var(--text-primary);
  font-size: 0.95rem;
  font-weight: 500;
}

// Update header layout for better mobile responsiveness
.header-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  padding: 2.5rem;
  position: relative;
  gap: 2.5rem;
  z-index: 1;

  // Apply a subtle glass effect to the inner content
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    bottom: 12px;
    border-radius: 28px;
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    opacity: 0.35;
    z-index: -1;
    transition: opacity 0.4s ease;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.2rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.refresh-hint {
  position: absolute;
  bottom: 5px;
  right: 5px;
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 0.7rem;
  opacity: 0.6;
  color: var(--text-secondary);
  transition: opacity 0.3s ease;

  .q-icon {
    margin-top: 1px;
  }
}

.weather-widget:hover .refresh-hint {
  opacity: 1;
}

// Add these new CSS classes to the styles section
.rtl-layout {
  direction: rtl;
  text-align: right;
}

.rtl-text {
  direction: rtl;
  text-align: right;
  font-family: 'Vazirmatn', 'Tajawal', sans-serif; /* Add appropriate RTL fonts */
}

.ltr-layout {
  direction: ltr;
  text-align: left;
}

.ltr-text {
  direction: ltr;
  text-align: left;
}

// Update logo container for better RTL support
.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  padding: 12px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.06);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(37, 99, 235, 0.15);
  transform: none;
  justify-content: flex-start;

  &.rtl-layout {
    flex-direction: row-reverse;
  }

  &:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.12);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    justify-content: center;
    gap: 0.5rem;

    &.rtl-layout {
      flex-direction: column;
    }
  }
}

.logo-image {
  border-radius: 50%;
  object-fit: contain;
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.35);
  transition: all 0.4s ease;

  &:hover {
    transform: scale(1.08) rotate(5deg);
    box-shadow: 0 10px 28px rgba(37, 99, 235, 0.45);
  }
}

// Update header actions for RTL support
.header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  background: var(--panel-bg);
  border-radius: 24px;
  padding: 12px 16px;
  backdrop-filter: blur(8px);
  box-shadow: 0 8px 24px var(--shadow-color);
  border: 1px solid var(--border-color);
  transform: translateZ(5px);
  transition: all 0.4s ease;
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;

  &.rtl-layout {
    flex-direction: row-reverse;
  }

  /* Rest of existing header-actions styles */

  @media (max-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
    padding: 8px;
    gap: 8px;
    border-radius: 16px;

    &.rtl-layout {
      flex-direction: row-reverse;
      flex-wrap: wrap;
    }

    &::before {
      border-radius: 15px;
    }

    &::after {
      border-radius: 16.5px;
    }
  }
}

// Fix language dropdown alignment
.language-selector {
  .lang-dropdown {
    &.rtl-layout {
      padding-right: 15px;
      padding-left: 10px;
    }
  }
}

.q-item {
  &.rtl-layout {
    flex-direction: row-reverse;
  }
}

// Update header content grid for better RTL support
.header-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  padding: 2.5rem;
  position: relative;
  gap: 2.5rem;
  z-index: 1;

  &.rtl-layout {
    direction: rtl;
  }

  /* Rest of existing header-content styles */

  @media (max-width: 768px) {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.2rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.mobile-actions {
  @media (max-width: 768px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 6px;

    .last-update-info {
      grid-column: span 2;
      order: -1;
    }

    .dark-mode-toggle, .language-selector {
      width: 100%;
    }
  }
}

// Improve touch interactions
.currency-row {
  -webkit-tap-highlight-color: rgba(0,0,0,0);

  &:active {
    background-color: rgba(37, 99, 235, 0.05);
  }
}

// Fix mobile calculator dialog
.calculator-card {
  @media (max-width: 768px) {
    margin: 0 !important;
    max-height: 90vh;
    overflow-y: auto;
  }
}

// Optimize for mobile touch
.operation-btn {
  @media (max-width: 768px) {
    min-height: 44px; // Ensure touch target size
  }
}

// Improve mobile scrolling
.currency-list {
  @media (max-width: 768px) {
    -webkit-overflow-scrolling: touch;
    max-height: calc(100vh - 270px);
  }
}

// Fix input fields on mobile
.q-field__native, .q-field__control {
  @media (max-width: 768px) {
    font-size: 16px; // Prevent iOS zoom on focus
  }
}

// Fix iOS notch issues
.page-container {
  @supports (padding-top: env(safe-area-inset-top)) {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

// Reduce animations on mobile for better performance
@media (max-width: 768px) {
  .floating-element {
    display: none;
  }

  .bubbles .bubble {
    animation-duration: 15s !important;
  }

  .bottom-floating-elements {
    .bottom-bubble, .bottom-fish {
      animation-duration: 20s !important;
    }
  }

  .header-waves::after {
    opacity: 0.5;
    filter: blur(3px);
  }
}

}
</style>


