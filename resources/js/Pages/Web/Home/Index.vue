<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import PageHeader from '@/Components/PageHeader.vue';
import CurrencyList from '@/Components/CurrencyList.vue';
import CurrencyCalculator from '@/Components/CurrencyCalculator.vue';
import CountryFlag from '@/components/CountryFlag.vue'; // Import the enhanced CountryFlag component
import axios from 'axios';
import '@/theme.css';

// Reactive state
const currencies = ref([]);
const loading = ref(false);
const lastUpdated = ref(null);
const weatherData = ref({
    loading: true,
    error: null,
    temperature: null,
    location: null,
    condition: null
});

// Calculator state
const showCalculator = ref(false);
const selectedCurrency = ref(null);

// PWA and Push Notification state
const isPWA = ref(false);
const isIOS = ref(false);
const pushSupported = ref(false);
const pushPermission = ref('default');
const swRegistration = ref(null);

// Language selection dialog
const showLangDialog = ref(false);

// Current time and date
const currentTime = ref('');
const currentDate = ref('');

// Auto refresh interval
const autoRefreshInterval = ref(null);
const autoRefreshEnabled = ref(true);
const REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes

const updateDateTime = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    currentTime.value = `${hours}:${minutes}`;

    // More detailed date format
    const options = {
        weekday: 'short',
        day: 'numeric',
        month: 'short',
        year: 'numeric'
    };

    // Format according to current language
    try {
        const locale = currentLang.value === 'ku' ? 'ckb' : currentLang.value;
        currentDate.value = now.toLocaleDateString(locale, options);
    } catch (e) {
        // Fallback to English if locale not supported
        currentDate.value = now.toLocaleDateString('en', options);
    }
};


// Translations and localization
const currentLang = ref('en');

const languages = [
    { code: 'en', name: 'English', flag: 'gb' },
    { code: 'ku', name: 'Kurdish', flag: 'kurdistan' },
    { code: 'ar', name: 'Arabic', flag: 'iq' }
];

// Language selection
const selectLanguage = (langCode) => {
    currentLang.value = langCode;
    localStorage.setItem('selectedLanguage', langCode);
    showLangDialog.value = false;

    // Show confirmation notification only if not first time selection
    if (window.Quasar && window.Quasar.Notify && localStorage.getItem('selectedLanguage')) {
        const selectedLang = languages.find(l => l.code === langCode);
        window.Quasar.Notify.create({
            message: translations[langCode].language.changed,
            color: 'primary',
            position: 'top',
            timeout: 2000,
            icon: 'translate'
        });
    }

    // Apply RTL if needed
    document.documentElement.dir = langCode === 'ar' ? 'rtl' : 'ltr';

    // Refresh data to apply new language
    refreshData();
};

// Check if first time launch
const checkFirstLaunch = () => {
    const savedLang = localStorage.getItem('selectedLanguage');
    if (!savedLang) {
        showLangDialog.value = true;
        // Default to English for UI rendering until user selects
        currentLang.value = 'en';
    } else {
        showLangDialog.value = false;
        currentLang.value = savedLang;
        // Apply RTL if needed
        document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';
    }
};

const translations = {
    en: {
        title: 'Currency Exchange Rates',
        currency: 'Currency',
        code: 'Code',
        sale: 'Sale',
        buy: 'Buy',
        close: 'Close',
        refresh: 'Refresh Data',
        lightMode: 'Light Mode',
        darkMode: 'Dark Mode',
        calculate: 'Calculate',
        clear: 'Clear',
        amount: 'Amount',
        result: 'Result',
        dinar: 'Iraqi Dinar',
        operations: 'Operations',
        add: 'Add',
        subtract: 'Subtract',
        multiply: 'Multiply',
        divide: 'Divide',
        cancel: 'Cancel',
        secondAmount: 'Second Amount',
        clearAll: 'Clear All',
        weather: {
            loading: 'Loading weather...',
            tap_refresh: 'Tap to refresh',
            erbil: 'Erbil, Iraq',
            condition: {
                sunny: 'Sunny',
                cloudy: 'Cloudy',
                rainy: 'Rainy',
                stormy: 'Stormy',
                snowy: 'Snowy'
            }
        },
        lastUpdated: 'Last Updated',
        loading: 'Loading currencies...',
        notifications: {
            enable: 'Enable notifications',
            enabled: 'Notifications enabled',
            currencyUpdate: 'Currency Update',
            ratesUpdated: 'Exchange rates have been updated!'
        },
        language: {
            select: 'Select Language',
            change: 'Change Language',
            confirm: 'Confirm',
            cancel: 'Cancel',
            changed: 'Language changed to English'
        },
        toggleMode: 'Switch Mode',
        to: 'to',
        from: 'from',
        selectCurrency: 'Select Currency',
        error: {
            fetchFailed: 'Failed to fetch currency data',
            networkError: 'Network error occurred',
            tryAgain: 'Please try again'
        }
    },
    ku: {
        title: 'نرخی دراو',
        currency: 'دراو',
        code: 'کۆد',
        sale: 'فرۆشتن',
        buy: 'کڕین',
        close: 'داخستن',
        refresh: 'نوێکردنەوەی داتا',
        lightMode: 'ڕووکاری ڕووناک',
        darkMode: 'ڕووکاری تاریک',
        calculate: 'ژمێرکردن',
        clear: 'پاککردنەوە',
        amount: 'بڕ',
        result: 'ئەنجام',
        dinar: 'دیناری عێراقی',
        operations: 'کردارەکان',
        add: 'کۆکردن',
        subtract: 'لێدەرکردن',
        multiply: 'لێکدان',
        divide: 'دابەشکردن',
        cancel: 'هەڵوەشاندنەوە',
        secondAmount: 'بڕی دووەم',
        clearAll: 'پاککردنەوەی هەموو',
        weather: {
            loading: 'بارودۆخی کەش و هەوا بارکردن...',
            tap_refresh: 'کرتە بکە بۆ نوێکردنەوە',
            erbil: 'هەولێر، عێراق',
            condition: {
                sunny: 'خۆر',
                cloudy: 'هەور',
                rainy: 'باران',
                stormy: 'باران و هەورە تریشقە',
                snowy: 'بەفر'
            }
        },
        lastUpdated: 'دوایین نوێکردنەوە',
        loading: 'دراوەکان بارکردن...',
        notifications: {
            enable: 'چالاککردنی ئاگادارکردنەوەکان',
            enabled: 'ئاگادارکردنەوەکان چالاکن',
            currencyUpdate: 'نوێکردنەوەی دراو',
            ratesUpdated: 'نرخەکانی دراو نوێکرانەوە!'
        },
        language: {
            select: 'زمان هەڵبژێرە',
            change: 'گۆڕینی زمان',
            confirm: 'دڵنیاکردنەوە',
            cancel: 'هەڵوەشاندنەوە',
            changed: 'زمان گۆڕدرا بۆ کوردی'
        },
        toggleMode: 'گۆڕینی دۆخی',
        to: 'بۆ',
        from: 'لە',
        selectCurrency: 'دراو هەڵبژێرە',
        error: {
            fetchFailed: 'نەتوانرا داتای دراو بهێنرێت',
            networkError: 'هەڵەی تۆڕ ڕوویدا',
            tryAgain: 'تکایە دووبارە هەوڵ بدەوە'
        }
    },
    ar: {
        title: 'أسعار صرف العملات',
        currency: 'العملة',
        code: 'الرمز',
        sale: 'البيع',
        buy: 'الشراء',
        close: 'إغلاق',
        refresh: 'تحديث البيانات',
        lightMode: 'الوضع النهاري',
        darkMode: 'الوضع الليلي',
        calculate: 'حساب',
        clear: 'مسح',
        amount: 'المبلغ',
        result: 'النتيجة',
        dinar: 'الدينار العراقي',
        operations: 'العمليات',
        add: 'جمع',
        subtract: 'طرح',
        multiply: 'ضرب',
        divide: 'قسمة',
        cancel: 'إلغاء',
        secondAmount: 'المبلغ الثاني',
        clearAll: 'مسح الكل',
        weather: {
            loading: 'جاري تحميل الطقس...',
            tap_refresh: 'انقر للتحديث',
            erbil: 'أربيل، العراق',
            condition: {
                sunny: 'مشمس',
                cloudy: 'غائم',
                rainy: 'ممطر',
                stormy: 'عاصف',
                snowy: 'ثلجي'
            }
        },
        lastUpdated: 'آخر تحديث',
        loading: 'جاري تحميل العملات...',
        notifications: {
            enable: 'تفعيل الإشعارات',
            enabled: 'الإشعارات مفعلة',
            currencyUpdate: 'تحديث العملات',
            ratesUpdated: 'تم تحديث أسعار الصرف!'
        },
        language: {
            select: 'اختر اللغة',
            change: 'تغيير اللغة',
            confirm: 'تأكيد',
            cancel: 'إلغاء',
            changed: 'تم تغيير اللغة إلى العربية'
        },
        toggleMode: 'تبديل الوضع',
        to: 'إلى',
        from: 'من',
        selectCurrency: 'اختر العملة',
        error: {
            fetchFailed: 'فشل في جلب بيانات العملة',
            networkError: 'حدث خطأ في الشبكة',
            tryAgain: 'يرجى المحاولة مرة أخرى'
        }
    }
};

// Computed for current translation
const t = computed(() => translations[currentLang.value]);


// Weather icons and colors
const weatherIcons = {
    sunny: 'wb_sunny',
    cloudy: 'cloud',
    rainy: 'water_drop',
    stormy: 'thunderstorm',
    snowy: 'ac_unit',
    default: 'wb_cloudy'
};
const weatherColors = {
    sunny: 'yellow-8',
    cloudy: 'grey-6',
    rainy: 'blue-6',
    stormy: 'deep-purple-6',
    snowy: 'light-blue-2',
    default: 'grey-7'
};

// Dark mode
const darkMode = ref(false);
const systemPrefersDark = ref(false);

// Apply theme based on preference
const applyTheme = (isDark) => {
    darkMode.value = isDark;

    // Apply class to document body
    document.documentElement.classList.toggle('dark-theme', isDark);

    // For immediate effect, also toggle class on body
    document.body.classList.toggle('dark-theme', isDark);

    // Save to localStorage for persistence
    localStorage.setItem('darkMode', isDark.toString());

    console.log('Dark mode set to:', isDark);
};

const toggleDarkMode = () => {
    console.log('Toggle dark mode called with:', darkMode.value);
    applyTheme(darkMode.value);
};

// Currency methods
const formatCurrency = (value) => {
    if (value === undefined || value === null) return '0.00';
    return Number(value).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const refreshWeather = () => {
    // Implement weather refresh logic
    weatherData.value.loading = true;
    weatherData.value.error = null;
    // Simulated weather fetch
    setTimeout(() => {
        weatherData.value = {
            loading: false,
            temperature: 25,
            location: t.value.weather.erbil,
            condition: 'sunny',
            error: null
        };
    }, 1500);
};

// Data refresh
const refreshData = async (retryCount = 0) => {
    try {
        loading.value = true;
        currencies.value = [];

        // Fetch currencies and rates from backend - try new API first, fallback to old
        let currenciesResponse, ratesResponse;

        try {
            // Try new public API endpoint first
            const [newCurrenciesResponse, newRatesResponse] = await Promise.all([
                axios.get('/api/public/currencies', {
                    headers: { 'Accept-Language': currentLang.value }
                }),
                axios.get('/api/exchange/rates', {
                    headers: { 'Accept-Language': currentLang.value }
                })
            ]);
            currenciesResponse = newCurrenciesResponse;
            ratesResponse = newRatesResponse;
        } catch (error) {
            console.warn('New API failed, falling back to old API:', error);
            // Fallback to old API
            const [oldCurrenciesResponse, oldRatesResponse] = await Promise.all([
                axios.get('/api/exchange/currencies', {
                    headers: { 'Accept-Language': currentLang.value }
                }),
                axios.get('/api/exchange/rates', {
                    headers: { 'Accept-Language': currentLang.value }
                })
            ]);
            currenciesResponse = oldCurrenciesResponse;
            ratesResponse = oldRatesResponse;
        }

        console.log('Raw API responses:', { currencies: currenciesResponse.data, rates: ratesResponse.data });

        if (currenciesResponse.data.success && ratesResponse.data.success) {
            const currenciesData = currenciesResponse.data.data;
            const ratesData = ratesResponse.data.data;

            // Update last updated timestamp
            if (ratesResponse.data.timestamp) {
                lastUpdated.value = new Date(ratesResponse.data.timestamp);
            }

            // Validate data arrays
            if (!Array.isArray(currenciesData)) {
                console.error('Currencies data is not an array:', currenciesData);
                throw new Error('Invalid currencies data format');
            }

            if (!Array.isArray(ratesData)) {
                console.error('Rates data is not an array:', ratesData);
                throw new Error('Invalid rates data format');
            }

            console.log(`Processing ${currenciesData.length} currencies and ${ratesData.length} rates`);

            // Transform the data to match the expected format
            const transformedCurrencies = currenciesData
                .filter(currency => {
                    // Only include active currencies that are not IQD (base currency)
                    const isValid = currency &&
                                   currency.is_active === true &&
                                   currency.code &&
                                   currency.code !== 'IQD';

                    if (!isValid && currency) {
                        console.log(`Filtering out currency: ${currency.code}, active: ${currency.is_active}`);
                    }

                    return isValid;
                })
                .map(currency => {
                    // Find rates for this currency (both buy and sell)
                    // Look for direct rates to/from IQD
                    const buyRate = ratesData.find(rate =>
                        rate.from_currency?.code === currency.code &&
                        rate.to_currency?.code === 'IQD' &&
                        rate.is_active === true
                    );

                    const sellRate = ratesData.find(rate =>
                        rate.from_currency?.code === 'IQD' &&
                        rate.to_currency?.code === currency.code &&
                        rate.is_active === true
                    );

                    // Also look for rates via USD as intermediary
                    const usdToThisCurrency = ratesData.find(rate =>
                        rate.from_currency?.code === 'USD' &&
                        rate.to_currency?.code === currency.code &&
                        rate.is_active === true
                    );

                    const thisCurrencyToUsd = ratesData.find(rate =>
                        rate.from_currency?.code === currency.code &&
                        rate.to_currency?.code === 'USD' &&
                        rate.is_active === true
                    );

                    const usdToIqd = ratesData.find(rate =>
                        rate.from_currency?.code === 'USD' &&
                        rate.to_currency?.code === 'IQD' &&
                        rate.is_active === true
                    );

                    const iqdToUsd = ratesData.find(rate =>
                        rate.from_currency?.code === 'IQD' &&
                        rate.to_currency?.code === 'USD' &&
                        rate.is_active === true
                    );

                    // Calculate buy and sell rates
                    let buyPrice = 0;
                    let sellPrice = 0;

                    // Priority 1: Direct rates to/from IQD
                    if (buyRate) {
                        buyPrice = parseFloat(buyRate.rate);
                    } else if (sellRate) {
                        buyPrice = 1 / parseFloat(sellRate.rate);
                    }
                    // Priority 2: Calculate via USD
                    else if (thisCurrencyToUsd && usdToIqd) {
                        buyPrice = parseFloat(thisCurrencyToUsd.rate) * parseFloat(usdToIqd.rate);
                    } else if (usdToThisCurrency && usdToIqd) {
                        buyPrice = parseFloat(usdToIqd.rate) / parseFloat(usdToThisCurrency.rate);
                    }

                    if (sellRate) {
                        sellPrice = 1 / parseFloat(sellRate.rate);
                    } else if (buyRate) {
                        sellPrice = parseFloat(buyRate.rate);
                    }
                    // Priority 2: Calculate via USD
                    else if (usdToThisCurrency && iqdToUsd) {
                        sellPrice = parseFloat(iqdToUsd.rate) / parseFloat(usdToThisCurrency.rate);
                    } else if (thisCurrencyToUsd && iqdToUsd) {
                        sellPrice = parseFloat(iqdToUsd.rate) * parseFloat(thisCurrencyToUsd.rate);
                    }

                    // Log rate information for debugging
                    console.log(`Currency ${currency.code}: buyPrice=${buyPrice}, sellPrice=${sellPrice}`);
                    if (buyPrice === 0 && sellPrice === 0) {
                        console.warn(`No rates found for currency: ${currency.code}`);
                    }

                    // Fix flag codes - ensure proper country code format
                    let flagCode = currency.flag_icon || currency.code?.toLowerCase() || 'usd';
                    // Fix for Iraq flag (iq instead of IQ)
                    if (flagCode === 'iq' || flagCode === 'IQ') {
                        flagCode = 'iq';
                    }

                    // Format the currency object
                    return {
                        id: currency.id || Math.random(),
                        name: currency.name || currency.code || 'Unknown Currency',
                        code: flagCode,
                        currency_code: currency.code || 'N/A',
                        symbol: currency.symbol || '',
                        country: currency.country || '',
                        buy: buyPrice || 0,
                        sale: sellPrice || 0,
                        buy_status: buyRate?.status || 'stable',
                        sale_status: sellRate?.status || 'stable',
                        decimal_places: currency.decimal_places || 2,
                        min_amount: currency.min_amount || 0,
                        max_amount: currency.max_amount || 999999999,
                        last_updated: buyRate?.updated_at || sellRate?.updated_at
                    };
                })
                .filter(currency => {
                    // Include currencies with at least one valid rate
                    const hasValidRates = currency.buy > 0 || currency.sale > 0;
                    if (!hasValidRates) {
                        console.warn(`Filtering out ${currency.currency_code} - no valid rates (buy: ${currency.buy}, sale: ${currency.sale})`);
                    }
                    return hasValidRates;
                });

            // Debug the transformed currencies
            console.log(`Successfully processed ${transformedCurrencies.length} currencies`);

            // Sort currencies alphabetically by name - with proper null/undefined check
            transformedCurrencies.sort((a, b) => {
                // Handle cases where name might be null, undefined or not a string
                const nameA = typeof a.name === 'string' ? a.name : (a.currency_code || 'Unknown');
                const nameB = typeof b.name === 'string' ? b.name : (b.currency_code || 'Unknown');
                return nameA.localeCompare(nameB);
            });

            // Update the currencies list
            currencies.value = transformedCurrencies;

            // Show success notification if this is a manual refresh and we have currencies
            if (window.Quasar && window.Quasar.Notify && currencies.value.length > 0) {
                window.Quasar.Notify.create({
                    message: t.value.refresh || 'Data refreshed successfully',
                    color: 'positive',
                    position: 'top',
                    timeout: 2000,
                    icon: 'refresh'
                });
            }
        } else {
            console.error('API returned error:', {
                currencies: currenciesResponse.data,
                rates: ratesResponse.data
            });
            throw new Error('Failed to fetch currency data');
        }

        // Refresh weather data
        refreshWeather();
    } catch (error) {
        console.error('Error fetching currency data:', error);

        // Implement retry logic (max 3 retries)
        if (retryCount < 3) {
            console.log(`Retrying data fetch (${retryCount + 1}/3)...`);
            setTimeout(() => {
                refreshData(retryCount + 1);
            }, 1000 * (retryCount + 1)); // Exponential backoff
            return;
        }

        // Show error notification
        if (window.Quasar && window.Quasar.Notify) {
            window.Quasar.Notify.create({
                message: t.value.error?.fetchFailed || 'Failed to fetch currency data',
                color: 'negative',
                position: 'top',
                timeout: 3000,
                icon: 'error'
            });
        }

        // Fallback to empty array
        currencies.value = [];
        refreshWeather();
    } finally {
        if (loading.value) {
            loading.value = false;
        }
    }
};

// Service Worker Registration
const registerServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
        try {
            console.log('[PWA] Attempting to register service worker');

            // First check if we're on iOS
            if (isIOS.value) {
                console.log('[PWA] iOS detected, using special handling');

                // For iOS, we need to be more careful about service worker registration
                // as it has limited support in Safari

                if (isPWA.value) {
                    console.log('[PWA] Running as PWA on iOS');

                    // When running as PWA on iOS, we can try to register the service worker
                    // but we need to handle notifications differently
                    const registration = await navigator.serviceWorker.register('/service-worker.js', {
                        scope: '/',
                        updateViaCache: 'none' // Don't use cache for updates
                    });

                    swRegistration.value = registration;
                    console.log('[PWA] Service Worker registered on iOS PWA with scope:', registration.scope);

                    // Set up iOS-specific notification handling
                    setupIOSPWA();
                } else {
                    console.log('[PWA] Running in browser on iOS, limited service worker support');

                    // In Safari browser (not PWA), service worker support is limited
                    // We can still register it but won't rely on it for notifications
                    navigator.serviceWorker.register('/service-worker.js', { scope: '/' })
                        .then(registration => {
                            swRegistration.value = registration;
                            console.log('[PWA] Service Worker registered in iOS browser with scope:', registration.scope);
                        })
                        .catch(error => {
                            console.warn('[PWA] Service Worker registration failed in iOS browser:', error);
                        });
                }
            } else {
                // For non-iOS devices, normal service worker registration
                const registration = await navigator.serviceWorker.register('/service-worker.js', { scope: '/' });
                swRegistration.value = registration;
                console.log('[PWA] Service Worker registered with scope:', registration.scope);

                // Check if push is supported
                checkPushSupport();
            }
        } catch (error) {
            console.error('[PWA] Service Worker registration failed:', error);
        }
    } else {
        console.log('[PWA] Service Workers not supported in this browser');
    }
};

// Check if push is supported
const checkPushSupport = () => {
    // On iOS, push notifications are not fully supported in PWAs
    // We'll use our custom notification system instead
    if (isIOS.value) {
        console.log('[PWA] iOS detected, using custom notification system');
        pushSupported.value = false;
        return;
    }

    // For other platforms, check standard push support
    const standardPushSupported = 'PushManager' in window;
    const notificationsSupported = 'Notification' in window;

    pushSupported.value = standardPushSupported && notificationsSupported;
    console.log('[PWA] Standard push support:', pushSupported.value);

    if (pushSupported.value && swRegistration.value) {
        // Check current subscription status
        swRegistration.value.pushManager.getSubscription()
            .then(subscription => {
                if (subscription) {
                    console.log('[PWA] User is subscribed to push notifications');
                    pushPermission.value = Notification.permission;
                } else {
                    console.log('[PWA] User is NOT subscribed to push notifications');

                    // Check if permission was previously granted
                    if (Notification.permission === 'granted') {
                        pushPermission.value = 'granted';
                        // Auto-subscribe if permission was already granted
                        subscribeToPush();
                    } else {
                        pushPermission.value = Notification.permission;
                    }
                }
            })
            .catch(error => {
                console.error('[PWA] Error checking push subscription:', error);
            });
    }
};


// Subscribe to push notifications
const subscribeToPush = async () => {
    if (!swRegistration.value) return;

    try {
        const subscription = await swRegistration.value.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: urlBase64ToUint8Array(
                'BEl62iUYgUivxIkv69yViEuiBIa-Ib9-SkvMeAtA3LFgDzkrxZJjSgSnfckjBJuBkr3qBUYIHBQFLXYp5Nksh8U'
            )
        });

        console.log('User is subscribed:', subscription);

        // Here you would typically send the subscription to your server
        // sendSubscriptionToServer(subscription);
    } catch (error) {
        console.error('Failed to subscribe the user:', error);
    }
};

// Helper function to convert base64 to Uint8Array for VAPID key
const urlBase64ToUint8Array = (base64String) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
        .replace(/-/g, '+')
        .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
};

// Setup for iOS PWA specific features
const setupIOSPWA = () => {
    // iOS PWA doesn't fully support service workers and push notifications
    // We need to use a workaround with localStorage and periodic checks

    console.log('[PWA] Setting up iOS PWA environment');

    // Check if we need to show any pending notifications from IndexedDB
    checkPendingNotificationsFromIndexedDB();

    // Listen for service worker messages
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
        navigator.serviceWorker.addEventListener('message', (event) => {
            console.log('[PWA] Received message from service worker:', event.data);

            if (event.data && event.data.type === 'NOTIFICATION_SHOWN') {
                // Show the notification using our custom UI
                showIOSNotification(event.data.notification);
            }
        });
    }

    // Set up periodic checks for new notifications
    setInterval(() => {
        // This would typically check your server for new notifications
        checkForNewNotifications();
    }, 60000); // Check every minute
};

// Check for pending notifications in IndexedDB
const checkPendingNotificationsFromIndexedDB = () => {
    if (!window.indexedDB) return;

    const request = indexedDB.open('notifications-store', 1);

    request.onerror = function(event) {
        console.error('[PWA] IndexedDB error:', event.target.errorCode);
    };

    request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction(['notifications'], 'readwrite');
        const store = transaction.objectStore('notifications');
        const getAllRequest = store.getAll();

        getAllRequest.onsuccess = function() {
            const notifications = getAllRequest.result;
            if (notifications && notifications.length > 0) {
                console.log('[PWA] Found pending notifications:', notifications);

                // Sort by timestamp (newest first)
                notifications.sort((a, b) => b.timestamp - a.timestamp);

                // Show the most recent notification
                showIOSNotification(notifications[0]);

                // Clear the notifications we've processed
                const clearTransaction = db.transaction(['notifications'], 'readwrite');
                const clearStore = clearTransaction.objectStore('notifications');
                clearStore.clear();
            }
        };
    };

    request.onupgradeneeded = function(event) {
        const db = event.target.result;
        if (!db.objectStoreNames.contains('notifications')) {
            db.createObjectStore('notifications', { keyPath: 'timestamp' });
        }
    };
};

// Check for new notifications (would typically be an API call)
const checkForNewNotifications = async () => {
    if (!isIOS.value || !isPWA.value) return;

    console.log('[PWA] Checking for new notifications');

    // This is where you would make an API call to check for new notifications
    // For demo purposes, we'll simulate a notification every 5 minutes
    const lastCheck = localStorage.getItem('lastNotificationCheck') || 0;
    const now = Date.now();

    if (now - parseInt(lastCheck) > 300000) { // 5 minutes
        console.log('[PWA] Simulating a new notification');

        // Simulate a new notification
        const newNotification = {
            title: t.value.notifications.currencyUpdate,
            body: t.value.notifications.ratesUpdated,
            timestamp: now
        };

        // Store the last check time
        localStorage.setItem('lastNotificationCheck', now.toString());

        // Show the notification
        showIOSNotification(newNotification);

        // Also check if we have a service worker to communicate with
        if (swRegistration.value && navigator.serviceWorker.controller) {
            // Store in IndexedDB via service worker
            navigator.serviceWorker.controller.postMessage({
                type: 'STORE_NOTIFICATION',
                notification: newNotification
            });
        }
    }
};

// Show a custom notification UI for iOS PWA
const showIOSNotification = (notification) => {
    console.log('[PWA] Showing iOS notification:', notification);

    // Create a custom notification UI element
    const notifEl = document.createElement('div');
    notifEl.className = 'ios-notification';
    notifEl.setAttribute('role', 'alert');
    notifEl.setAttribute('aria-live', 'assertive');

    // Add content
    notifEl.innerHTML = `
    <div class="ios-notification-content">
      <div class="ios-notification-title">${notification.title}</div>
      <div class="ios-notification-body">${notification.body}</div>
    </div>
    <div class="ios-notification-close" aria-label="Close">×</div>
  `;

    // Style the notification
    Object.assign(notifEl.style, {
        position: 'fixed',
        top: '10px',
        left: '50%',
        width: '90%',
        maxWidth: '400px',
        backgroundColor: darkMode.value ? '#1e1e1e' : '#ffffff',
        color: darkMode.value ? '#ffffff' : '#0f172a',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        padding: '12px 16px',
        zIndex: '9999',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        transition: 'all 0.3s ease',
        opacity: '0',
        transform: 'translateX(-50%) translateY(-20px)',
        border: darkMode.value ? '1px solid #333' : '1px solid #e2e8f0'
    });

    // Add sound effect for notification
    const notificationSound = new Audio('/notification-sound.mp3');
    notificationSound.volume = 0.5;

    try {
        notificationSound.play().catch(e => {
            console.log('[PWA] Could not play notification sound:', e);
        });
    } catch (e) {
        console.log('[PWA] Error playing notification sound:', e);
    }

    // Add vibration if supported
    if ('vibrate' in navigator) {
        try {
            navigator.vibrate([100, 50, 100]);
        } catch (e) {
            console.log('[PWA] Vibration not supported or not allowed');
        }
    }

    // Add to DOM
    document.body.appendChild(notifEl);

    // Trigger animation
    setTimeout(() => {
        notifEl.style.opacity = '1';
        notifEl.style.transform = 'translateX(-50%) translateY(0)';
    }, 10);

    // Add click handler to close
    const closeBtn = notifEl.querySelector('.ios-notification-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            dismissNotification(notifEl);
        });
    }

    // Make the entire notification clickable
    notifEl.addEventListener('click', (event) => {
        if (!event.target.classList.contains('ios-notification-close')) {
            // Handle notification click - could navigate to a specific page
            console.log('[PWA] Notification clicked');
            dismissNotification(notifEl);
        }
    });

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        dismissNotification(notifEl);
    }, 5000);
};

// Helper function to dismiss notification with animation
const dismissNotification = (element) => {
    if (!document.body.contains(element)) return;

    element.style.opacity = '0';
    element.style.transform = 'translateX(-50%) translateY(-20px)';

    setTimeout(() => {
        if (document.body.contains(element)) {
            document.body.removeChild(element);
        }
    }, 300);
};

// Detect if running as PWA and on iOS
const detectEnvironment = () => {
    // Check if running on iOS - multiple methods for better detection
    const ua = window.navigator.userAgent;
    const iOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
    const iOSWebkit = /WebKit/.test(ua) && /Mobile/.test(ua) && !/(Chrome|Android)/.test(ua);
    const iOSSafari = /Safari/.test(ua) && /Apple/.test(ua) && !/(Chrome|Android)/.test(ua);
    const iOSPlatform = navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform);

    isIOS.value = iOS || iOSWebkit || iOSSafari || iOSPlatform;

    // Check if running as PWA (installed to home screen) - multiple methods
    // Method 1: display-mode media query
    const displayModeStandalone = window.matchMedia('(display-mode: standalone)').matches;

    // Method 2: navigator.standalone (iOS Safari specific)
    const navigatorStandalone = window.navigator.standalone === true;

    // Method 3: Check for PWA context
    const isPWAContext = document.referrer.startsWith('android-app://') ||
        navigator.userAgent.includes(' wv') ||
        window.matchMedia('(display-mode: standalone)').matches;

    // Method 4: Check for launch_handler client_mode in manifest
    const fromManifest = document.referrer === '' &&
        window.location.search.includes('source=pwa');

    isPWA.value = displayModeStandalone || navigatorStandalone || isPWAContext || fromManifest;

    // Store in localStorage for debugging
    localStorage.setItem('isIOS', isIOS.value);
    localStorage.setItem('isPWA', isPWA.value);
    localStorage.setItem('userAgent', ua);
    localStorage.setItem('navigatorStandalone', navigatorStandalone);

    console.log('[PWA] Environment detection:');
    console.log('[PWA] User Agent:', ua);
    console.log('[PWA] Running on iOS:', isIOS.value);
    console.log('[PWA] Running as PWA:', isPWA.value);
    console.log('[PWA] display-mode: standalone:', displayModeStandalone);
    console.log('[PWA] navigator.standalone:', navigatorStandalone);
    console.log('[PWA] PWA context detection:', isPWAContext);
    console.log('[PWA] From manifest launch:', fromManifest);

    // Add class to body for CSS targeting
    if (isIOS.value) {
        document.body.classList.add('ios-device');
    }

    if (isPWA.value) {
        document.body.classList.add('pwa-mode');
    }

    if (isIOS.value && isPWA.value) {
        document.body.classList.add('ios-pwa');

        // Set a special meta tag for iOS PWA
        const metaTag = document.createElement('meta');
        metaTag.name = 'apple-mobile-web-app-status-bar-style';
        metaTag.content = 'black-translucent';
        document.head.appendChild(metaTag);
    }
};

// Fix iOS PWA logo and splash screen issues
const fixIOSPWAIssues = () => {
    if (!isIOS.value || !isPWA.value) return;

    console.log('[PWA] Applying iOS PWA fixes');

    // Force logo preload
    const preloadLogo = new Image();
    preloadLogo.src = '/logo.png';

    // Force apple-touch-icon preload
    const preloadAppleIcon = new Image();
    preloadAppleIcon.src = '/apple-touch-icon.png';

    const width = window.screen.width;
    const height = window.screen.height;
    const devicePixelRatio = window.devicePixelRatio || 1;

    console.log(`[PWA] Device: ${width}x${height} @${devicePixelRatio}x`);

    // Determine which splash screen to use
    let splashUrl = '';

    // iPad Pro 12.9"
    if (width === 1024 && height === 1366 && devicePixelRatio === 2) {
        splashUrl = '/splash/apple-splash-2048-2732.png';
    }
    // iPad Pro 11"
    else if (width === 834 && height === 1194 && devicePixelRatio === 2) {
        splashUrl = '/splash/apple-splash-1668-2388.png';
    }
    // iPad 10.2"
    else if (width === 768 && height === 1024 && devicePixelRatio === 2) {
        splashUrl = '/splash/apple-splash-1536-2048.png';
    }
    // iPhone 11 Pro Max, XS Max
    else if (width === 414 && height === 896 && devicePixelRatio === 3) {
        splashUrl = '/splash/apple-splash-1242-2688.png';
    }
    // iPhone X, XS, 11 Pro
    else if (width === 375 && height === 812 && devicePixelRatio === 3) {
        splashUrl = '/splash/apple-splash-1125-2436.png';
    }
    // iPhone XR, 11
    else if (width === 414 && height === 896 && devicePixelRatio === 2) {
        splashUrl = '/splash/apple-splash-828-1792.png';
    }
    // iPhone 8, 7, 6s, 6
    else if (width === 375 && height === 667 && devicePixelRatio === 2) {
        splashUrl = '/splash/apple-splash-750-1334.png';
    }
    // iPhone SE
    else if (width === 320 && height === 568 && devicePixelRatio === 2) {
        splashUrl = '/splash/apple-splash-640-1136.png';
    }
    // Fallback
    else {
        splashUrl = '/splash/apple-splash-750-1334.png'; // Most common size as fallback
    }

    console.log(`[PWA] Preloading splash screen: ${splashUrl}`);

    // Preload the appropriate splash screen
    const preloadSplash = new Image();
    preloadSplash.src = splashUrl;

    // Add meta tag to ensure proper icon display
    const linkIconFix = document.createElement('link');
    linkIconFix.rel = 'apple-touch-icon-precomposed';
    linkIconFix.href = '/apple-touch-icon.png';
    document.head.appendChild(linkIconFix);

    // Add a second apple-touch-icon link with sizes attribute
    const linkIconSizes = document.createElement('link');
    linkIconSizes.rel = 'apple-touch-icon';
    linkIconSizes.href = '/apple-touch-icon.png';
    linkIconSizes.sizes = '180x180';
    document.head.appendChild(linkIconSizes);

    // Add a second manifest link to force reload
    const linkManifest = document.createElement('link');
    linkManifest.rel = 'manifest';
    linkManifest.href = '/manifest.json?v=' + Date.now();
    document.head.appendChild(linkManifest);

    // Use service worker to cache all the assets needed for iOS PWA
    if (navigator.serviceWorker && navigator.serviceWorker.controller) {
        console.log('[PWA] Asking service worker to cache iOS PWA assets');

        const messageChannel = new MessageChannel();
        messageChannel.port1.onmessage = (event) => {
            if (event.data && event.data.type === 'IOS_PWA_FIX_APPLIED') {
                console.log('[PWA] Service worker applied iOS PWA fixes');
            }
        };

        navigator.serviceWorker.controller.postMessage({
            type: 'FIX_IOS_PWA'
        }, [messageChannel.port2]);
    }

    // Create a dynamic splash screen element
    const createDynamicSplashScreen = () => {
        const splash = document.createElement('div');
        splash.id = 'ios-dynamic-splash';
        splash.style.position = 'fixed';
        splash.style.top = '0';
        splash.style.left = '0';
        splash.style.width = '100vw';
        splash.style.height = '100vh';
        splash.style.backgroundColor = '#ffffff';
        splash.style.zIndex = '9999';
        splash.style.display = 'flex';
        splash.style.alignItems = 'center';
        splash.style.justifyContent = 'center';
        splash.style.transition = 'opacity 0.5s ease-out';

        // Add logo to splash screen
        const logo = document.createElement('img');
        logo.src = '/logo.png';
        logo.style.maxWidth = '40%';
        logo.style.maxHeight = '40%';

        splash.appendChild(logo);
        document.body.appendChild(splash);

        // Hide splash screen after a delay
        setTimeout(() => {
            splash.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(splash)) {
                    document.body.removeChild(splash);
                }
            }, 500);
        }, 1500);
    };

    // Show dynamic splash screen on first load
    if (sessionStorage.getItem('firstLoad') !== 'true') {
        createDynamicSplashScreen();
        sessionStorage.setItem('firstLoad', 'true');
    }
};

const getFlagPath = (flag) => {
    // For Kurdistan, we use the PNG format
    if (flag === 'kurdistan') {
        return `/flags/${flag}.png`;
    }

    // Map currency codes to country codes for flags
    const currencyToCountryMap = {
        'usd': 'us',  // Map USD currency to US country code
        'eur': 'eu',  // Euro to EU flag
        'gbp': 'gb',  // British Pound to Great Britain
        'jpy': 'jp',  // Japanese Yen to Japan
        'cad': 'ca',  // Canadian Dollar to Canada
        'aud': 'au',  // Australian Dollar to Australia
        'chf': 'ch',  // Swiss Franc to Switzerland
        'cny': 'cn',  // Chinese Yuan to China
        'inr': 'in',  // Indian Rupee to India
        'try': 'tr',  // Turkish Lira to Turkey
        'irr': 'ir',  // Iranian Rial to Iran
        'iqd': 'iq',  // Iraqi Dinar to Iraq
    };

    // Check if we need to map this currency code to a country code
    const countryCode = currencyToCountryMap[flag.toLowerCase()] || flag.toLowerCase();

    // For other flags, use SVG format
    return `/flags/${countryCode}.svg`;
};

// Calculator methods
const openCalculator = (currency) => {
    selectedCurrency.value = currency;
    showCalculator.value = true;
};

const closeCalculator = () => {
    showCalculator.value = false;
    selectedCurrency.value = null;
};

// Currency conversion using backend API
const convertCurrency = async (amount, fromCode, toCode) => {
    try {
        const response = await axios.get('/api/exchange/convert', {
            params: {
                from: fromCode,
                to: toCode,
                amount: amount
            }
        });

        if (response.data.success) {
            return {
                convertedAmount: response.data.data.converted_amount,
                rate: response.data.data.rate,
                lastUpdated: response.data.data.last_updated
            };
        } else {
            throw new Error('Conversion failed');
        }
    } catch (error) {
        console.error('Currency conversion error:', error);
        throw error;
    }
};

// Auto refresh functionality
const startAutoRefresh = () => {
    if (autoRefreshInterval.value) {
        clearInterval(autoRefreshInterval.value);
    }

    if (autoRefreshEnabled.value) {
        autoRefreshInterval.value = setInterval(() => {
            console.log('Auto-refreshing currency data...');
            refreshData();
        }, REFRESH_INTERVAL);
    }
};

const stopAutoRefresh = () => {
    if (autoRefreshInterval.value) {
        clearInterval(autoRefreshInterval.value);
        autoRefreshInterval.value = null;
    }
};

// Lifecycle hooks
onMounted(() => {
    // Initialize time and date
    updateDateTime();
    // Update time every second for smoother experience
    setInterval(updateDateTime, 1000);

    // Check system dark mode preference
    const savedDarkMode = localStorage.getItem('darkMode');

    // Apply initial theme immediately
    if (savedDarkMode !== null) {
        // Use saved preference if it exists
        applyTheme(savedDarkMode === 'true');
    } else {
        // Otherwise check system preference
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        systemPrefersDark.value = prefersDark;
        applyTheme(prefersDark);
    }

    // Listen for system theme changes
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = (e) => {
            // Only update if user hasn't set a preference
            if (localStorage.getItem('darkMode') === null) {
                systemPrefersDark.value = e.matches;
                applyTheme(e.matches);
            }
        };

        // Use the appropriate event listener method
        if (mediaQuery.addEventListener) {
            mediaQuery.addEventListener('change', handleChange);
        } else if (mediaQuery.addListener) {
            // For older browsers
            mediaQuery.addListener(handleChange);
        }
    }

    // Detect environment (PWA, iOS)
    detectEnvironment();

    // Apply iOS PWA fixes if needed
    fixIOSPWAIssues();

    // Register service worker
    registerServiceWorker();

    // Check auto-refresh preference
    const savedAutoRefresh = localStorage.getItem('autoRefreshEnabled');
    if (savedAutoRefresh !== null) {
        autoRefreshEnabled.value = savedAutoRefresh === 'true';
    }

    // Get data
    refreshData();

    // Start auto-refresh if enabled
    if (autoRefreshEnabled.value) {
        startAutoRefresh();
    }

    // Check first time launch
    checkFirstLaunch();
});

onUnmounted(() => {
    // Cleanup if needed
    clearInterval(updateDateTime);

    // Stop auto-refresh
    stopAutoRefresh();

    // Remove event listener
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        if (mediaQuery.removeEventListener) {
            mediaQuery.removeEventListener('change', () => {});
        } else if (mediaQuery.removeListener) {
            // For older browsers
            mediaQuery.removeListener(() => {});
        }
    }
});
</script>

<template>
    <q-page
        class="min-h-screen relative-position overflow-hidden responsive-padding"
        :style="{
      backgroundColor: darkMode ? 'var(--bg-primary)' : 'var(--bg-primary)',
      color: darkMode ? 'var(--text-primary)' : 'var(--text-primary)'
    }"
    >


        <PageHeader
            :t="t"
            :isRTL="currentLang === 'ku' || currentLang === 'ar'"
            :lastUpdated="lastUpdated ? lastUpdated.toLocaleString(currentLang === 'ku' ? 'ckb' : currentLang) : t.lastUpdated"
            v-model:darkMode="darkMode"
            :currentLang="currentLang"
            :languages="languages"
            :digitalTime="currentTime"
            :currentDate="currentDate"
            :toggleDarkMode="toggleDarkMode"
            :weatherData="weatherData"
            :weatherIcons="weatherIcons"
            :weatherColors="weatherColors"
            :refreshWeather="refreshWeather"
            :refreshData="refreshData"
            :loading="loading"
            @update:currentLang="selectLanguage($event)"
        />

        <!-- Language Selection Dialog -->
        <q-dialog v-model="showLangDialog" persistent transition-show="scale" transition-hide="scale">
            <q-card style="min-width: 350px; max-width: 90vw; border-radius: 16px" :class="{ 'dark-theme': darkMode }">
                <q-card-section class="text-center q-pb-none">
                    <div class="text-h5 q-py-sm">{{ t.language.select }}</div>
                </q-card-section>
                <q-card-section class="text-subtitle2 q-pt-none text-center">
                    زمان هەڵبژێرە / اختر اللغة / Select Language
                </q-card-section>

                <q-separator :dark="darkMode" />

                <q-card-section class="q-pt-md">
                    <div class="row q-col-gutter-md justify-center">
                        <div
                            v-for="lang in languages"
                            :key="lang.code"
                            class="col-4 text-center language-item"
                        >
                            <q-card
                                clickable
                                v-ripple
                                @click="selectLanguage(lang.code)"
                                :class="[
                  'flag-card',
                  currentLang === lang.code ? 'flag-card-selected' : '',
                  darkMode ? 'dark-flag-card' : ''
                ]"
                            >
                                <q-card-section class="q-pa-sm">
                                    <div class="q-mb-sm flex justify-center">
                                        <CountryFlag
                                            :code="lang.flag"
                                            :country="lang.name"
                                            :flag-icon="lang.flag"
                                            size="xl"
                                            :shadow="true"
                                            :rounded="false"
                                        />
                                    </div>
                                    <div class="text-weight-bold">{{ lang.name }}</div>
                                    <q-icon
                                        v-if="currentLang === lang.code"
                                        name="check_circle"
                                        color="primary"
                                        size="24px"
                                        class="absolute-bottom-right q-ma-xs"
                                    />
                                </q-card-section>
                            </q-card>
                        </div>
                    </div>
                </q-card-section>

                <q-card-actions align="center" class="q-px-md q-pb-md">
                    <q-btn
                        color="primary"
                        :label="t.language.confirm"
                        @click="selectLanguage(currentLang)"
                        :ripple="false"
                        rounded
                        class="q-px-xl"
                    />
                </q-card-actions>
            </q-card>
        </q-dialog>

        <div class="relative-position q-mx-auto q-mt-md" style="z-index: 1; max-width: 1200px;">
            <div class="row q-col-gutter-md">
                <div class="col-12">
                    <transition appear enter-active-class="animated fadeIn">
                        <div v-if="currencies.length > 0 && !loading">
                            <CurrencyList
                                :currencies="currencies"
                                :t="t"
                                :formatCurrency="formatCurrency"
                                :darkMode="darkMode"
                                @open-calculator="openCalculator"
                            />
                        </div>
                        <div v-else-if="loading" class="flex flex-center" style="min-height: 400px">
                            <div class="text-center">
                            <q-spinner-dots color="primary" size="4em" />
                                <div class="q-mt-md text-subtitle1">{{ t.loading || 'Loading currencies...' }}</div>
                            </div>
                        </div>
                        <div v-else class="flex flex-center" style="min-height: 400px">
                            <div class="text-center">
                                <q-icon name="error_outline" size="4em" color="grey-5" />
                                <div class="q-mt-md text-subtitle1">{{ t.error?.fetchFailed || 'No currency data available' }}</div>
                                <q-btn
                                    :label="t.error?.tryAgain || 'Try Again'"
                                    color="primary"
                                    @click="refreshData"
                                    class="q-mt-md"
                                    :loading="loading"
                                />
                            </div>
                        </div>
                    </transition>
                </div>
            </div>
        </div>

        <CurrencyCalculator
            v-model:show="showCalculator"
            :currency="selectedCurrency"
            :currencies="currencies"
            :darkMode="darkMode"
            :t="t"
            :formatCurrency="formatCurrency"
            :convertCurrency="convertCurrency"
            @close="closeCalculator"
            @currency-changed="selectedCurrency = $event"
        />

    </q-page>
</template>

<style>
.ios-notification-content {
    flex: 1;
}
.ios-notification-title {
    font-weight: bold;
    margin-bottom: 4px;
}
.ios-notification-body {
    font-size: 0.9rem;
}
.ios-notification-close {
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    padding: 0 8px;
}
:root {
    --transition-speed: 0.3s;

    /* Default light theme variables */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --header-bg: #ffffff;
    --header-border: rgba(37, 99, 235, 0.25);
    --card-border: rgba(37, 99, 235, 0.15);
}

/* Dark theme variables - defined at root level for immediate access */
.dark-theme {
    --bg-primary: #121212 !important;
    --bg-secondary: #1e1e1e !important;
    --text-primary: #ffffff !important;
    --text-secondary: #b0b0b0 !important;
    --border-color: #2c2c2c !important;
    --card-bg: #1e1e1e !important;
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
    --primary-color: #3b82f6 !important;
    --primary-light: #60a5fa !important;
    --header-bg: #121212 !important;
    --header-border: rgba(66, 133, 244, 0.25) !important;
    --card-border: rgba(66, 133, 244, 0.2) !important;
}

body {
    transition: background-color var(--transition-speed) ease,
    color var(--transition-speed) ease;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

body.dark-theme {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Apply theme transitions to components */
.q-card, .q-table, .q-item, .q-field__control {
    transition: background-color var(--transition-speed) ease,
    border-color var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
}

/* Theme-specific gradient backgrounds */
.bg-gradient {
    background: linear-gradient(120deg, rgba(37,99,235,0.16) 0%, rgba(37,99,235,0.09) 100%);
    top: 0;
    height: 320px;
    filter: blur(1px);
    transition: background var(--transition-speed) ease;
    z-index: 0;
}

.dark-gradient {
    background: linear-gradient(120deg, rgba(37,99,235,0.08) 0%, rgba(37,99,235,0.04) 100%);
}

.overlay-gradient {
    background: linear-gradient(to top, rgba(37,99,235,0.09), transparent);
    bottom: 0;
    height: 220px;
    filter: blur(1px);
    transition: background var(--transition-speed) ease;
    z-index: 0;
}

.dark-overlay-gradient {
    background: linear-gradient(to top, rgba(37,99,235,0.05), transparent);
}

.animated {
    animation-duration: 0.5s;
    animation-fill-mode: both;
}

.fadeIn {
    animation-name: fadeIn;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Elevation adjustments for dark mode */
.dark-theme .q-card {
    background-color: var(--card-bg);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

/* Dark mode improvements */
.dark-theme .q-item {
    color: var(--text-primary);
}

.dark-theme .q-field__native,
.dark-theme .q-field__prefix,
.dark-theme .q-field__suffix,
.dark-theme .q-field__input {
    color: var(--text-primary);
}

.dark-theme .q-field__label {
    color: var(--text-secondary);
}

.dark-theme .q-table__card {
    color: var(--text-primary);
    background-color: var(--card-bg);
}

.dark-theme .q-table__container {
    background-color: var(--card-bg);
}

.dark-theme .q-table th {
    color: var(--text-secondary);
    background-color: var(--bg-secondary);
}

.dark-theme .q-table td {
    color: var(--text-primary);
}

.dark-theme .q-table tbody tr:hover {
    background-color: rgba(66, 133, 244, 0.1);
}

/* Custom scrollbar for dark mode */
.dark-theme::-webkit-scrollbar {
    width: 10px;
}

.dark-theme::-webkit-scrollbar-track {
    background: #1e1e1e;
}

.dark-theme::-webkit-scrollbar-thumb {
    background: #3e3e3e;
    border-radius: 5px;
}

.dark-theme::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Force dark mode for testing */
.force-dark {
    background-color: #121212 !important;
    color: #ffffff !important;
}

/* Force light mode for testing */
.force-light {
    background-color: #f8fafc !important;
    color: #0f172a !important;
}

/* Language selection flag cards */
.flag-card {
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    will-change: transform, box-shadow;
    backface-visibility: hidden;
    -webkit-font-smoothing: subpixel-antialiased;
}

.flag-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.flag-card:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.flag-card-selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.flag-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,0.03) 100%);
    pointer-events: none;
}

.dark-flag-card {
    background-color: var(--bg-secondary);
}

.dark-flag-card::after {
    background: linear-gradient(to bottom, rgba(255,255,255,0.03) 0%, rgba(255,255,255,0) 100%);
}

.dark-flag-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.dark-theme .flag-card-selected {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.4);
}

/* Add smooth reveal animation for languages */
@keyframes smoothReveal {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.language-item {
    animation: smoothReveal 0.4s ease forwards;
}

.language-item:nth-child(2) {
    animation-delay: 0.1s;
}

.language-item:nth-child(3) {
    animation-delay: 0.2s;
}

/* Responsive padding for mobile */
.responsive-padding {
    padding: 16px;
}

@media (min-width: 601px) {
    .responsive-padding {
        padding: 24px;
    }
}

@media (min-width: 1024px) {
    .responsive-padding {
        padding: 32px;
    }
}

/* Mobile optimizations */
@media (max-width: 600px) {
    .q-dialog__inner > div {
        max-width: 95vw !important;
    }

    /* Optimize card spacing on mobile */
    .flag-card {
        margin-bottom: 16px;
    }

    /* Optimize touch targets for mobile */
    .q-btn {
        min-height: 42px;
        padding: 8px 16px;
    }

    /* Better table display on mobile */
    .q-table th, .q-table td {
        padding: 12px 8px;
    }

    /* Add back-to-top button for long pages */
    .back-to-top {
        position: fixed;
        bottom: 16px;
        right: 16px;
        z-index: 5;
    }
}

@media (max-width: 480px) {
    .responsive-padding {
        padding: 12px 8px;
    }

    /* Stack grid columns on very small screens */
    .sm-stack-vertical {
        flex-direction: column;
    }

    /* Optimize font sizes for readability */
    .text-h5 {
        font-size: 1.25rem;
    }

    .text-subtitle1 {
        font-size: 0.95rem;
    }

    /* Better notification positioning on mobile */
    .ios-notification {
        width: 92% !important;
        max-width: none !important;
    }
}

/* Improved touch feedback */
@media (hover: none) {
    .flag-card {
        transform: none !important; /* Disable hover animation on touch devices */
    }

    .flag-card:active {
        transform: scale(0.98) !important;
        transition: transform 0.2s;
    }

    /* Better tap highlight color that respects dark mode */
    .dark-theme .q-btn:active,
    .dark-theme .flag-card:active,
    .dark-theme [role="button"]:active {
        background-color: rgba(66, 133, 244, 0.2);
    }
}

/* Mobile optimizations */
@media (max-width: 600px) {
    .q-dialog__inner > div {
        max-width: 95vw !important;
    }
}

@media (max-width: 480px) {
    .responsive-padding {
        padding: 12px;
    }
}
</style>

