<template>
  <div class="admin-layout">
    <!-- <PERSON> Header -->
    <div class="admin-card q-mb-lg animate-slide-in-up">
      <div class="admin-card-header">
        <h1 class="admin-card-title">Enhanced Admin Components Demo</h1>
        <p class="admin-card-subtitle">
          Showcase of the new enhanced admin components with modern design and functionality
        </p>
      </div>
    </div>

    <!-- Enhanced Statistics Cards Demo -->
    <div class="row q-gutter-lg q-mb-xl">
      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern primary animate-slide-in-up">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="people" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_up" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">1,234</div>
              <div class="stats-label-modern">Total Users</div>
              <div class="stats-change-indicator">
                <q-icon name="trending_up" size="12px" />
                <span>+12% this month</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern secondary animate-slide-in-up" style="animation-delay: 0.1s">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="currency_exchange" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_up" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">567</div>
              <div class="stats-label-modern">Exchange Rates</div>
              <div class="stats-change-indicator">
                <q-icon name="check" size="12px" />
                <span>All active</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern accent animate-slide-in-up" style="animation-delay: 0.2s">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="update" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_flat" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">89</div>
              <div class="stats-label-modern">Updates Today</div>
              <div class="stats-change-indicator">
                <q-icon name="schedule" size="12px" />
                <span>Last: 2h ago</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card-modern animate-slide-in-up" style="background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); color: white; animation-delay: 0.3s">
          <q-card-section class="stats-content-modern">
            <div class="stats-header-modern">
              <div class="stats-icon-modern">
                <q-icon name="analytics" size="40px" />
              </div>
              <div class="stats-trend-modern">
                <q-icon name="trending_up" size="16px" />
              </div>
            </div>
            <div class="stats-info-modern">
              <div class="stats-value-large">98.5%</div>
              <div class="stats-label-modern">System Health</div>
              <div class="stats-change-indicator">
                <q-icon name="health_and_safety" size="12px" />
                <span>Excellent</span>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Enhanced Data Table Demo -->
    <div class="animate-slide-in-up" style="animation-delay: 0.4s">
      <EnhancedDataTable
        title="Users Management"
        subtitle="Manage system users with advanced features"
        :columns="tableColumns"
        :rows="tableData"
        :bulk-actions="bulkActions"
        searchable
        filterable
        exportable
        @bulk-action="handleBulkAction"
        @export="handleExport"
      >
        <template #actions>
          <q-btn
            icon="person_add"
            label="Add User"
            color="primary"
            class="admin-btn-primary"
            @click="showAddUserForm = true"
          />
        </template>

        <template #filters="{ filters, updateFilter }">
          <div class="row q-gutter-md">
            <div class="col-12 col-md-4">
              <q-select
                :model-value="filters.role"
                @update:model-value="updateFilter('role', $event)"
                :options="roleOptions"
                label="Filter by Role"
                outlined
                clearable
              />
            </div>
            <div class="col-12 col-md-4">
              <q-select
                :model-value="filters.status"
                @update:model-value="updateFilter('status', $event)"
                :options="statusOptions"
                label="Filter by Status"
                outlined
                clearable
              />
            </div>
          </div>
        </template>

        <template #cell-name="{ row }">
          <div class="user-info">
            <q-avatar size="32px" class="q-mr-sm">
              <img v-if="row.avatar" :src="row.avatar" />
              <q-icon v-else name="person" />
            </q-avatar>
            <div>
              <div class="text-weight-medium">{{ row.name }}</div>
              <div class="text-caption text-grey-6">{{ row.email }}</div>
            </div>
          </div>
        </template>

        <template #cell-role="{ value }">
          <q-chip
            :color="getRoleColor(value)"
            text-color="white"
            size="sm"
          >
            {{ value }}
          </q-chip>
        </template>

        <template #cell-status="{ value }">
          <div class="status-badge" :class="value ? 'active' : 'inactive'">
            {{ value ? 'Active' : 'Inactive' }}
          </div>
        </template>

        <template #row-actions="{ row }">
          <q-btn
            flat
            round
            icon="edit"
            size="sm"
            color="primary"
            @click="editUser(row)"
          >
            <q-tooltip>Edit User</q-tooltip>
          </q-btn>
          <q-btn
            flat
            round
            icon="delete"
            size="sm"
            color="negative"
            @click="deleteUser(row)"
          >
            <q-tooltip>Delete User</q-tooltip>
          </q-btn>
        </template>
      </EnhancedDataTable>
    </div>

    <!-- Enhanced Form Demo -->
    <div class="q-mt-xl animate-slide-in-up" style="animation-delay: 0.6s">
      <EnhancedForm
        v-if="showAddUserForm"
        title="Add New User"
        subtitle="Create a new user account with role and permissions"
        :sections="formSections"
        v-model="formData"
        :loading="formLoading"
        @submit="handleFormSubmit"
        @cancel="showAddUserForm = false"
      >
        <template #field-avatar>
          <div class="avatar-upload">
            <q-avatar size="80px" class="q-mb-md">
              <img v-if="formData.avatar" :src="formData.avatar" />
              <q-icon v-else name="person" size="40px" />
            </q-avatar>
            <q-btn
              flat
              icon="camera_alt"
              label="Upload Avatar"
              @click="uploadAvatar"
            />
          </div>
        </template>
      </EnhancedForm>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useQuasar } from 'quasar'
import EnhancedDataTable from '@/components/Admin/EnhancedDataTable.vue'
import EnhancedForm from '@/components/Admin/EnhancedForm.vue'

const $q = useQuasar()

// Reactive data
const showAddUserForm = ref(false)
const formLoading = ref(false)

const formData = ref({
  name: '',
  email: '',
  role: null,
  status: true,
  avatar: null,
  permissions: []
})

// Table configuration
const tableColumns = [
  { name: 'name', label: 'Name', field: 'name', sortable: true, align: 'left' },
  { name: 'role', label: 'Role', field: 'role', sortable: true, align: 'center' },
  { name: 'status', label: 'Status', field: 'status', sortable: true, align: 'center' },
  { name: 'created_at', label: 'Created', field: 'created_at', sortable: true, align: 'center' }
]

const tableData = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Admin',
    status: true,
    created_at: '2024-01-15',
    avatar: null
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'Manager',
    status: true,
    created_at: '2024-01-10',
    avatar: null
  },
  {
    id: 3,
    name: 'Mike Johnson',
    email: '<EMAIL>',
    role: 'Editor',
    status: false,
    created_at: '2024-01-05',
    avatar: null
  }
])

const bulkActions = [
  { name: 'activate', label: 'Activate Selected', icon: 'check_circle', color: 'positive' },
  { name: 'deactivate', label: 'Deactivate Selected', icon: 'block', color: 'warning' },
  { name: 'delete', label: 'Delete Selected', icon: 'delete', color: 'negative' }
]

const roleOptions = [
  { label: 'Admin', value: 'Admin' },
  { label: 'Manager', value: 'Manager' },
  { label: 'Editor', value: 'Editor' }
]

const statusOptions = [
  { label: 'Active', value: true },
  { label: 'Inactive', value: false }
]

// Form configuration
const formSections = [
  {
    title: 'Basic Information',
    description: 'Enter the user\'s basic details',
    layout: 'grid-2',
    fields: [
      {
        name: 'name',
        type: 'text',
        label: 'Full Name',
        placeholder: 'Enter full name',
        required: true,
        rules: [val => !!val || 'Name is required']
      },
      {
        name: 'email',
        type: 'email',
        label: 'Email Address',
        placeholder: 'Enter email address',
        required: true,
        rules: [
          val => !!val || 'Email is required',
          val => /.+@.+\..+/.test(val) || 'Email must be valid'
        ]
      }
    ]
  },
  {
    title: 'Role & Permissions',
    description: 'Set user role and access permissions',
    layout: 'grid-2',
    fields: [
      {
        name: 'role',
        type: 'select',
        label: 'User Role',
        options: roleOptions,
        required: true,
        rules: [val => !!val || 'Role is required']
      },
      {
        name: 'status',
        type: 'checkbox',
        label: 'Active User'
      }
    ]
  },
  {
    title: 'Profile',
    description: 'Upload user avatar and additional details',
    layout: 'grid-1',
    fields: [
      {
        name: 'avatar',
        type: 'custom',
        label: 'Profile Avatar'
      }
    ]
  }
]

// Methods
const getRoleColor = (role) => {
  const colors = {
    'Admin': 'negative',
    'Manager': 'warning',
    'Editor': 'info'
  }
  return colors[role] || 'grey'
}

const handleBulkAction = ({ action, rows }) => {
  $q.notify({
    type: 'info',
    message: `Bulk action "${action}" applied to ${rows.length} users`
  })
}

const handleExport = ({ rows }) => {
  $q.notify({
    type: 'positive',
    message: `Exported ${rows.length} users`
  })
}

const editUser = (user) => {
  $q.notify({
    type: 'info',
    message: `Editing user: ${user.name}`
  })
}

const deleteUser = (user) => {
  $q.dialog({
    title: 'Confirm Delete',
    message: `Are you sure you want to delete ${user.name}?`,
    cancel: true,
    persistent: true
  }).onOk(() => {
    $q.notify({
      type: 'positive',
      message: `User ${user.name} deleted successfully`
    })
  })
}

const handleFormSubmit = () => {
  formLoading.value = true

  setTimeout(() => {
    formLoading.value = false
    showAddUserForm.value = false

    $q.notify({
      type: 'positive',
      message: 'User created successfully!'
    })

    // Reset form
    formData.value = {
      name: '',
      email: '',
      role: null,
      status: true,
      avatar: null,
      permissions: []
    }
  }, 2000)
}

const uploadAvatar = () => {
  $q.notify({
    type: 'info',
    message: 'Avatar upload functionality would be implemented here'
  })
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Add staggered animation delays */
.animate-slide-in-up:nth-child(1) { animation-delay: 0s; }
.animate-slide-in-up:nth-child(2) { animation-delay: 0.1s; }
.animate-slide-in-up:nth-child(3) { animation-delay: 0.2s; }
.animate-slide-in-up:nth-child(4) { animation-delay: 0.3s; }
</style>
