<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import axios from 'axios'
import ExchangeRateChart from '@/components/Admin/Charts/ExchangeRateChart.vue'
import CurrencyPerformanceChart from '@/components/Admin/Charts/CurrencyPerformanceChart.vue'

const { t } = useI18n()

// Reactive data
const refreshing = ref(false)
const updating = ref(false)
const bulkUpdating = ref(false)
const chartLoading = ref(false)
const showBulkUpdateDialog = ref(false)
const bulkUpdateNotes = ref('')
const chartPeriod = ref('7')

const stats = ref({
  total_currencies: 0,
  active_currencies: 0,
  total_rates: 0,
  active_rates: 0,
  total_updates: 0,
  avg_volatility: 0
})

const updateStatus = ref({
  last_update: null,
  is_stale: true
})

const chartData = ref([])
const recentActivity = ref([])
const activePairs = ref([])

// Chart options
const chartPeriodOptions = [
  { label: '7D', value: '7' },
  { label: '30D', value: '30' },
  { label: '90D', value: '90' }
]

// Table columns
const pairsColumns = [
  { name: 'pair', label: 'Currency Pair', field: 'pair', sortable: false },
  { name: 'updates', label: 'Updates', field: 'updates_count', sortable: true },
  { name: 'last_rate', label: 'Current Rate', field: 'current_rate', sortable: true },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false }
]

// Computed properties
const maxCount = computed(() => {
  return Math.max(...chartData.value.map(d => d.count), 1)
})

// Methods
const refreshData = async () => {
  refreshing.value = true
  try {
    await Promise.all([
      loadStats(),
      loadUpdateStatus(),
      loadChartData(),
      loadRecentActivity(),
      loadActivePairs()
    ])
  } catch (error) {
    console.error('Error refreshing data:', error)
  } finally {
    refreshing.value = false
  }
}

const loadStats = async () => {
  try {
    // This would typically call multiple API endpoints
    // For now, using mock data
    stats.value = {
      total_currencies: 12,
      active_currencies: 12,
      total_rates: 37,
      active_rates: 37,
      total_updates: 15,
      avg_volatility: 2.3
    }
  } catch (error) {
    console.error('Error loading stats:', error)
  }
}

const loadUpdateStatus = async () => {
  try {
    const response = await axios.get('/api/exchange/update-status')
    if (response.data.success) {
      updateStatus.value = response.data.data
    }
  } catch (error) {
    console.error('Error loading update status:', error)
  }
}

const loadChartData = async () => {
  chartLoading.value = true
  try {
    // Mock chart data - in real app, this would call an API
    const days = parseInt(chartPeriod.value)
    chartData.value = Array.from({ length: days }, (_, i) => ({
      date: new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      count: Math.floor(Math.random() * 20) + 1
    }))
  } catch (error) {
    console.error('Error loading chart data:', error)
  } finally {
    chartLoading.value = false
  }
}

const loadRecentActivity = async () => {
  try {
    // Mock recent activity - in real app, this would call an API
    recentActivity.value = [
      {
        id: 1,
        type: 'rate_update',
        description: 'Exchange rate updated',
        from_currency: 'USD',
        to_currency: 'IQD',
        rate: '1311.70',
        source: 'api',
        created_at: new Date().toISOString()
      },
      {
        id: 2,
        type: 'rate_update',
        description: 'Exchange rate updated',
        from_currency: 'USD',
        to_currency: 'EUR',
        rate: '0.8820',
        source: 'api',
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      }
    ]
  } catch (error) {
    console.error('Error loading recent activity:', error)
  }
}

const loadActivePairs = async () => {
  try {
    const response = await axios.get('/api/exchange/statistics?period=7')
    if (response.data.success) {
      activePairs.value = response.data.data.most_active_pairs || []
    }
  } catch (error) {
    console.error('Error loading active pairs:', error)
  }
}

const triggerManualUpdate = async () => {
  updating.value = true
  try {
    // This would trigger the rate update command
    await axios.post('/admin/trigger-rate-update')
    await loadUpdateStatus()
    await loadRecentActivity()
  } catch (error) {
    console.error('Error triggering manual update:', error)
  } finally {
    updating.value = false
  }
}

const performBulkUpdate = async () => {
  bulkUpdating.value = true
  try {
    // This would perform bulk update
    await axios.patch('/admin/currency-exchange-rates/bulk-update', {
      notes: bulkUpdateNotes.value
    })
    showBulkUpdateDialog.value = false
    bulkUpdateNotes.value = ''
    await refreshData()
  } catch (error) {
    console.error('Error performing bulk update:', error)
  } finally {
    bulkUpdating.value = false
  }
}

const formatLastUpdate = (lastUpdate) => {
  if (!lastUpdate) return 'Never'
  const now = new Date()
  const update = new Date(lastUpdate)
  const diffMinutes = Math.floor((now - update) / (1000 * 60))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  return update.toLocaleDateString()
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const formatChartDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}

const getActivityIcon = (type) => {
  switch (type) {
    case 'rate_update': return 'update'
    case 'currency_added': return 'add'
    case 'rate_deleted': return 'delete'
    default: return 'info'
  }
}

const getActivityColor = (type) => {
  switch (type) {
    case 'rate_update': return 'primary'
    case 'currency_added': return 'positive'
    case 'rate_deleted': return 'negative'
    default: return 'grey'
  }
}

const getSourceColor = (source) => {
  switch (source) {
    case 'api': return 'blue'
    case 'automatic': return 'green'
    case 'manual': return 'orange'
    default: return 'grey'
  }
}

// Lifecycle
onMounted(() => {
  refreshData()
})
</script>

<template>
  <q-page class="admin-dashboard">
    <!-- Modern Header Section -->
    <div class="modern-dashboard-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">Dashboard</h1>
            <p class="header-subtitle">Monitor your currency exchange system performance and activity</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="refreshData"
              :loading="refreshing"
              icon="refresh"
              label="Refresh Data"
              class="refresh-btn"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <!-- Enhanced Statistics Cards -->
      <div class="row q-gutter-lg q-mb-xl">
        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-primary">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="account_balance_wallet" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total_currencies }}</div>
                <div class="stat-label">Total Currencies</div>
                <div class="stat-change positive">
                  <q-icon name="trending_up" size="14px" />
                  {{ stats.active_currencies }} active
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-success">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="swap_horiz" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total_rates }}</div>
                <div class="stat-label">Exchange Rates</div>
                <div class="stat-change positive">
                  <q-icon name="check_circle" size="14px" />
                  {{ stats.active_rates }} active
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-warning">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="update" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total_updates }}</div>
                <div class="stat-label">Updates Today</div>
                <div class="stat-change" :class="updateStatus.is_stale ? 'neutral' : 'positive'">
                  <q-icon :name="updateStatus.is_stale ? 'warning' : 'check'" size="14px" />
                  {{ updateStatus.is_stale ? 'Stale' : 'Fresh' }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-accent">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="trending_up" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.avg_volatility }}%</div>
                <div class="stat-label">Avg Volatility</div>
                <div class="stat-change neutral">
                  <q-icon name="timeline" size="14px" />
                  Last 24 hours
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Main Content Row -->
      <div class="row q-gutter-md">
        <!-- Exchange Rate Chart -->
        <div class="col-12 col-md-8">
          <ExchangeRateChart
            :currencies="['USD', 'EUR', 'GBP']"
            :height="300"
          />
        </div>

        <!-- System Status -->
        <div class="col-12 col-md-4">
          <q-card class="status-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">System Status</h6>

              <!-- Update Status -->
              <div class="status-item q-mb-md">
                <div class="row items-center">
                  <div class="col">
                    <div class="text-subtitle2">Rate Updates</div>
                    <div class="text-caption text-grey-6">
                      Last: {{ formatLastUpdate(updateStatus.last_update) }}
                    </div>
                  </div>
                  <div class="col-auto">
                    <q-chip
                      :color="updateStatus.is_stale ? 'warning' : 'positive'"
                      text-color="white"
                      size="sm"
                    >
                      {{ updateStatus.is_stale ? 'Stale' : 'Fresh' }}
                    </q-chip>
                  </div>
                </div>
              </div>

              <!-- API Status -->
              <div class="status-item q-mb-md">
                <div class="row items-center">
                  <div class="col">
                    <div class="text-subtitle2">API Status</div>
                    <div class="text-caption text-grey-6">External providers</div>
                  </div>
                  <div class="col-auto">
                    <q-chip color="positive" text-color="white" size="sm">
                      Online
                    </q-chip>
                  </div>
                </div>
              </div>

              <!-- Database Status -->
              <div class="status-item q-mb-md">
                <div class="row items-center">
                  <div class="col">
                    <div class="text-subtitle2">Database</div>
                    <div class="text-caption text-grey-6">Connection status</div>
                  </div>
                  <div class="col-auto">
                    <q-chip color="positive" text-color="white" size="sm">
                      Connected
                    </q-chip>
                  </div>
                </div>
              </div>

              <!-- Manual Update Button -->
              <q-btn
                @click="triggerManualUpdate"
                :loading="updating"
                icon="update"
                label="Update Rates Now"
                color="primary"
                class="full-width q-mt-md"
              />
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Analytics Row -->
      <div class="row q-gutter-md q-mt-md">
        <!-- Currency Performance Chart -->
        <div class="col-12 col-md-6">
          <CurrencyPerformanceChart :height="250" />
        </div>

        <!-- Recent Activity -->
        <div class="col-12 col-md-6">
          <q-card class="activity-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Recent Activity</h6>

              <q-list>
                <q-item
                  v-for="activity in recentActivity"
                  :key="activity.id"
                  class="activity-item"
                >
                  <q-item-section avatar>
                    <q-icon
                      :name="getActivityIcon(activity.type)"
                      :color="getActivityColor(activity.type)"
                      size="sm"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ activity.description }}</q-item-label>
                    <q-item-label caption>
                      {{ activity.from_currency }} → {{ activity.to_currency }}
                      <span class="q-ml-sm text-weight-medium">
                        Rate: {{ activity.rate }}
                      </span>
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label caption>{{ formatTime(activity.created_at) }}</q-item-label>
                    <q-chip
                      :color="getSourceColor(activity.source)"
                      text-color="white"
                      size="xs"
                    >
                      {{ activity.source }}
                    </q-chip>
                  </q-item-section>
                </q-item>
              </q-list>

              <div v-if="recentActivity.length === 0" class="text-center text-grey-6 q-pa-lg">
                <q-icon name="history" size="3rem" class="q-mb-md" />
                <div>No recent activity</div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Quick Actions -->
        <div class="col-12 col-md-4">
          <q-card class="actions-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Quick Actions</h6>

              <div class="q-gutter-sm">
                <q-btn
                  @click="$inertia.visit(route('admin.currencies.create'))"
                  icon="add"
                  label="Add Currency"
                  color="primary"
                  class="full-width"
                  outline
                />

                <q-btn
                  @click="$inertia.visit(route('admin.currency-exchange-rates.create'))"
                  icon="swap_horiz"
                  label="Add Exchange Rate"
                  color="secondary"
                  class="full-width"
                  outline
                />

                <q-btn
                  @click="showBulkUpdateDialog = true"
                  icon="edit"
                  label="Bulk Update Rates"
                  color="accent"
                  class="full-width"
                  outline
                />

                <q-btn
                  :to="{ name: 'admin.exchange-rate-history.index' }"
                  icon="history"
                  label="View History"
                  color="info"
                  class="full-width"
                  outline
                />
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Most Active Pairs -->
      <div class="row q-gutter-md q-mt-md">
        <div class="col-12">
          <q-card class="pairs-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Most Active Currency Pairs</h6>

              <q-table
                :rows="activePairs"
                :columns="pairsColumns"
                row-key="id"
                flat
                :pagination="{ rowsPerPage: 5 }"
              >
                <template v-slot:body-cell-pair="props">
                  <q-td :props="props">
                    <div class="flex items-center">
                      <span class="text-lg q-mr-xs">{{ props.row.from_currency.flag_icon }}</span>
                      <span class="font-medium">{{ props.row.from_currency.code }}</span>
                      <q-icon name="arrow_forward" size="xs" class="q-mx-sm" />
                      <span class="text-lg q-mr-xs">{{ props.row.to_currency.flag_icon }}</span>
                      <span class="font-medium">{{ props.row.to_currency.code }}</span>
                    </div>
                  </q-td>
                </template>

                <template v-slot:body-cell-updates="props">
                  <q-td :props="props">
                    <q-chip color="primary" text-color="white" size="sm">
                      {{ props.row.updates_count }}
                    </q-chip>
                  </q-td>
                </template>

                <template v-slot:body-cell-actions="props">
                  <q-td :props="props">
                    <q-btn
                      @click="$inertia.visit(route('admin.currency-exchange-rates.edit', props.row.id))"
                      icon="edit"
                      color="primary"
                      flat
                      round
                      size="sm"
                    />
                  </q-td>
                </template>
              </q-table>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>

    <!-- Bulk Update Dialog -->
    <q-dialog v-model="showBulkUpdateDialog">
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">Bulk Update Exchange Rates</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            v-model="bulkUpdateNotes"
            label="Update Notes"
            outlined
            type="textarea"
            rows="3"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            @click="performBulkUpdate"
            :loading="bulkUpdating"
            label="Update All"
            color="primary"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<style scoped>
.admin-dashboard {
  background: #f8fafc;
  min-height: 100vh;
}

/* Modern Header */
.modern-dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.refresh-btn {
  border-radius: 12px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  color: white;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.dashboard-header {
  background: linear-gradient(135deg, #1976d2 0%, #7b1fa2 100%);
}

/* Enhanced Statistics Cards */
.modern-stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.modern-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card-accent {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stat-icon {
  margin-right: 16px;
  opacity: 0.9;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.stat-change.neutral {
  color: rgba(255, 255, 255, 0.7);
}

.stat-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.chart-card, .status-card, .activity-card, .actions-card, .pairs-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chart-visual {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.chart-bars {
  display: flex;
  align-items: end;
  height: 200px;
  gap: 8px;
  margin-bottom: 10px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, #1976d2, #42a5f5);
  border-radius: 4px 4px 0 0;
  position: relative;
  min-height: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.chart-bar:hover {
  background: linear-gradient(to top, #1565c0, #1976d2);
}

.bar-value {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: bold;
  color: #666;
}

.chart-labels {
  display: flex;
  gap: 8px;
}

.chart-label {
  flex: 1;
  text-align: center;
  font-size: 10px;
  color: #666;
}

.status-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.activity-item {
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
  }

  .chart-container {
    height: 200px !important;
  }
}
</style>
