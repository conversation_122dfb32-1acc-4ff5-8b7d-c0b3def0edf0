<template>
  <q-page class="exchange-rate-edit">
    <!-- Header -->
    <div class="page-header q-pa-lg bg-white shadow-1">
      <div class="row items-center">
        <div class="col">
          <h1 class="text-h4 q-mb-sm">Edit Exchange Rate</h1>
          <p class="text-subtitle1 text-grey-6">Update exchange rate information</p>
        </div>
        <div class="col-auto">
          <q-btn
            :to="{ name: 'admin.currency-exchange-rates.index' }"
            icon="arrow_back"
            label="Back to Exchange Rates"
            color="grey"
            outline
          />
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <div class="row justify-center">
        <div class="col-12 col-md-8 col-lg-6">
          <q-card class="form-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-lg">Exchange Rate Information</h6>

              <q-form @submit="submitForm" class="q-gutter-md">
                <!-- Currency Pair Display (Read-only) -->
                <div class="currency-pair-display q-pa-md bg-blue-1 rounded-borders q-mb-md">
                  <div class="text-subtitle2 q-mb-xs">Currency Pair</div>
                  <div class="row items-center">
                    <div class="col-auto">
                      <div class="text-2xl q-mr-sm">{{ rate.from_currency?.flag_icon }}</div>
                    </div>
                    <div class="col">
                      <div class="text-h6">{{ rate.from_currency?.name }}</div>
                      <div class="text-caption text-grey-6">{{ rate.from_currency?.code }}</div>
                    </div>
                    <div class="col-auto">
                      <q-icon name="arrow_forward" size="md" class="q-mx-md" />
                    </div>
                    <div class="col-auto">
                      <div class="text-2xl q-mr-sm">{{ rate.to_currency?.flag_icon }}</div>
                    </div>
                    <div class="col">
                      <div class="text-h6">{{ rate.to_currency?.name }}</div>
                      <div class="text-caption text-grey-6">{{ rate.to_currency?.code }}</div>
                    </div>
                  </div>
                </div>

                <!-- Exchange Rate -->
                <div class="row q-gutter-md">
                  <div class="col-12 col-sm-8">
                    <q-input
                      v-model.number="form.rate"
                      label="Exchange Rate *"
                      outlined
                      type="number"
                      step="0.00000001"
                      min="0.00000001"
                      :error="!!errors.rate"
                      :error-message="errors.rate"
                      hint="1 unit of source currency = X units of target currency"
                    >
                      <template v-slot:prepend>
                        <q-icon name="swap_horiz" />
                      </template>
                    </q-input>
                  </div>

                  <div class="col-12 col-sm-4">
                    <q-btn
                      @click="fetchCurrentRate"
                      :loading="fetchingRate"
                      icon="refresh"
                      label="Fetch Rate"
                      color="secondary"
                      outline
                      class="full-width"
                    />
                  </div>
                </div>

                <!-- Rate Comparison -->
                <div v-if="form.rate && rate.rate" class="rate-comparison q-pa-md bg-grey-1 rounded-borders">
                  <div class="text-subtitle2 q-mb-xs">Rate Comparison</div>
                  <div class="row q-gutter-md">
                    <div class="col">
                      <div class="text-caption text-grey-6">Current Rate</div>
                      <div class="text-h6">{{ formatRate(rate.rate) }}</div>
                    </div>
                    <div class="col">
                      <div class="text-caption text-grey-6">New Rate</div>
                      <div class="text-h6">{{ formatRate(form.rate) }}</div>
                    </div>
                    <div class="col">
                      <div class="text-caption text-grey-6">Change</div>
                      <div class="text-h6" :class="getChangeColor(rateChangePercentage)">
                        {{ rateChangePercentage > 0 ? '+' : '' }}{{ rateChangePercentage.toFixed(2) }}%
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Rate Preview -->
                <div v-if="ratePreview" class="rate-preview q-pa-md bg-blue-1 rounded-borders">
                  <div class="text-subtitle2 q-mb-xs">Rate Preview</div>
                  <div class="row items-center">
                    <div class="col">
                      <div class="text-body1">
                        1 {{ rate.from_currency?.code }} = {{ formatRate(form.rate) }} {{ rate.to_currency?.code }}
                      </div>
                      <div class="text-caption text-grey-6">
                        {{ formatAmount(100, rate.from_currency?.code) }} = {{ formatAmount(100 * form.rate, rate.to_currency?.code) }}
                      </div>
                    </div>
                    <div class="col-auto">
                      <q-icon name="trending_up" size="md" color="positive" />
                    </div>
                  </div>
                </div>

                <!-- Notes -->
                <q-input
                  v-model="form.notes"
                  label="Update Notes"
                  outlined
                  type="textarea"
                  rows="3"
                  :error="!!errors.notes"
                  :error-message="errors.notes"
                  hint="Optional notes about this rate update"
                />

                <!-- Status -->
                <div class="q-gutter-sm">
                  <q-checkbox
                    v-model="form.is_active"
                    label="Active Exchange Rate"
                    color="positive"
                  />
                  <div class="text-caption text-grey-6 q-ml-lg">
                    Active rates can be used for currency conversions
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="row q-gutter-sm q-mt-lg">
                  <div class="col">
                    <q-btn
                      :to="{ name: 'admin.currency-exchange-rates.index' }"
                      label="Cancel"
                      color="grey"
                      outline
                      class="full-width"
                    />
                  </div>
                  <div class="col">
                    <q-btn
                      type="submit"
                      :loading="submitting"
                      label="Update Exchange Rate"
                      color="primary"
                      class="full-width"
                    />
                  </div>
                </div>
              </q-form>
            </q-card-section>
          </q-card>

          <!-- Rate Calculator -->
          <q-card v-if="form.rate && rate.from_currency && rate.to_currency" class="calculator-card q-mt-lg">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Rate Calculator</h6>

              <div class="row q-gutter-md">
                <div class="col-12 col-sm-6">
                  <q-input
                    v-model.number="calculatorAmount"
                    label="Amount"
                    outlined
                    type="number"
                    step="0.01"
                    min="0"
                  >
                    <template v-slot:append>
                      <span class="text-caption">{{ rate.from_currency?.code }}</span>
                    </template>
                  </q-input>
                </div>

                <div class="col-12 col-sm-6">
                  <q-input
                    :model-value="convertedAmount"
                    label="Converted Amount"
                    outlined
                    readonly
                  >
                    <template v-slot:append>
                      <span class="text-caption">{{ rate.to_currency?.code }}</span>
                    </template>
                  </q-input>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- Rate History -->
          <q-card v-if="rateHistory.length > 0" class="history-card q-mt-lg">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Recent Rate History</h6>

              <q-list>
                <q-item
                  v-for="history in rateHistory.slice(0, 5)"
                  :key="history.id"
                  class="history-item"
                >
                  <q-item-section>
                    <q-item-label>{{ formatRate(history.rate) }}</q-item-label>
                    <q-item-label caption>{{ formatDateTime(history.created_at) }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-badge :color="getSourceColor(history.source)">
                      {{ history.source }}
                    </q-badge>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { router } from '@inertiajs/vue3'
import axios from 'axios'

const $router = useRouter()
const $q = useQuasar()

// Props
const props = defineProps({
  rate: {
    type: Object,
    required: true
  },
  currencies: {
    type: Array,
    default: () => []
  }
})

// Form data
const form = ref({
  rate: null,
  notes: '',
  is_active: true
})

const errors = ref({})
const submitting = ref(false)
const fetchingRate = ref(false)
const calculatorAmount = ref(1)
const rateHistory = ref([])

// Computed properties
const ratePreview = computed(() => {
  return form.value.rate && props.rate.from_currency && props.rate.to_currency
})

const convertedAmount = computed(() => {
  if (calculatorAmount.value && form.value.rate) {
    return (calculatorAmount.value * form.value.rate).toFixed(4)
  }
  return 0
})

const rateChangePercentage = computed(() => {
  if (form.value.rate && props.rate.rate) {
    return ((form.value.rate - props.rate.rate) / props.rate.rate) * 100
  }
  return 0
})

// Methods
const submitForm = async () => {
  submitting.value = true
  errors.value = {}

  try {
    router.put(`/admin/currency-exchange-rates/${props.rate.id}`, form.value, {
      onSuccess: () => {
        $q.notify({
          type: 'positive',
          message: 'Exchange rate updated successfully',
          position: 'top'
        })
      },
      onError: (formErrors) => {
        errors.value = formErrors
        $q.notify({
          type: 'negative',
          message: 'Please check the form for errors',
          position: 'top'
        })
      }
    })
  } catch (error) {
    console.error('Error updating exchange rate:', error)
    $q.notify({
      type: 'negative',
      message: 'An error occurred while updating the exchange rate',
      position: 'top'
    })
  } finally {
    submitting.value = false
  }
}

const fetchCurrentRate = async () => {
  fetchingRate.value = true
  try {
    // This would typically call an external API to get current rates
    // For now, we'll simulate it
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock rate based on currency codes
    let mockRate = 1
    if (props.rate.from_currency?.code === 'USD' && props.rate.to_currency?.code === 'IQD') {
      mockRate = 1310.50
    } else if (props.rate.from_currency?.code === 'USD' && props.rate.to_currency?.code === 'EUR') {
      mockRate = 0.85
    } else if (props.rate.from_currency?.code === 'EUR' && props.rate.to_currency?.code === 'USD') {
      mockRate = 1.18
    } else {
      mockRate = Math.random() * 10 + 0.1
    }

    form.value.rate = parseFloat(mockRate.toFixed(8))

    $q.notify({
      type: 'positive',
      message: 'Current rate fetched successfully',
      position: 'top'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch current rate',
      position: 'top'
    })
  } finally {
    fetchingRate.value = false
  }
}

const loadRateHistory = async () => {
  try {
    const response = await axios.get(`/api/admin/exchange-rates/${props.rate.id}/history`)
    if (response.data.success) {
      rateHistory.value = response.data.history
    }
  } catch (error) {
    console.error('Error loading rate history:', error)
  }
}

const formatRate = (rate) => {
  return parseFloat(rate).toFixed(4)
}

const formatAmount = (amount, currencyCode) => {
  const currency = props.currencies.find(c => c.code === currencyCode)
  const decimals = currency?.decimal_places || 2
  return parseFloat(amount).toFixed(decimals)
}

const formatDateTime = (datetime) => {
  return new Date(datetime).toLocaleString()
}

const getChangeColor = (changePercentage) => {
  if (changePercentage > 0) return 'text-positive'
  if (changePercentage < 0) return 'text-negative'
  return 'text-grey'
}

const getSourceColor = (source) => {
  switch (source) {
    case 'api': return 'blue'
    case 'automatic': return 'green'
    case 'manual': return 'orange'
    default: return 'grey'
  }
}

const loadRateData = () => {
  if (props.rate) {
    form.value = {
      rate: props.rate.rate || null,
      notes: '',
      is_active: props.rate.is_active !== undefined ? props.rate.is_active : true
    }
  }
}

// Lifecycle
onMounted(() => {
  loadRateData()
  loadRateHistory()
})
</script>

<style scoped>
.exchange-rate-edit {
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  border-bottom: 1px solid #e0e0e0;
}

.form-card, .calculator-card, .history-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.currency-pair-display {
  border: 1px solid #e3f2fd;
}

.rate-preview, .rate-comparison {
  border: 1px solid #e3f2fd;
}

.history-item {
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
}
</style>
