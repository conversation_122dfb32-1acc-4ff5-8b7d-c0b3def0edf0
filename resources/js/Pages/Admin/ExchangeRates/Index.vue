<template>
  <q-page class="exchange-rates-admin">
    <!-- Header -->
    <div class="page-header q-pa-lg bg-white shadow-1">
      <div class="row items-center">
        <div class="col">
          <h1 class="text-h4 q-mb-sm">Exchange Rates Management</h1>
          <p class="text-subtitle1 text-grey-6">Manage currency exchange rates and monitor updates</p>
        </div>
        <div class="col-auto">
          <div class="q-gutter-sm">
            <q-btn
              @click="showRateFetchingModal = true"
              :loading="updating"
              icon="sync"
              label="Fetch from Multiple Sources"
              color="accent"
              outline
            />
            <q-btn
              @click="triggerRateUpdate"
              :loading="updating"
              icon="update"
              label="Update Rates"
              color="secondary"
              outline
            />
            <q-btn
              @click="showExchangeRateCreateModal = true"
              icon="add"
              label="Add Rate"
              color="primary"
              size="lg"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <!-- Update Status Banner -->
      <q-banner
        v-if="updateStatus.is_stale"
        class="bg-warning text-white q-mb-lg"
        rounded
      >
        <template v-slot:avatar>
          <q-icon name="warning" />
        </template>
        Exchange rates are stale. Last update: {{ formatLastUpdate(updateStatus.last_update) }}
        <template v-slot:action>
          <q-btn
            @click="triggerRateUpdate"
            :loading="updating"
            flat
            color="white"
            label="Update Now"
          />
        </template>
      </q-banner>

      <!-- Filters and Search -->
      <q-card class="filter-card q-mb-lg">
        <q-card-section>
          <div class="row q-gutter-md items-end">
            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="filters.from_currency_id"
                :options="currencyOptions"
                option-value="id"
                option-label="display_name"
                label="From Currency"
                outlined
                clearable
                @update:model-value="loadRates"
              />
            </div>

            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="filters.to_currency_id"
                :options="currencyOptions"
                option-value="id"
                option-label="display_name"
                label="To Currency"
                outlined
                clearable
                @update:model-value="loadRates"
              />
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-select
                v-model="filters.is_active"
                :options="statusOptions"
                label="Status"
                outlined
                clearable
                @update:model-value="loadRates"
              />
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-select
                v-model="filters.source"
                :options="sourceOptions"
                label="Source"
                outlined
                clearable
                @update:model-value="loadRates"
              />
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-btn
                @click="resetFilters"
                icon="clear"
                label="Reset"
                color="grey"
                outline
                class="full-width"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Statistics Cards -->
      <div class="row q-gutter-md q-mb-lg">
        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="stat-card">
            <q-card-section class="text-center">
              <q-icon name="swap_horiz" size="2rem" color="primary" class="q-mb-sm" />
              <div class="text-h5 text-weight-bold">{{ stats.total }}</div>
              <div class="text-caption text-grey-6">Total Rates</div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="stat-card">
            <q-card-section class="text-center">
              <q-icon name="check_circle" size="2rem" color="positive" class="q-mb-sm" />
              <div class="text-h5 text-weight-bold">{{ stats.active }}</div>
              <div class="text-caption text-grey-6">Active</div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="stat-card">
            <q-card-section class="text-center">
              <q-icon name="schedule" size="2rem" color="warning" class="q-mb-sm" />
              <div class="text-h5 text-weight-bold">{{ stats.stale }}</div>
              <div class="text-caption text-grey-6">Stale (>1h)</div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="stat-card">
            <q-card-section class="text-center">
              <q-icon name="api" size="2rem" color="info" class="q-mb-sm" />
              <div class="text-h5 text-weight-bold">{{ stats.api_updated }}</div>
              <div class="text-caption text-grey-6">API Updated</div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Exchange Rates Table -->
      <q-card class="table-card">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <div class="col">
              <h6 class="q-ma-none">Exchange Rates</h6>
            </div>
            <div class="col-auto">
              <div class="q-gutter-sm">
                <q-btn
                  @click="showBulkUpdateDialog = true"
                  :disable="selectedRates.length === 0"
                  icon="edit"
                  label="Bulk Update"
                  color="accent"
                  outline
                  size="sm"
                />
                <q-btn
                  @click="exportRates"
                  icon="download"
                  label="Export"
                  color="info"
                  outline
                  size="sm"
                />
              </div>
            </div>
          </div>

          <q-table
            :rows="rates.data"
            :columns="columns"
            row-key="id"
            :loading="loading"
            :pagination="pagination"
            @request="onRequest"
            selection="multiple"
            v-model:selected="selectedRates"
            flat
            bordered
          >
            <template v-slot:body-cell-pair="props">
              <q-td :props="props">
                <div class="flex items-center">
                  <span class="text-lg q-mr-xs">{{ props.row.from_currency.flag_icon }}</span>
                  <span class="font-medium">{{ props.row.from_currency.code }}</span>
                  <q-icon name="arrow_forward" size="xs" class="q-mx-sm" />
                  <span class="text-lg q-mr-xs">{{ props.row.to_currency.flag_icon }}</span>
                  <span class="font-medium">{{ props.row.to_currency.code }}</span>
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-rate="props">
              <q-td :props="props">
                <div class="text-weight-bold">{{ formatRate(props.row.rate) }}</div>
                <div class="text-caption text-grey-6">
                  1 {{ props.row.from_currency.code }} = {{ formatRate(props.row.rate) }} {{ props.row.to_currency.code }}
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-change="props">
              <q-td :props="props">
                <q-chip
                  v-if="props.row.change_percentage !== null"
                  :color="getChangeColor(props.row.change_percentage)"
                  text-color="white"
                  size="sm"
                >
                  <q-icon
                    :name="props.row.change_percentage > 0 ? 'trending_up' : props.row.change_percentage < 0 ? 'trending_down' : 'trending_flat'"
                    class="q-mr-xs"
                  />
                  {{ props.row.change_percentage > 0 ? '+' : '' }}{{ props.row.change_percentage }}%
                </q-chip>
                <span v-else class="text-grey-6">-</span>
              </q-td>
            </template>

            <template v-slot:body-cell-status="props">
              <q-td :props="props">
                <q-chip
                  :color="props.row.is_active ? 'positive' : 'negative'"
                  text-color="white"
                  size="sm"
                >
                  {{ props.row.is_active ? 'Active' : 'Inactive' }}
                </q-chip>
              </q-td>
            </template>

            <template v-slot:body-cell-source="props">
              <q-td :props="props">
                <q-badge :color="getSourceColor(props.row.source)">
                  {{ props.row.source }}
                </q-badge>
              </q-td>
            </template>

            <template v-slot:body-cell-updated="props">
              <q-td :props="props">
                <div class="text-caption">{{ formatDateTime(props.row.updated_at) }}</div>
                <div class="text-caption text-grey-6">{{ getTimeAgo(props.row.updated_at) }}</div>
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <div class="q-gutter-xs">
                  <q-btn
                    @click="showRateHistory(props.row)"
                    icon="history"
                    color="info"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>View History</q-tooltip>
                  </q-btn>

                  <q-btn
                    @click="$inertia.visit(route('admin.currency-exchange-rates.edit', props.row.id))"
                    icon="edit"
                    color="primary"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Edit Rate</q-tooltip>
                  </q-btn>

                  <q-btn
                    @click="toggleStatus(props.row)"
                    :icon="props.row.is_active ? 'pause' : 'play_arrow'"
                    :color="props.row.is_active ? 'warning' : 'positive'"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>{{ props.row.is_active ? 'Deactivate' : 'Activate' }}</q-tooltip>
                  </q-btn>

                  <q-btn
                    @click="confirmDelete(props.row)"
                    icon="delete"
                    color="negative"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Delete Rate</q-tooltip>
                  </q-btn>
                </div>
              </q-td>
            </template>
          </q-table>

          <!-- Empty State -->
          <div v-if="rates.data.length === 0 && !loading" class="text-center q-pa-xl">
            <q-icon name="swap_horiz" size="4rem" color="grey-4" class="q-mb-md" />
            <div class="text-h6 text-grey-6 q-mb-sm">No exchange rates found</div>
            <div class="text-subtitle2 text-grey-5 q-mb-lg">
              Get started by adding your first exchange rate
            </div>
            <q-btn
              @click="showExchangeRateCreateModal = true"
              icon="add"
              label="Add Exchange Rate"
              color="primary"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Bulk Update Dialog -->
    <q-dialog v-model="showBulkUpdateDialog">
      <q-card style="min-width: 500px">
        <q-card-section>
          <div class="text-h6">Bulk Update Exchange Rates</div>
          <div class="text-subtitle2">Update {{ selectedRates.length }} selected rates</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input
            v-model="bulkUpdateData.multiplier"
            label="Rate Multiplier"
            outlined
            type="number"
            step="0.01"
            hint="Multiply all selected rates by this value"
            class="q-mb-md"
          />

          <q-input
            v-model="bulkUpdateData.notes"
            label="Update Notes"
            outlined
            type="textarea"
            rows="3"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            @click="performBulkUpdate"
            :loading="bulkUpdating"
            label="Update Rates"
            color="primary"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Rate History Dialog -->
    <q-dialog v-model="showHistoryDialog" maximized>
      <q-card>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Rate History</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section v-if="selectedRate">
          <div class="text-subtitle1 q-mb-md">
            {{ selectedRate.from_currency.code }} → {{ selectedRate.to_currency.code }}
          </div>

          <!-- History chart would go here -->
          <div class="chart-placeholder text-center q-pa-xl bg-grey-1 rounded">
            <q-icon name="show_chart" size="4rem" color="grey-4" class="q-mb-md" />
            <div class="text-h6 text-grey-6">Rate History Chart</div>
            <div class="text-subtitle2 text-grey-5">Chart implementation would go here</div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog">
      <q-card>
        <q-card-section>
          <div class="text-h6">Confirm Delete</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          Are you sure you want to delete the exchange rate for
          <strong>{{ selectedRate?.from_currency?.code }} → {{ selectedRate?.to_currency?.code }}</strong>?
          This action cannot be undone.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            @click="deleteRate"
            :loading="deleting"
            label="Delete"
            color="negative"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Rate Fetching Modal -->
    <RateFetchingModal
      v-model="showRateFetchingModal"
      :update-database="true"
      @completed="onRateFetchCompleted"
      @error="onRateFetchError"
    />

    <!-- Exchange Rate Create Modal -->
    <ExchangeRateCreateModal
      v-model="showExchangeRateCreateModal"
      :currencies="currencies"
      @created="onExchangeRateCreated"
      @error="onExchangeRateCreateError"
    />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import RateFetchingModal from '@/components/RateFetchingModal.vue'
import ExchangeRateCreateModal from '@/components/ExchangeRateCreateModal.vue'

const router = useRouter()
const $q = useQuasar()

// Props
const props = defineProps({
  rates: {
    type: Object,
    default: () => ({ data: [], total: 0 })
  },
  currencies: {
    type: Array,
    default: () => []
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

// Reactive data
const loading = ref(false)
const updating = ref(false)
const deleting = ref(false)
const bulkUpdating = ref(false)
const showBulkUpdateDialog = ref(false)
const showHistoryDialog = ref(false)
const showDeleteDialog = ref(false)
const showRateFetchingModal = ref(false)
const showExchangeRateCreateModal = ref(false)
const selectedRate = ref(null)
const selectedRates = ref([])

const filters = ref({
  from_currency_id: props.filters.from_currency_id || null,
  to_currency_id: props.filters.to_currency_id || null,
  is_active: props.filters.is_active || null,
  source: props.filters.source || null
})

const rates = ref(props.rates)
const currencies = ref(props.currencies)

const updateStatus = ref({
  last_update: null,
  is_stale: true
})

const bulkUpdateData = ref({
  multiplier: 1,
  notes: ''
})

const pagination = ref({
  page: 1,
  rowsPerPage: 15,
  rowsNumber: props.rates.total || 0
})

// Options
const statusOptions = [
  { label: 'Active', value: true },
  { label: 'Inactive', value: false }
]

const sourceOptions = [
  { label: 'API', value: 'api' },
  { label: 'Manual', value: 'manual' },
  { label: 'Automatic', value: 'automatic' }
]

// Table columns
const columns = [
  { name: 'pair', label: 'Currency Pair', field: 'pair', sortable: false, align: 'left' },
  { name: 'rate', label: 'Exchange Rate', field: 'rate', sortable: true, align: 'right' },
  { name: 'change', label: 'Change', field: 'change_percentage', sortable: true, align: 'center' },
  { name: 'status', label: 'Status', field: 'is_active', sortable: true, align: 'center' },
  { name: 'source', label: 'Source', field: 'source', sortable: true, align: 'center' },
  { name: 'updated', label: 'Last Updated', field: 'updated_at', sortable: true, align: 'center' },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: 'center' }
]

// Computed properties
const currencyOptions = computed(() => {
  return currencies.value.map(currency => ({
    ...currency,
    display_name: `${currency.flag_icon} ${currency.name} (${currency.code})`
  }))
})

const stats = computed(() => {
  const data = rates.value.data || []
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

  return {
    total: data.length,
    active: data.filter(r => r.is_active).length,
    stale: data.filter(r => new Date(r.updated_at) < oneHourAgo).length,
    api_updated: data.filter(r => r.source === 'api').length
  }
})

// Methods
const loadRates = async (requestProp = null) => {
  loading.value = true

  try {
    const params = {
      page: requestProp?.pagination?.page || pagination.value.page,
      per_page: requestProp?.pagination?.rowsPerPage || pagination.value.rowsPerPage,
      from_currency_id: filters.value.from_currency_id,
      to_currency_id: filters.value.to_currency_id,
      is_active: filters.value.is_active,
      source: filters.value.source
    }

    // Remove null/undefined values
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    const response = await axios.get('/api/admin/exchange-rates', { params })

    rates.value = response.data.rates
    pagination.value.page = response.data.rates.current_page
    pagination.value.rowsNumber = response.data.rates.total

  } catch (error) {
    console.error('Error loading rates:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load exchange rates'
    })
  } finally {
    loading.value = false
  }
}

const loadUpdateStatus = async () => {
  try {
    const response = await axios.get('/api/exchange/update-status')
    if (response.data.success) {
      updateStatus.value = response.data.data
    }
  } catch (error) {
    console.error('Error loading update status:', error)
  }
}

const onRequest = (props) => {
  loadRates(props)
}

const resetFilters = () => {
  filters.value = {
    from_currency_id: null,
    to_currency_id: null,
    is_active: null,
    source: null
  }
  loadRates()
}

const triggerRateUpdate = async () => {
  updating.value = true
  try {
    await axios.post('/api/admin/trigger-rate-update')
    await loadUpdateStatus()
    await loadRates()
    $q.notify({
      type: 'positive',
      message: 'Exchange rates updated successfully'
    })
  } catch (error) {
    console.error('Error updating rates:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to update exchange rates'
    })
  } finally {
    updating.value = false
  }
}

const toggleStatus = async (rate) => {
  try {
    await axios.patch(`/api/admin/exchange-rates/${rate.id}/toggle-status`)

    // Update local data
    const index = rates.value.data.findIndex(r => r.id === rate.id)
    if (index !== -1) {
      rates.value.data[index].is_active = !rates.value.data[index].is_active
    }

    $q.notify({
      type: 'positive',
      message: `Exchange rate ${rate.is_active ? 'deactivated' : 'activated'} successfully`
    })
  } catch (error) {
    console.error('Error toggling rate status:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to update rate status'
    })
  }
}

const confirmDelete = (rate) => {
  selectedRate.value = rate
  showDeleteDialog.value = true
}

const deleteRate = async () => {
  deleting.value = true

  try {
    await axios.delete(`/api/admin/exchange-rates/${selectedRate.value.id}`)

    // Remove from local data
    const index = rates.value.data.findIndex(r => r.id === selectedRate.value.id)
    if (index !== -1) {
      rates.value.data.splice(index, 1)
    }

    showDeleteDialog.value = false
    selectedRate.value = null

    $q.notify({
      type: 'positive',
      message: 'Exchange rate deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting rate:', error)
    $q.notify({
      type: 'negative',
      message: error.response?.data?.message || 'Failed to delete exchange rate'
    })
  } finally {
    deleting.value = false
  }
}

const performBulkUpdate = async () => {
  bulkUpdating.value = true

  try {
    const rateUpdates = selectedRates.value.map(rate => ({
      id: rate.id,
      rate: rate.rate * parseFloat(bulkUpdateData.value.multiplier)
    }))

    await axios.patch('/api/admin/exchange-rates/bulk-update', {
      rates: rateUpdates,
      notes: bulkUpdateData.value.notes
    })

    showBulkUpdateDialog.value = false
    selectedRates.value = []
    bulkUpdateData.value = { multiplier: 1, notes: '' }

    await loadRates()

    $q.notify({
      type: 'positive',
      message: 'Exchange rates updated successfully'
    })
  } catch (error) {
    console.error('Error performing bulk update:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to update exchange rates'
    })
  } finally {
    bulkUpdating.value = false
  }
}

const showRateHistory = (rate) => {
  selectedRate.value = rate
  showHistoryDialog.value = true
}

const exportRates = () => {
  // Implementation for exporting rates
  $q.notify({
    type: 'info',
    message: 'Export functionality would be implemented here'
  })
}

const formatRate = (rate) => {
  return parseFloat(rate).toFixed(4)
}

const formatDateTime = (datetime) => {
  return new Date(datetime).toLocaleString()
}

const getTimeAgo = (datetime) => {
  const now = new Date()
  const date = new Date(datetime)
  const diffMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  return `${Math.floor(diffMinutes / 1440)}d ago`
}

const formatLastUpdate = (lastUpdate) => {
  if (!lastUpdate) return 'Never'
  return new Date(lastUpdate).toLocaleString()
}

const getChangeColor = (changePercentage) => {
  if (changePercentage > 0) return 'positive'
  if (changePercentage < 0) return 'negative'
  return 'grey'
}

const getSourceColor = (source) => {
  switch (source) {
    case 'api': return 'blue'
    case 'automatic': return 'green'
    case 'manual': return 'orange'
    default: return 'grey'
  }
}

const onRateFetchCompleted = async (result) => {
  showRateFetchingModal.value = false

  if (result.success) {
    await loadRates()
    await loadUpdateStatus()

    $q.notify({
      type: 'positive',
      message: `Successfully updated ${result.data.updated_count} exchange rates from multiple sources`,
      timeout: 5000
    })
  }
}

const onRateFetchError = (error) => {
  showRateFetchingModal.value = false

  $q.notify({
    type: 'negative',
    message: error.error || 'Failed to fetch rates from multiple sources',
    timeout: 5000
  })
}

const onExchangeRateCreated = async (result) => {
  showExchangeRateCreateModal.value = false

  if (result.success) {
    await loadRates()

    $q.notify({
      type: 'positive',
      message: `Exchange rate for ${result.rate.from_currency.code}/${result.rate.to_currency.code} created successfully`,
      timeout: 5000
    })
  }
}

const onExchangeRateCreateError = (error) => {
  showExchangeRateCreateModal.value = false

  $q.notify({
    type: 'negative',
    message: error.error || 'Failed to create exchange rate',
    timeout: 5000
  })
}

// Watchers
watch(() => filters.value, () => {
  setTimeout(() => {
    loadRates()
  }, 300)
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (!rates.value.data.length) {
    loadRates()
  }
  loadUpdateStatus()
})
</script>

<style scoped>
.exchange-rates-admin {
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  border-bottom: 1px solid #e0e0e0;
}

.filter-card, .table-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.chart-placeholder {
  border: 2px dashed #e0e0e0;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
}
</style>
