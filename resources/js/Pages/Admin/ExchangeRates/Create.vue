<template>
  <q-page class="exchange-rate-create">
    <!-- Header -->
    <div class="page-header q-pa-lg bg-white shadow-1">
      <div class="row items-center">
        <div class="col">
          <h1 class="text-h4 q-mb-sm">Add New Exchange Rate</h1>
          <p class="text-subtitle1 text-grey-6">Create a new exchange rate between currencies</p>
        </div>
        <div class="col-auto">
          <q-btn
            @click="$inertia.visit(route('admin.currency-exchange-rates.index'))"
            icon="arrow_back"
            label="Back to Exchange Rates"
            color="grey"
            outline
          />
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <div class="row justify-center">
        <div class="col-12 col-md-8 col-lg-6">
          <q-card class="form-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-lg">Exchange Rate Information</h6>

              <q-form @submit="submitForm" class="q-gutter-md">
                <!-- Currency Pair Selection -->
                <div class="row q-gutter-md">
                  <div class="col-12 col-sm-6">
                    <q-select
                      v-model="form.from_currency_id"
                      :options="currencyOptions"
                      option-value="id"
                      option-label="display_name"
                      label="From Currency *"
                      outlined
                      :error="!!errors.from_currency_id"
                      :error-message="errors.from_currency_id"
                      hint="Select the source currency"
                      @update:model-value="checkExistingRate"
                    >
                      <template v-slot:option="scope">
                        <q-item v-bind="scope.itemProps">
                          <q-item-section avatar>
                            <span class="text-lg">{{ scope.opt.flag_icon }}</span>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>{{ scope.opt.name }}</q-item-label>
                            <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </template>
                    </q-select>
                  </div>

                  <div class="col-12 col-sm-6">
                    <q-select
                      v-model="form.to_currency_id"
                      :options="filteredToCurrencies"
                      option-value="id"
                      option-label="display_name"
                      label="To Currency *"
                      outlined
                      :error="!!errors.to_currency_id"
                      :error-message="errors.to_currency_id"
                      hint="Select the target currency"
                      @update:model-value="checkExistingRate"
                    >
                      <template v-slot:option="scope">
                        <q-item v-bind="scope.itemProps">
                          <q-item-section avatar>
                            <span class="text-lg">{{ scope.opt.flag_icon }}</span>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>{{ scope.opt.name }}</q-item-label>
                            <q-item-label caption>{{ scope.opt.code }}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </template>
                    </q-select>
                  </div>
                </div>

                <!-- Existing Rate Warning -->
                <q-banner
                  v-if="existingRateWarning"
                  class="bg-warning text-white q-mb-md"
                  rounded
                >
                  <template v-slot:avatar>
                    <q-icon name="warning" />
                  </template>
                  An exchange rate already exists for this currency pair. Creating this rate will replace the existing one.
                </q-banner>

                <!-- Exchange Rate -->
                <div class="row q-gutter-md">
                  <div class="col-12 col-sm-8">
                    <q-input
                      v-model.number="form.rate"
                      label="Exchange Rate *"
                      outlined
                      type="number"
                      step="0.00000001"
                      min="0.00000001"
                      :error="!!errors.rate"
                      :error-message="errors.rate"
                      hint="1 unit of source currency = X units of target currency"
                    >
                      <template v-slot:prepend>
                        <q-icon name="swap_horiz" />
                      </template>
                    </q-input>
                  </div>

                  <div class="col-12 col-sm-4">
                    <q-btn
                      @click="fetchCurrentRate"
                      :loading="fetchingRate"
                      icon="refresh"
                      label="Fetch Rate"
                      color="secondary"
                      outline
                      class="full-width"
                      :disable="!canFetchRate"
                    />
                  </div>
                </div>

                <!-- Rate Preview -->
                <div v-if="ratePreview" class="rate-preview q-pa-md bg-blue-1 rounded-borders">
                  <div class="text-subtitle2 q-mb-xs">Rate Preview</div>
                  <div class="row items-center">
                    <div class="col">
                      <div class="text-body1">
                        1 {{ fromCurrency?.code }} = {{ formatRate(form.rate) }} {{ toCurrency?.code }}
                      </div>
                      <div class="text-caption text-grey-6">
                        {{ formatAmount(100, fromCurrency?.code) }} = {{ formatAmount(100 * form.rate, toCurrency?.code) }}
                      </div>
                    </div>
                    <div class="col-auto">
                      <q-icon name="trending_up" size="md" color="positive" />
                    </div>
                  </div>
                </div>

                <!-- Notes -->
                <q-input
                  v-model="form.notes"
                  label="Notes"
                  outlined
                  type="textarea"
                  rows="3"
                  :error="!!errors.notes"
                  :error-message="errors.notes"
                  hint="Optional notes about this exchange rate"
                />

                <!-- Status -->
                <div class="q-gutter-sm">
                  <q-checkbox
                    v-model="form.is_active"
                    label="Active Exchange Rate"
                    color="positive"
                  />
                  <div class="text-caption text-grey-6 q-ml-lg">
                    Active rates can be used for currency conversions
                  </div>
                </div>

                <!-- Form Actions -->
                <div class="row q-gutter-sm q-mt-lg">
                  <div class="col">
                    <q-btn
                      @click="$inertia.visit(route('admin.currency-exchange-rates.index'))"
                      label="Cancel"
                      color="grey"
                      outline
                      class="full-width"
                    />
                  </div>
                  <div class="col">
                    <q-btn
                      type="submit"
                      :loading="submitting"
                      label="Create Exchange Rate"
                      color="primary"
                      class="full-width"
                    />
                  </div>
                </div>
              </q-form>
            </q-card-section>
          </q-card>

          <!-- Rate Calculator -->
          <q-card v-if="form.rate && fromCurrency && toCurrency" class="calculator-card q-mt-lg">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Rate Calculator</h6>

              <div class="row q-gutter-md">
                <div class="col-12 col-sm-6">
                  <q-input
                    v-model.number="calculatorAmount"
                    label="Amount"
                    outlined
                    type="number"
                    step="0.01"
                    min="0"
                  >
                    <template v-slot:append>
                      <span class="text-caption">{{ fromCurrency.code }}</span>
                    </template>
                  </q-input>
                </div>

                <div class="col-12 col-sm-6">
                  <q-input
                    :model-value="convertedAmount"
                    label="Converted Amount"
                    outlined
                    readonly
                  >
                    <template v-slot:append>
                      <span class="text-caption">{{ toCurrency.code }}</span>
                    </template>
                  </q-input>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { router } from '@inertiajs/vue3'
import axios from 'axios'

const $router = useRouter()
const $q = useQuasar()

// Props
const props = defineProps({
  currencies: {
    type: Array,
    default: () => []
  }
})

// Form data
const form = ref({
  from_currency_id: null,
  to_currency_id: null,
  rate: null,
  notes: '',
  is_active: true
})

const errors = ref({})
const submitting = ref(false)
const fetchingRate = ref(false)
const existingRateWarning = ref(false)
const calculatorAmount = ref(1)

// Computed properties
const currencyOptions = computed(() => {
  return props.currencies.map(currency => ({
    ...currency,
    display_name: `${currency.flag_icon} ${currency.name} (${currency.code})`
  }))
})

const filteredToCurrencies = computed(() => {
  return currencyOptions.value.filter(currency =>
    currency.id !== form.value.from_currency_id
  )
})

const fromCurrency = computed(() => {
  return props.currencies.find(c => c.id === form.value.from_currency_id)
})

const toCurrency = computed(() => {
  return props.currencies.find(c => c.id === form.value.to_currency_id)
})

const canFetchRate = computed(() => {
  return form.value.from_currency_id && form.value.to_currency_id
})

const ratePreview = computed(() => {
  return form.value.rate && fromCurrency.value && toCurrency.value
})

const convertedAmount = computed(() => {
  if (calculatorAmount.value && form.value.rate) {
    return (calculatorAmount.value * form.value.rate).toFixed(4)
  }
  return 0
})

// Methods
const submitForm = async () => {
  submitting.value = true
  errors.value = {}

  try {
    router.post('/admin/currency-exchange-rates', form.value, {
      onSuccess: () => {
        $q.notify({
          type: 'positive',
          message: 'Exchange rate created successfully',
          position: 'top'
        })
      },
      onError: (formErrors) => {
        errors.value = formErrors
        $q.notify({
          type: 'negative',
          message: 'Please check the form for errors',
          position: 'top'
        })
      }
    })
  } catch (error) {
    console.error('Error creating exchange rate:', error)
    $q.notify({
      type: 'negative',
      message: 'An error occurred while creating the exchange rate',
      position: 'top'
    })
  } finally {
    submitting.value = false
  }
}

const checkExistingRate = async () => {
  if (form.value.from_currency_id && form.value.to_currency_id) {
    try {
      const response = await axios.get('/admin/currency-exchange-rates/get-rate', {
        params: {
          from_currency_id: form.value.from_currency_id,
          to_currency_id: form.value.to_currency_id
        }
      })

      if (response.data.rate) {
        existingRateWarning.value = true
      } else {
        existingRateWarning.value = false
      }
    } catch (error) {
      existingRateWarning.value = false
    }
  }
}

const fetchCurrentRate = async () => {
  if (!canFetchRate.value) return

  fetchingRate.value = true
  try {
    // This would typically call an external API to get current rates
    // For now, we'll simulate it
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock rate based on currency codes
    let mockRate = 1
    if (fromCurrency.value?.code === 'USD' && toCurrency.value?.code === 'IQD') {
      mockRate = 1310.50
    } else if (fromCurrency.value?.code === 'USD' && toCurrency.value?.code === 'EUR') {
      mockRate = 0.85
    } else if (fromCurrency.value?.code === 'EUR' && toCurrency.value?.code === 'USD') {
      mockRate = 1.18
    } else {
      mockRate = Math.random() * 10 + 0.1
    }

    form.value.rate = parseFloat(mockRate.toFixed(8))

    $q.notify({
      type: 'positive',
      message: 'Current rate fetched successfully',
      position: 'top'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch current rate',
      position: 'top'
    })
  } finally {
    fetchingRate.value = false
  }
}

const formatRate = (rate) => {
  return parseFloat(rate).toFixed(4)
}

const formatAmount = (amount, currencyCode) => {
  const currency = props.currencies.find(c => c.code === currencyCode)
  const decimals = currency?.decimal_places || 2
  return parseFloat(amount).toFixed(decimals)
}

// Watchers
watch(() => [form.value.from_currency_id, form.value.to_currency_id], () => {
  if (form.value.from_currency_id === form.value.to_currency_id) {
    form.value.to_currency_id = null
  }
})
</script>

<style scoped>
.exchange-rate-create {
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  border-bottom: 1px solid #e0e0e0;
}

.form-card, .calculator-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.rate-preview {
  border: 1px solid #e3f2fd;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
}
</style>
