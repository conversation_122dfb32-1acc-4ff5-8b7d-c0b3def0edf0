<template>
  <q-page class="role-show">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">{{ role.name }}</h1>
            <p class="header-subtitle">Role details and permissions</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="$inertia.visit($route('admin.roles.edit', role.id))"
              icon="edit"
              label="Edit Role"
              color="warning"
              size="lg"
              class="q-mr-sm"
              no-caps
              unelevated
            />
            <q-btn
              @click="$inertia.visit($route('admin.roles.index'))"
              icon="arrow_back"
              label="Back to Roles"
              color="grey-7"
              size="lg"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <div class="row q-gutter-lg">
        <!-- Role Information -->
        <div class="col-12 col-md-4">
          <q-card class="info-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Role Information</h6>
              
              <div class="info-item q-mb-md">
                <div class="info-label">Role Name</div>
                <div class="info-value">{{ role.name }}</div>
              </div>

              <div class="info-item q-mb-md">
                <div class="info-label">Total Permissions</div>
                <div class="info-value">
                  <q-chip color="blue" text-color="white" size="sm">
                    {{ role.permissions?.length || 0 }} permissions
                  </q-chip>
                </div>
              </div>

              <div class="info-item q-mb-md">
                <div class="info-label">Users with this Role</div>
                <div class="info-value">
                  <q-chip color="green" text-color="white" size="sm">
                    {{ role.users?.length || 0 }} users
                  </q-chip>
                </div>
              </div>

              <div class="info-item">
                <div class="info-label">Created At</div>
                <div class="info-value">{{ formatDate(role.created_at) }}</div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Permissions -->
        <div class="col-12 col-md-8">
          <q-card class="permissions-card">
            <q-card-section>
              <h6 class="q-ma-none q-mb-md">Assigned Permissions</h6>
              
              <div v-if="groupedPermissions && Object.keys(groupedPermissions).length > 0">
                <div v-for="(permissions, groupName) in groupedPermissions" :key="groupName" class="permission-group q-mb-lg">
                  <div class="group-header q-mb-sm">
                    <h6 class="group-title">{{ groupName }} Permissions</h6>
                    <q-chip color="primary" text-color="white" size="xs">
                      {{ permissions.length }} permissions
                    </q-chip>
                  </div>
                  
                  <div class="permission-list">
                    <q-chip
                      v-for="permission in permissions"
                      :key="permission.id"
                      color="blue-1"
                      text-color="blue-8"
                      size="sm"
                      class="q-ma-xs"
                    >
                      <q-icon name="key" size="xs" class="q-mr-xs" />
                      {{ permission.name }}
                    </q-chip>
                  </div>
                </div>
              </div>

              <div v-else class="text-center q-pa-xl">
                <q-icon name="key" size="4rem" color="grey-4" class="q-mb-md" />
                <div class="text-h6 text-grey-6 q-mb-sm">No permissions assigned</div>
                <div class="text-subtitle2 text-grey-5 q-mb-lg">
                  This role doesn't have any permissions assigned yet.
                </div>
                <q-btn
                  @click="$inertia.visit($route('admin.roles.assign-permissions', role.id))"
                  icon="add"
                  label="Assign Permissions"
                  color="primary"
                />
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  role: {
    type: Object,
    required: true
  }
})

// Group permissions by category
const groupedPermissions = computed(() => {
  if (!props.role.permissions || props.role.permissions.length === 0) {
    return {}
  }

  return props.role.permissions.reduce((groups, permission) => {
    const group = permission.name.split(' ')[1] || 'general'
    if (!groups[group]) {
      groups[group] = []
    }
    groups[group].push(permission)
    return groups
  }, {})
})

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}
</script>

<style scoped>
.role-show {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.info-card, .permissions-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.info-item {
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.permission-group {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  background: #f9fafb;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  text-transform: capitalize;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }
  
  .header-title {
    font-size: 2rem;
  }
  
  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
}
</style>
