<template>
    <AdminLayout>
        <Head title="Create Role" />

        <div class="py-6">
            <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="mb-6 flex justify-between items-center">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Create Role</h1>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Create a new role and assign permissions
                        </p>
                    </div>
                    <Link
                        :href="route('admin.roles.index')"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
                    >
                        <ArrowLeftIcon class="w-4 h-4 mr-2" />
                        Back to Roles
                    </Link>
                </div>

                <!-- Form -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                    <form @submit.prevent="submit" class="p-6 space-y-6">
                        <!-- Role Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Role Name
                            </label>
                            <input
                                id="name"
                                v-model="form.name"
                                type="text"
                                class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                :class="{ 'border-red-500': form.errors.name }"
                                required
                            />
                            <div v-if="form.errors.name" class="mt-2 text-sm text-red-600">
                                {{ form.errors.name }}
                            </div>
                        </div>

                        <!-- Permissions -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                                Permissions
                            </label>
                            
                            <div class="space-y-6">
                                <div v-for="(groupPermissions, groupName) in permissions" :key="groupName" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white capitalize">
                                            {{ groupName }} Permissions
                                        </h3>
                                        <div class="flex space-x-2">
                                            <button
                                                type="button"
                                                @click="selectAllInGroup(groupPermissions)"
                                                class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                            >
                                                Select All
                                            </button>
                                            <button
                                                type="button"
                                                @click="deselectAllInGroup(groupPermissions)"
                                                class="text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
                                            >
                                                Deselect All
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                                        <div v-for="permission in groupPermissions" :key="permission.id" class="flex items-center">
                                            <input
                                                :id="`permission-${permission.id}`"
                                                v-model="form.permissions"
                                                :value="permission.id"
                                                type="checkbox"
                                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            />
                                            <label
                                                :for="`permission-${permission.id}`"
                                                class="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize"
                                            >
                                                {{ permission.name }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div v-if="form.errors.permissions" class="mt-2 text-sm text-red-600">
                                {{ form.errors.permissions }}
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                            <Link
                                :href="route('admin.roles.index')"
                                class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md font-semibold text-xs text-gray-700 dark:text-gray-300 uppercase tracking-widest shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                :disabled="form.processing"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"
                            >
                                <span v-if="form.processing">Creating...</span>
                                <span v-else>Create Role</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
    permissions: Object,
})

const form = useForm({
    name: '',
    permissions: [],
})

const submit = () => {
    form.post(route('admin.roles.store'))
}

const selectAllInGroup = (groupPermissions) => {
    groupPermissions.forEach(permission => {
        if (!form.permissions.includes(permission.id)) {
            form.permissions.push(permission.id)
        }
    })
}

const deselectAllInGroup = (groupPermissions) => {
    groupPermissions.forEach(permission => {
        const index = form.permissions.indexOf(permission.id)
        if (index > -1) {
            form.permissions.splice(index, 1)
        }
    })
}
</script>
