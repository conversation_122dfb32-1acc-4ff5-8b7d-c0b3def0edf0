<template>
  <q-page class="roles-admin">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">Roles Management</h1>
            <p class="header-subtitle">Manage user roles and their permissions</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="$inertia.visit($route('admin.roles.create'))"
              icon="add"
              label="Create Role"
              color="primary"
              size="lg"
              class="add-role-btn"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <!-- Search and Filters -->
      <q-card class="modern-filter-card q-mb-lg">
        <q-card-section>
          <div class="filter-header q-mb-md">
            <h6 class="filter-title">Search & Filter Roles</h6>
          </div>

          <div class="row q-gutter-md items-end">
            <div class="col-12 col-sm-6 col-md-4">
              <q-input
                v-model="filters.search"
                label="Search roles..."
                outlined
                clearable
                debounce="300"
                @update:model-value="loadRoles"
                class="modern-input"
              >
                <template v-slot:prepend>
                  <q-icon name="search" color="primary" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-btn
                @click="resetFilters"
                icon="clear_all"
                label="Reset"
                color="grey-7"
                outline
                class="full-width modern-btn"
                no-caps
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Roles Table -->
      <q-card class="table-card">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <div class="col">
              <h6 class="q-ma-none">Roles List</h6>
            </div>
          </div>

          <q-table
            :rows="roles.data"
            :columns="columns"
            row-key="id"
            :loading="loading"
            :pagination="pagination"
            @request="onRequest"
            flat
            bordered
          >
            <template v-slot:body-cell-name="props">
              <q-td :props="props">
                <div class="flex items-center">
                  <q-icon name="admin_panel_settings" color="primary" size="sm" class="q-mr-sm" />
                  <div class="text-weight-medium">{{ props.row.name }}</div>
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-permissions="props">
              <q-td :props="props">
                <q-chip
                  color="blue"
                  text-color="white"
                  size="sm"
                >
                  {{ props.row.permissions?.length || 0 }} permissions
                </q-chip>
              </q-td>
            </template>

            <template v-slot:body-cell-users="props">
              <q-td :props="props">
                {{ props.row.users_count || 0 }} users
              </q-td>
            </template>

            <template v-slot:body-cell-created_at="props">
              <q-td :props="props">
                <div class="text-caption">
                  {{ formatDate(props.row.created_at) }}
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <div class="q-gutter-xs">
                  <q-btn
                    @click="$inertia.visit($route('admin.roles.show', props.row.id))"
                    icon="visibility"
                    color="primary"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>View Role</q-tooltip>
                  </q-btn>

                  <q-btn
                    @click="$inertia.visit($route('admin.roles.edit', props.row.id))"
                    icon="edit"
                    color="warning"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Edit Role</q-tooltip>
                  </q-btn>

                  <q-btn
                    @click="$inertia.visit($route('admin.roles.assign-permissions', props.row.id))"
                    icon="key"
                    color="positive"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Assign Permissions</q-tooltip>
                  </q-btn>

                  <q-btn
                    v-if="props.row.name !== 'Super Admin'"
                    @click="confirmDelete(props.row)"
                    icon="delete"
                    color="negative"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Delete Role</q-tooltip>
                  </q-btn>
                </div>
              </q-td>
            </template>
          </q-table>

          <!-- Empty State -->
          <div v-if="roles.data.length === 0 && !loading" class="text-center q-pa-xl">
            <q-icon name="admin_panel_settings" size="4rem" color="grey-4" class="q-mb-md" />
            <div class="text-h6 text-grey-6 q-mb-sm">No roles found</div>
            <div class="text-subtitle2 text-grey-5 q-mb-lg">
              {{ filters.search ? 'Try adjusting your search criteria' : 'Get started by creating your first role' }}
            </div>
            <q-btn
              @click="$inertia.visit($route('admin.roles.create'))"
              icon="add"
              label="Create Role"
              color="primary"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog">
      <q-card>
        <q-card-section>
          <div class="text-h6">Confirm Delete</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          Are you sure you want to delete the role <strong>{{ selectedRole?.name }}</strong>?
          This action cannot be undone.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            @click="deleteRole"
            :loading="deleting"
            label="Delete"
            color="negative"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// Props
const props = defineProps({
  roles: {
    type: Object,
    default: () => ({ data: [], total: 0 })
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

// Reactive data
const loading = ref(false)
const deleting = ref(false)
const showDeleteDialog = ref(false)
const selectedRole = ref(null)

const filters = ref({
  search: props.filters.search || ''
})

const roles = ref(props.roles)

const pagination = ref({
  page: 1,
  rowsPerPage: 15,
  rowsNumber: props.roles.total || 0
})

// Table columns
const columns = [
  { name: 'name', label: 'Role Name', field: 'name', sortable: true, align: 'left' },
  { name: 'permissions', label: 'Permissions', field: 'permissions', sortable: false, align: 'center' },
  { name: 'users', label: 'Users', field: 'users_count', sortable: true, align: 'center' },
  { name: 'created_at', label: 'Created At', field: 'created_at', sortable: true, align: 'center' },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: 'center' }
]

// Methods
const loadRoles = async () => {
  loading.value = true
  try {
    // This would typically make an API call
    // For now, we'll use the props data
  } catch (error) {
    console.error('Error loading roles:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load roles'
    })
  } finally {
    loading.value = false
  }
}

const onRequest = () => {
  loadRoles()
}

const resetFilters = () => {
  filters.value = {
    search: ''
  }
  loadRoles()
}

const confirmDelete = (role) => {
  selectedRole.value = role
  showDeleteDialog.value = true
}

const deleteRole = async () => {
  deleting.value = true
  try {
    // This would make a delete request
    await $inertia.delete($route('admin.roles.destroy', selectedRole.value.id))
    showDeleteDialog.value = false
    $q.notify({
      type: 'positive',
      message: 'Role deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting role:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to delete role'
    })
  } finally {
    deleting.value = false
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
/* Use the same modern styles from Currency Index */
.roles-admin {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.add-role-btn {
  border-radius: 12px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.add-role-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* Enhanced Filter Card */
.modern-filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.filter-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

/* Enhanced Cards */
.table-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .add-role-btn {
    width: 100%;
  }
}
</style>
