<template>
  <q-page class="users-admin">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">User Management</h1>
            <p class="header-subtitle">Manage admin users, roles, and permissions</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="showCreateUserDialog = true"
              icon="person_add"
              label="Add User"
              color="primary"
              size="lg"
              class="add-user-btn"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="page-content">
      <!-- Enhanced Statistics Cards -->
      <div class="row q-gutter-lg q-mb-xl">
        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-primary">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="people" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStats.total }}</div>
                <div class="stat-label">Total Users</div>
                <div class="stat-change positive">
                  <q-icon name="trending_up" size="14px" />
                  {{ userStats.active }} active
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-success">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="admin_panel_settings" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStats.admins }}</div>
                <div class="stat-label">Administrators</div>
                <div class="stat-change positive">
                  <q-icon name="security" size="14px" />
                  Full access
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-warning">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="schedule" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStats.online }}</div>
                <div class="stat-label">Online Now</div>
                <div class="stat-change positive">
                  <q-icon name="circle" size="14px" />
                  Active sessions
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-accent">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="login" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStats.todayLogins }}</div>
                <div class="stat-label">Logins Today</div>
                <div class="stat-change neutral">
                  <q-icon name="today" size="14px" />
                  Last 24h
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Enhanced Filter Card -->
      <q-card class="modern-filter-card q-mb-lg">
        <q-card-section>
          <div class="filter-header q-mb-md">
            <h6 class="filter-title">Search & Filter Users</h6>
            <q-chip
              v-if="hasActiveFilters"
              @click="resetFilters"
              removable
              color="primary"
              text-color="white"
              size="sm"
            >
              {{ activeFiltersCount }} filter(s) active
            </q-chip>
          </div>

          <div class="row q-gutter-md items-end">
            <div class="col-12 col-sm-6 col-md-4">
              <q-input
                v-model="searchForm.search"
                label="Search users..."
                outlined
                clearable
                debounce="300"
                @update:model-value="performSearch"
                class="modern-input"
              >
                <template v-slot:prepend>
                  <q-icon name="search" color="primary" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="searchForm.role"
                :options="roleOptions"
                label="Role"
                outlined
                clearable
                @update:model-value="performSearch"
                class="modern-select"
              >
                <template v-slot:prepend>
                  <q-icon name="badge" color="secondary" />
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="searchForm.status"
                :options="statusOptions"
                label="Status"
                outlined
                clearable
                @update:model-value="performSearch"
                class="modern-select"
              >
                <template v-slot:prepend>
                  <q-icon name="toggle_on" color="positive" />
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-btn
                @click="resetFilters"
                icon="clear_all"
                label="Reset"
                color="grey-7"
                outline
                class="full-width modern-btn"
                no-caps
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Users Table -->
      <q-card class="table-card">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <div class="col">
              <h6 class="q-ma-none">Users List</h6>
            </div>
            <div class="col-auto">
              <q-btn-toggle
                v-model="viewMode"
                :options="[
                  { label: 'Table', value: 'table', icon: 'table_view' },
                  { label: 'Cards', value: 'cards', icon: 'grid_view' }
                ]"
                color="primary"
                outline
                size="sm"
              />
            </div>
          </div>

          <!-- Table View -->
          <div v-if="viewMode === 'table'">
            <q-table
              :rows="props.users?.data || []"
              :columns="columns"
              row-key="id"
              :loading="loading"
              :pagination="paginationData"
              @request="onRequest"
              flat
              bordered
            >
              <template v-slot:body-cell-avatar="props">
                <q-td :props="props">
                  <q-avatar size="40px">
                    <img v-if="props.row.avatar" :src="props.row.avatar" />
                    <q-icon v-else name="person" />
                  </q-avatar>
                </q-td>
              </template>

              <template v-slot:body-cell-name="props">
                <q-td :props="props">
                  <div>
                    <div class="text-weight-medium">{{ props.row.name }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.email }}</div>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-role="props">
                <q-td :props="props">
                  <q-chip
                    :color="getRoleColor(props.row.role)"
                    text-color="white"
                    size="sm"
                  >
                    {{ props.row.role }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <div class="status-indicator">
                    <q-icon
                      :name="props.row.is_online ? 'circle' : 'radio_button_unchecked'"
                      :color="props.row.is_online ? 'positive' : 'grey'"
                      size="12px"
                    />
                    <span class="q-ml-xs">
                      {{ props.row.is_online ? 'Online' : 'Offline' }}
                    </span>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-last_login="props">
                <q-td :props="props">
                  <div class="text-caption">
                    {{ formatLastLogin(props.row.last_login) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <div class="q-gutter-xs">
                    <q-btn
                      @click="editUser(props.row)"
                      icon="edit"
                      color="primary"
                      flat
                      round
                      size="sm"
                    >
                      <q-tooltip>Edit User</q-tooltip>
                    </q-btn>

                    <q-btn
                      @click="toggleUserStatus(props.row)"
                      :icon="props.row.is_active ? 'block' : 'check_circle'"
                      :color="props.row.is_active ? 'warning' : 'positive'"
                      flat
                      round
                      size="sm"
                    >
                      <q-tooltip>{{ props.row.is_active ? 'Deactivate' : 'Activate' }}</q-tooltip>
                    </q-btn>

                    <q-btn
                      @click="viewUserActivity(props.row)"
                      icon="history"
                      color="info"
                      flat
                      round
                      size="sm"
                    >
                      <q-tooltip>View Activity</q-tooltip>
                    </q-btn>
                  </div>
                </q-td>
              </template>
            </q-table>
          </div>

          <!-- Cards View -->
          <div v-else class="row q-gutter-md">
            <div
              v-for="user in props.users?.data || []"
              :key="user.id"
              class="col-12 col-sm-6 col-md-4 col-lg-3"
            >
              <UserCard
                :user="user"
                @edit="editUser"
                @toggle-status="toggleUserStatus"
                @view-activity="viewUserActivity"
              />
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="(props.users?.data?.length || 0) === 0 && !loading" class="text-center q-pa-xl">
            <q-icon name="people" size="4rem" color="grey-4" class="q-mb-md" />
            <div class="text-h6 text-grey-6 q-mb-sm">No users found</div>
            <div class="text-subtitle2 text-grey-5 q-mb-lg">
              {{ searchForm.search ? 'Try adjusting your search criteria' : 'Get started by adding your first user' }}
            </div>
            <q-btn
              @click="showCreateUserDialog = true"
              icon="person_add"
              label="Add User"
              color="primary"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Create User Dialog -->
    <CreateUserDialog
      v-model="showCreateUserDialog"
      @created="onUserCreated"
    />

    <!-- Edit User Dialog -->
    <EditUserDialog
      v-model="showEditUserDialog"
      :user="selectedUser"
      @updated="onUserUpdated"
    />

    <!-- User Activity Dialog -->
    <UserActivityDialog
      v-model="showActivityDialog"
      :user="selectedUser"
    />
  </q-page>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { router, usePage } from '@inertiajs/vue3'
import route from 'ziggy-js'
import UserCard from '@/components/Admin/Users/<USER>'
import CreateUserDialog from '@/components/Admin/Users/<USER>'
import EditUserDialog from '@/components/Admin/Users/<USER>'
import UserActivityDialog from '@/components/Admin/Users/<USER>'

const $q = useQuasar()
const $page = usePage()

// Props from Inertia
const props = defineProps({
  users: Object,
  roles: Array,
  userStats: Object,
  filters: Object,
})









// Reactive data
const loading = ref(false)
const showCreateUserDialog = ref(false)
const showEditUserDialog = ref(false)
const showActivityDialog = ref(false)
const selectedUser = ref(null)
const viewMode = ref('table')

// Search and filter state
const searchForm = ref({
  search: props.filters?.search || '',
  role: props.filters?.role || '',
  status: props.filters?.status || '',
})

// Computed properties
const paginationData = computed(() => ({
  page: props.users?.current_page || 1,
  rowsPerPage: props.users?.per_page || 15,
  rowsNumber: props.users?.total || 0
}))

// Role options for filter
const roleOptions = computed(() => [
  { label: 'All Roles', value: '' },
  ...(props.roles || []).map(role => ({
    label: role.name,
    value: role.name
  }))
])

// Status options for filter
const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' }
]

// Table columns
const columns = [
  { name: 'avatar', label: '', field: 'avatar', sortable: false, align: 'center' },
  { name: 'name', label: 'Name', field: 'name', sortable: true, align: 'left' },
  { name: 'role', label: 'Role', field: 'role', sortable: true, align: 'center' },
  { name: 'status', label: 'Status', field: 'is_online', sortable: true, align: 'center' },
  { name: 'last_login', label: 'Last Login', field: 'last_login', sortable: true, align: 'center' },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: 'center' }
]

// Computed properties for filters
const hasActiveFilters = computed(() => {
  return searchForm.value.search ||
         searchForm.value.role ||
         searchForm.value.status
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (searchForm.value.search) count++
  if (searchForm.value.role) count++
  if (searchForm.value.status) count++
  return count
})

// Methods
const onRequest = (requestProp) => {
  const params = {
    page: requestProp.pagination.page,
    search: searchForm.value.search,
    role: searchForm.value.role,
    status: searchForm.value.status,
  }

  // Remove empty values
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null) {
      delete params[key]
    }
  })

  router.get(route('admin.users.index'), params, {
    preserveState: true,
    preserveScroll: true,
  })
}

const performSearch = () => {
  const params = {
    search: searchForm.value.search,
    role: searchForm.value.role,
    status: searchForm.value.status,
  }

  // Remove empty values
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null) {
      delete params[key]
    }
  })

  router.get(route('admin.users.index'), params, {
    preserveState: true,
    preserveScroll: true,
  })
}

const resetFilters = () => {
  searchForm.value = {
    search: '',
    role: '',
    status: ''
  }

  router.get(route('admin.users.index'), {}, {
    preserveState: true,
    preserveScroll: true,
  })
}

const getRoleColor = (role) => {
  if (!role) return 'grey'

  const colors = {
    'Super Admin': 'deep-purple',
    'Admin': 'primary',
    'Manager': 'secondary',
    'Editor': 'accent',
    'Viewer': 'info',
    'User': 'grey'
  }
  return colors[role] || 'grey'
}

const formatLastLogin = (lastLogin) => {
  if (!lastLogin) return 'Never'

  const now = new Date()
  const login = new Date(lastLogin)
  const diffMinutes = Math.floor((now - login) / (1000 * 60))

  if (diffMinutes < 1) return 'Just now'
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`
  return login.toLocaleDateString()
}

const editUser = (user) => {
  selectedUser.value = user
  showEditUserDialog.value = true
}

const toggleUserStatus = (user) => {
  router.patch(route('admin.users.toggle-status', user.id), {}, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      $q.notify({
        type: 'positive',
        message: `User ${user.is_active ? 'deactivated' : 'activated'} successfully`
      })
    },
    onError: () => {
      $q.notify({
        type: 'negative',
        message: 'Failed to update user status'
      })
    }
  })
}

const viewUserActivity = (user) => {
  selectedUser.value = user
  showActivityDialog.value = true
}

const onUserCreated = () => {
  showCreateUserDialog.value = false
  $q.notify({
    type: 'positive',
    message: 'User created successfully'
  })
}

const onUserUpdated = () => {
  showEditUserDialog.value = false
  $q.notify({
    type: 'positive',
    message: 'User updated successfully'
  })
}
</script>

<style scoped>
/* Use the same modern styles from Currency Index */
.users-admin {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.add-user-btn {
  border-radius: 12px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.add-user-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.page-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Enhanced Statistics Cards */
.modern-stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.modern-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card-accent {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stat-icon {
  margin-right: 16px;
  opacity: 0.9;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.stat-change.neutral {
  color: rgba(255, 255, 255, 0.7);
}

/* Enhanced Filter Card */
.modern-filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

/* Enhanced Cards */
.table-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.status-indicator {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .add-user-btn {
    width: 100%;
  }

  .page-content {
    padding: 16px;
  }

  .stat-content {
    padding: 16px;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}
</style>
