<template>
  <q-page class="user-edit">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">Edit User</h1>
            <p class="header-subtitle">Update {{ user.name }}'s information</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="$inertia.visit(route('admin.users.show', user.id))"
              icon="visibility"
              label="View User"
              color="info"
              size="lg"
              class="q-mr-sm"
              no-caps
              unelevated
            />
            <q-btn
              @click="$inertia.visit(route('admin.users.index'))"
              icon="arrow_back"
              label="Back to Users"
              color="grey-7"
              size="lg"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="page-content">
      <!-- Edit User Form -->
      <q-card class="form-card">
        <q-card-section>
          <form @submit.prevent="submit">
            <div class="row q-gutter-lg">
              <!-- Basic Information -->
              <div class="col-12 col-md-6">
                <h6 class="section-title">Basic Information</h6>
                
                <q-input
                  v-model="form.name"
                  label="Full Name"
                  outlined
                  :error="!!form.errors.name"
                  :error-message="form.errors.name"
                  class="q-mb-md"
                  required
                />

                <q-input
                  v-model="form.email"
                  label="Email Address"
                  type="email"
                  outlined
                  :error="!!form.errors.email"
                  :error-message="form.errors.email"
                  class="q-mb-md"
                  required
                />

                <q-input
                  v-model="form.password"
                  label="New Password (leave blank to keep current)"
                  :type="showPassword ? 'text' : 'password'"
                  outlined
                  :error="!!form.errors.password"
                  :error-message="form.errors.password"
                  class="q-mb-md"
                >
                  <template v-slot:append>
                    <q-icon
                      :name="showPassword ? 'visibility_off' : 'visibility'"
                      class="cursor-pointer"
                      @click="showPassword = !showPassword"
                    />
                  </template>
                </q-input>

                <q-input
                  v-model="form.password_confirmation"
                  label="Confirm New Password"
                  :type="showPasswordConfirm ? 'text' : 'password'"
                  outlined
                  :error="!!form.errors.password_confirmation"
                  :error-message="form.errors.password_confirmation"
                  class="q-mb-md"
                >
                  <template v-slot:append>
                    <q-icon
                      :name="showPasswordConfirm ? 'visibility_off' : 'visibility'"
                      class="cursor-pointer"
                      @click="showPasswordConfirm = !showPasswordConfirm"
                    />
                  </template>
                </q-input>

                <q-toggle
                  v-model="form.is_active"
                  label="Active User"
                  color="positive"
                  class="q-mb-md"
                />
              </div>

              <!-- Roles and Permissions -->
              <div class="col-12 col-md-6">
                <h6 class="section-title">Roles & Permissions</h6>
                
                <q-select
                  v-model="form.roles"
                  :options="roles"
                  option-value="id"
                  option-label="name"
                  label="Assign Roles"
                  outlined
                  multiple
                  use-chips
                  :error="!!form.errors.roles"
                  :error-message="form.errors.roles"
                  class="q-mb-md"
                />

                <div v-if="Object.keys(permissions).length > 0" class="permissions-section">
                  <h6 class="subsection-title">Direct Permissions</h6>
                  <div v-for="(groupPermissions, group) in permissions" :key="group" class="permission-group q-mb-md">
                    <q-expansion-item
                      :label="group.charAt(0).toUpperCase() + group.slice(1)"
                      icon="key"
                      class="permission-group-item"
                    >
                      <div class="q-pa-md">
                        <div class="row q-gutter-sm">
                          <div v-for="permission in groupPermissions" :key="permission.id" class="col-12 col-sm-6">
                            <q-checkbox
                              v-model="form.permissions"
                              :val="permission.id"
                              :label="permission.name"
                              color="primary"
                            />
                          </div>
                        </div>
                      </div>
                    </q-expansion-item>
                  </div>
                </div>
              </div>
            </div>

            <!-- User Stats -->
            <div class="row q-mt-lg">
              <div class="col-12">
                <h6 class="section-title">User Statistics</h6>
                <div class="row q-gutter-md">
                  <div class="col-12 col-sm-6 col-md-3">
                    <q-card class="stat-card">
                      <q-card-section>
                        <div class="stat-label">Member Since</div>
                        <div class="stat-value">{{ formatDate(user.created_at) }}</div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col-12 col-sm-6 col-md-3">
                    <q-card class="stat-card">
                      <q-card-section>
                        <div class="stat-label">Last Login</div>
                        <div class="stat-value">{{ formatDate(user.last_login_at) || 'Never' }}</div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col-12 col-sm-6 col-md-3">
                    <q-card class="stat-card">
                      <q-card-section>
                        <div class="stat-label">Status</div>
                        <div class="stat-value">
                          <q-chip :color="user.is_active ? 'positive' : 'negative'" text-color="white" size="sm">
                            {{ user.is_active ? 'Active' : 'Inactive' }}
                          </q-chip>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                  <div class="col-12 col-sm-6 col-md-3">
                    <q-card class="stat-card">
                      <q-card-section>
                        <div class="stat-label">Email Verified</div>
                        <div class="stat-value">
                          <q-chip :color="user.email_verified_at ? 'positive' : 'warning'" text-color="white" size="sm">
                            {{ user.email_verified_at ? 'Verified' : 'Pending' }}
                          </q-chip>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="row justify-end q-gutter-sm q-pt-lg">
              <q-btn
                @click="$inertia.visit(route('admin.users.index'))"
                label="Cancel"
                color="grey-7"
                outline
                no-caps
              />
              <q-btn
                type="submit"
                :loading="form.processing"
                label="Update User"
                color="primary"
                no-caps
                unelevated
              />
            </div>
          </form>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from 'vue'
import { useForm } from '@inertiajs/vue3'

const props = defineProps({
  user: {
    type: Object,
    required: true
  },
  roles: {
    type: Array,
    default: () => []
  },
  permissions: {
    type: Object,
    default: () => ({})
  }
})

const showPassword = ref(false)
const showPasswordConfirm = ref(false)

const form = useForm({
  name: props.user.name,
  email: props.user.email,
  password: '',
  password_confirmation: '',
  roles: props.user.roles?.map(role => role.id) || [],
  permissions: props.user.permissions?.map(permission => permission.id) || [],
  is_active: props.user.is_active ?? true,
})

const submit = () => {
  form.put(route('admin.users.update', props.user.id))
}

const formatDate = (date) => {
  if (!date) return null
  return new Date(date).toLocaleDateString()
}
</script>

<style scoped>
.user-edit {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.page-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.subsection-title {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
}

.permissions-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.permission-group-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
}

.stat-card {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }
  
  .header-title {
    font-size: 2rem;
  }
  
  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .page-content {
    padding: 16px;
  }
}
</style>
