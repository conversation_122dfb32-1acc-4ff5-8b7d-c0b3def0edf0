<template>
  <q-page class="user-show">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">{{ user.name }}</h1>
            <p class="header-subtitle">User profile and details</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="$inertia.visit(route('admin.users.edit', user.id))"
              icon="edit"
              label="Edit User"
              color="warning"
              size="lg"
              class="q-mr-sm"
              no-caps
              unelevated
            />
            <q-btn
              @click="$inertia.visit(route('admin.users.index'))"
              icon="arrow_back"
              label="Back to Users"
              color="grey-7"
              size="lg"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="page-content">
      <div class="row q-gutter-lg">
        <!-- User Information -->
        <div class="col-12 col-md-8">
          <!-- Basic Info Card -->
          <q-card class="info-card q-mb-lg">
            <q-card-section>
              <h6 class="section-title">Basic Information</h6>
              <div class="row q-gutter-md">
                <div class="col-12 col-sm-6">
                  <div class="info-item">
                    <div class="info-label">Full Name</div>
                    <div class="info-value">{{ user.name }}</div>
                  </div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="info-item">
                    <div class="info-label">Email Address</div>
                    <div class="info-value">{{ user.email }}</div>
                  </div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="info-item">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                      <q-chip :color="user.is_active ? 'positive' : 'negative'" text-color="white" size="sm">
                        {{ user.is_active ? 'Active' : 'Inactive' }}
                      </q-chip>
                    </div>
                  </div>
                </div>
                <div class="col-12 col-sm-6">
                  <div class="info-item">
                    <div class="info-label">Email Verified</div>
                    <div class="info-value">
                      <q-chip :color="user.email_verified_at ? 'positive' : 'warning'" text-color="white" size="sm">
                        {{ user.email_verified_at ? 'Verified' : 'Pending' }}
                      </q-chip>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- Roles Card -->
          <q-card class="info-card q-mb-lg">
            <q-card-section>
              <h6 class="section-title">Assigned Roles</h6>
              <div v-if="user.roles && user.roles.length > 0" class="row q-gutter-sm">
                <div v-for="role in user.roles" :key="role.id" class="col-auto">
                  <q-chip color="primary" text-color="white" size="md" icon="admin_panel_settings">
                    {{ role.name }}
                  </q-chip>
                </div>
              </div>
              <div v-else class="text-grey-6">
                No roles assigned
              </div>
            </q-card-section>
          </q-card>

          <!-- Permissions Card -->
          <q-card class="info-card">
            <q-card-section>
              <h6 class="section-title">Permissions</h6>
              
              <!-- Role Permissions -->
              <div v-if="rolePermissions.length > 0" class="q-mb-md">
                <div class="subsection-title">From Roles:</div>
                <div class="row q-gutter-xs">
                  <div v-for="permission in rolePermissions" :key="permission.id" class="col-auto">
                    <q-chip color="secondary" text-color="white" size="sm" icon="key">
                      {{ permission.name }}
                    </q-chip>
                  </div>
                </div>
              </div>

              <!-- Direct Permissions -->
              <div v-if="user.permissions && user.permissions.length > 0" class="q-mb-md">
                <div class="subsection-title">Direct Permissions:</div>
                <div class="row q-gutter-xs">
                  <div v-for="permission in user.permissions" :key="permission.id" class="col-auto">
                    <q-chip color="accent" text-color="white" size="sm" icon="key">
                      {{ permission.name }}
                    </q-chip>
                  </div>
                </div>
              </div>

              <div v-if="allPermissions.length === 0" class="text-grey-6">
                No permissions assigned
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Statistics Sidebar -->
        <div class="col-12 col-md-4">
          <!-- Stats Card -->
          <q-card class="stats-card q-mb-lg">
            <q-card-section>
              <h6 class="section-title">Statistics</h6>
              
              <div class="stat-item q-mb-md">
                <div class="stat-icon">
                  <q-icon name="calendar_today" size="24px" color="primary" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">Member Since</div>
                  <div class="stat-value">{{ formatDate(user.created_at) }}</div>
                </div>
              </div>

              <div class="stat-item q-mb-md">
                <div class="stat-icon">
                  <q-icon name="login" size="24px" color="positive" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">Last Login</div>
                  <div class="stat-value">{{ formatDate(user.last_login_at) || 'Never' }}</div>
                </div>
              </div>

              <div class="stat-item q-mb-md">
                <div class="stat-icon">
                  <q-icon name="update" size="24px" color="warning" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">Last Updated</div>
                  <div class="stat-value">{{ formatDate(user.updated_at) }}</div>
                </div>
              </div>

              <div class="stat-item">
                <div class="stat-icon">
                  <q-icon :name="user.is_online ? 'circle' : 'radio_button_unchecked'" 
                          size="24px" 
                          :color="user.is_online ? 'positive' : 'grey'" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">Online Status</div>
                  <div class="stat-value">{{ user.is_online ? 'Online' : 'Offline' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- Quick Actions Card -->
          <q-card class="actions-card">
            <q-card-section>
              <h6 class="section-title">Quick Actions</h6>
              
              <q-btn
                @click="toggleUserStatus"
                :icon="user.is_active ? 'block' : 'check_circle'"
                :label="user.is_active ? 'Deactivate User' : 'Activate User'"
                :color="user.is_active ? 'negative' : 'positive'"
                class="full-width q-mb-sm"
                no-caps
                unelevated
              />

              <q-btn
                @click="$inertia.visit(route('admin.users.assign-roles', user.id))"
                icon="admin_panel_settings"
                label="Manage Roles"
                color="primary"
                class="full-width q-mb-sm"
                no-caps
                outline
              />

              <q-btn
                @click="sendPasswordReset"
                icon="lock_reset"
                label="Send Password Reset"
                color="warning"
                class="full-width"
                no-caps
                outline
              />
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { computed } from 'vue'
import { useQuasar } from 'quasar'
import { router } from '@inertiajs/vue3'

const $q = useQuasar()

const props = defineProps({
  user: {
    type: Object,
    required: true
  }
})

// Computed properties
const rolePermissions = computed(() => {
  if (!props.user.roles) return []
  
  const permissions = []
  props.user.roles.forEach(role => {
    if (role.permissions) {
      permissions.push(...role.permissions)
    }
  })
  
  // Remove duplicates
  return permissions.filter((permission, index, self) => 
    index === self.findIndex(p => p.id === permission.id)
  )
})

const allPermissions = computed(() => {
  const direct = props.user.permissions || []
  const fromRoles = rolePermissions.value
  return [...direct, ...fromRoles].filter((permission, index, self) => 
    index === self.findIndex(p => p.id === permission.id)
  )
})

// Methods
const formatDate = (date) => {
  if (!date) return null
  return new Date(date).toLocaleDateString()
}

const toggleUserStatus = async () => {
  try {
    await router.patch(route('admin.users.toggle-status', props.user.id))
    $q.notify({
      type: 'positive',
      message: 'User status updated successfully'
    })
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to update user status'
    })
  }
}

const sendPasswordReset = () => {
  $q.notify({
    type: 'info',
    message: 'Password reset functionality will be implemented'
  })
}
</script>

<style scoped>
.user-show {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.page-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.info-card, .stats-card, .actions-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.subsection-title {
  margin: 0 0 8px 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.info-item {
  margin-bottom: 16px;
}

.info-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 4px;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }
  
  .header-title {
    font-size: 2rem;
  }
  
  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .page-content {
    padding: 16px;
  }
}
</style>
