<template>
  <q-page class="permission-create">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">Create Permission</h1>
            <p class="header-subtitle">Create a new system permission</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="$inertia.visit($route('admin.permissions.index'))"
              icon="arrow_back"
              label="Back to Permissions"
              color="grey-7"
              size="lg"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <!-- Form -->
      <q-card class="form-card">
        <q-card-section>
          <form @submit.prevent="submit">
            <!-- Permission Name -->
            <div class="q-mb-lg">
              <q-input
                v-model="form.name"
                label="Permission Name"
                placeholder="e.g., view users, edit currencies, etc."
                outlined
                :error="!!form.errors.name"
                :error-message="form.errors.name"
                class="q-mb-sm"
              >
                <template v-slot:prepend>
                  <q-icon name="key" />
                </template>
              </q-input>
              <div class="text-caption text-grey-6">
                Use format: "action resource" (e.g., "view users", "edit currencies")
              </div>
            </div>

            <!-- Suggested Groups -->
            <div v-if="groups.length > 0" class="q-mb-lg">
              <div class="text-subtitle2 q-mb-sm">Existing Permission Groups</div>
              <div class="q-gutter-xs">
                <q-chip
                  v-for="group in groups"
                  :key="group"
                  color="grey-3"
                  text-color="grey-8"
                  size="sm"
                  class="capitalize"
                >
                  {{ group }}
                </q-chip>
              </div>
              <div class="text-caption text-grey-6 q-mt-sm">
                Consider using one of these existing groups for consistency
              </div>
            </div>

            <!-- Actions -->
            <div class="row justify-end q-gutter-sm q-pt-lg">
              <q-btn
                @click="$inertia.visit($route('admin.permissions.index'))"
                label="Cancel"
                color="grey-7"
                outline
                no-caps
              />
              <q-btn
                type="submit"
                :loading="form.processing"
                label="Create Permission"
                color="primary"
                no-caps
                unelevated
              />
            </div>
          </form>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'

const props = defineProps({
  groups: {
    type: Array,
    default: () => []
  }
})

const form = useForm({
  name: '',
})

const submit = () => {
  form.post($route('admin.permissions.store'))
}
</script>

<style scoped>
.permission-create {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.form-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
  max-width: 600px;
  margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
}
</style>
