<template>
  <q-page class="permissions-admin">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">Permissions Management</h1>
            <p class="header-subtitle">Manage system permissions</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="$inertia.visit($route('admin.permissions.create'))"
              icon="add"
              label="Create Permission"
              color="primary"
              size="lg"
              class="add-permission-btn"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <!-- Search and Filters -->
      <q-card class="modern-filter-card q-mb-lg">
        <q-card-section>
          <div class="filter-header q-mb-md">
            <h6 class="filter-title">Search & Filter Permissions</h6>
          </div>

          <div class="row q-gutter-md items-end">
            <div class="col-12 col-sm-6 col-md-4">
              <q-input
                v-model="filters.search"
                label="Search permissions..."
                outlined
                clearable
                debounce="300"
                @update:model-value="loadPermissions"
                class="modern-input"
              >
                <template v-slot:prepend>
                  <q-icon name="search" color="primary" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="filters.group"
                :options="groupOptions"
                label="Filter by Group"
                outlined
                clearable
                @update:model-value="loadPermissions"
                class="modern-input"
              >
                <template v-slot:prepend>
                  <q-icon name="category" color="primary" />
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-btn
                @click="resetFilters"
                icon="clear_all"
                label="Reset"
                color="grey-7"
                outline
                class="full-width modern-btn"
                no-caps
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Permissions Table -->
      <q-card class="table-card">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <div class="col">
              <h6 class="q-ma-none">Permissions List</h6>
            </div>
          </div>

          <q-table
            :rows="permissions.data"
            :columns="columns"
            row-key="id"
            :loading="loading"
            :pagination="pagination"
            @request="onRequest"
            flat
            bordered
          >
            <template v-slot:body-cell-name="props">
              <q-td :props="props">
                <div class="flex items-center">
                  <q-icon name="key" color="green" size="sm" class="q-mr-sm" />
                  <div class="text-weight-medium">{{ props.row.name }}</div>
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-group="props">
              <q-td :props="props">
                <q-chip
                  color="grey-3"
                  text-color="grey-8"
                  size="sm"
                  class="capitalize"
                >
                  {{ getPermissionGroup(props.row.name) }}
                </q-chip>
              </q-td>
            </template>

            <template v-slot:body-cell-roles="props">
              <q-td :props="props">
                {{ props.row.roles?.length || 0 }} roles
              </q-td>
            </template>

            <template v-slot:body-cell-created_at="props">
              <q-td :props="props">
                <div class="text-caption">
                  {{ formatDate(props.row.created_at) }}
                </div>
              </q-td>
            </template>

            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <div class="q-gutter-xs">
                  <q-btn
                    @click="$inertia.visit($route('admin.permissions.show', props.row.id))"
                    icon="visibility"
                    color="primary"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>View Permission</q-tooltip>
                  </q-btn>

                  <q-btn
                    @click="$inertia.visit($route('admin.permissions.edit', props.row.id))"
                    icon="edit"
                    color="warning"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Edit Permission</q-tooltip>
                  </q-btn>

                  <q-btn
                    v-if="!isSystemPermission(props.row.name)"
                    @click="confirmDelete(props.row)"
                    icon="delete"
                    color="negative"
                    flat
                    round
                    size="sm"
                  >
                    <q-tooltip>Delete Permission</q-tooltip>
                  </q-btn>
                </div>
              </q-td>
            </template>
          </q-table>

          <!-- Empty State -->
          <div v-if="permissions.data.length === 0 && !loading" class="text-center q-pa-xl">
            <q-icon name="key" size="4rem" color="grey-4" class="q-mb-md" />
            <div class="text-h6 text-grey-6 q-mb-sm">No permissions found</div>
            <div class="text-subtitle2 text-grey-5 q-mb-lg">
              {{ filters.search ? 'Try adjusting your search criteria' : 'Get started by creating your first permission' }}
            </div>
            <q-btn
              @click="$inertia.visit($route('admin.permissions.create'))"
              icon="add"
              label="Create Permission"
              color="primary"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog">
      <q-card>
        <q-card-section>
          <div class="text-h6">Confirm Delete</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          Are you sure you want to delete the permission <strong>{{ selectedPermission?.name }}</strong>?
          This action cannot be undone.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            @click="deletePermission"
            :loading="deleting"
            label="Delete"
            color="negative"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// Props
const props = defineProps({
  permissions: {
    type: Object,
    default: () => ({ data: [], total: 0 })
  },
  groupedPermissions: {
    type: Object,
    default: () => ({})
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

// Reactive data
const loading = ref(false)
const deleting = ref(false)
const showDeleteDialog = ref(false)
const selectedPermission = ref(null)

const filters = ref({
  search: props.filters.search || '',
  group: props.filters.group || ''
})

const permissions = ref(props.permissions)

const pagination = ref({
  page: 1,
  rowsPerPage: 15,
  rowsNumber: props.permissions.total || 0
})

// Table columns
const columns = [
  { name: 'name', label: 'Permission Name', field: 'name', sortable: true, align: 'left' },
  { name: 'group', label: 'Group', field: 'group', sortable: false, align: 'center' },
  { name: 'roles', label: 'Roles', field: 'roles', sortable: false, align: 'center' },
  { name: 'created_at', label: 'Created At', field: 'created_at', sortable: true, align: 'center' },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: 'center' }
]

// Computed
const groupOptions = computed(() => {
  return Object.keys(props.groupedPermissions).map(group => ({
    label: `${group} (${props.groupedPermissions[group].length})`,
    value: group
  }))
})

// Methods
const loadPermissions = async () => {
  loading.value = true
  try {
    // This would typically make an API call
    // For now, we'll use the props data
  } catch (error) {
    console.error('Error loading permissions:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load permissions'
    })
  } finally {
    loading.value = false
  }
}

const onRequest = () => {
  loadPermissions()
}

const resetFilters = () => {
  filters.value = {
    search: '',
    group: ''
  }
  loadPermissions()
}

const confirmDelete = (permission) => {
  selectedPermission.value = permission
  showDeleteDialog.value = true
}

const deletePermission = async () => {
  deleting.value = true
  try {
    await $inertia.delete($route('admin.permissions.destroy', selectedPermission.value.id))
    showDeleteDialog.value = false
    $q.notify({
      type: 'positive',
      message: 'Permission deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting permission:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to delete permission'
    })
  } finally {
    deleting.value = false
  }
}

const getPermissionGroup = (permissionName) => {
  return permissionName.split(' ')[1] || 'general'
}

const isSystemPermission = (permissionName) => {
  // Prevent deletion of core system permissions
  const systemPermissions = ['view dashboard', 'view settings', 'manage system']
  return systemPermissions.includes(permissionName)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

// Lifecycle
onMounted(() => {
  loadPermissions()
})
</script>

<style scoped>
/* Use the same modern styles from other admin pages */
.permissions-admin {
  background: #f8fafc;
  min-height: 100vh;
}

.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.add-permission-btn {
  border-radius: 12px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.add-permission-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* Enhanced Filter Card */
.modern-filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.filter-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

/* Enhanced Cards */
.table-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .add-permission-btn {
    width: 100%;
  }
}
</style>
