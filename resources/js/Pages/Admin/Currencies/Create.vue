<template>
  <q-page class="currency-create">
    <!-- Header -->
    <div class="page-header q-pa-lg bg-white shadow-1">
      <div class="row items-center">
        <div class="col">
          <h1 class="text-h4 q-mb-sm">Add New Currency</h1>
          <p class="text-subtitle1 text-grey-6">Create a new currency with automatic flag detection</p>
        </div>
        <div class="col-auto">
          <q-btn
            @click="$inertia.visit(route('admin.currencies.index'))"
            icon="arrow_back"
            label="Back to Currencies"
            color="grey"
            outline
          />
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <div class="row justify-center">
        <div class="col-12 col-lg-10">
          <q-card class="form-card">
            <q-card-section>
              <div class="text-h6 q-mb-lg">Currency Information</div>

              <CurrencyForm
                v-model="form"
                :loading="submitting"
                :errors="errors"
                submit-label="Create Currency"
                @submit="submitForm"
                @reset="resetForm"
              />
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from 'vue'
import { useQuasar } from 'quasar'
import { router } from '@inertiajs/vue3'
import CurrencyForm from '@/components/Currency/CurrencyForm.vue'

const $q = useQuasar()

// This page uses StoreCurrencyRequest validation on the backend

// Form data
const form = ref({
  name: {
    en: '',
    ku: ''
  },
  code: '',
  symbol: '',
  country: '',
  flag_icon: '',
  decimal_places: 2,
  min_amount: 0.01,
  max_amount: 1000000,
  order: 0,
  description: {
    en: '',
    ku: ''
  },
  is_active: true,
  is_base_currency: false
})

const errors = ref({})
const submitting = ref(false)

// Methods
const submitForm = (formData) => {
  submitting.value = true
  errors.value = {}

  router.post('/admin/currencies', formData, {
    onSuccess: () => {
      $q.notify({
        type: 'positive',
        message: 'Currency created successfully',
        position: 'top'
      })
    },
    onError: (formErrors) => {
      errors.value = formErrors
      $q.notify({
        type: 'negative',
        message: 'Please check the form for errors',
        position: 'top'
      })
    },
    onFinish: () => {
      submitting.value = false
    }
  })
}

const resetForm = () => {
  form.value = {
    name: { en: '', ku: '' },
    code: '',
    symbol: '',
    country: '',
    flag_icon: '',
    decimal_places: 2,
    min_amount: 0.01,
    max_amount: 1000000,
    order: 0,
    description: { en: '', ku: '' },
    is_active: true,
    is_base_currency: false
  }
  errors.value = {}
}
</script>

<style scoped>
.currency-create {
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  border-bottom: 1px solid #e0e0e0;
}

.form-card, .preview-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.currency-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 2px dashed #e0e0e0;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
}
</style>
