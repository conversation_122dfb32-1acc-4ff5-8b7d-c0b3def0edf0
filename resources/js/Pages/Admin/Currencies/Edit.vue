<template>
  <q-page class="currency-edit">
    <!-- Header -->
    <div class="page-header q-pa-lg bg-white shadow-1">
      <div class="row items-center">
        <div class="col">
          <h1 class="text-h4 q-mb-sm">Edit Currency</h1>
          <p class="text-subtitle1 text-grey-6">Update currency information and settings</p>
        </div>
        <div class="col-auto">
          <q-btn
            :to="{ name: 'admin.currencies.index' }"
            icon="arrow_back"
            label="Back to Currencies"
            color="grey"
            outline
          />
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <div class="row justify-center">
        <div class="col-12 col-lg-10">
          <q-card class="form-card">
            <q-card-section>
              <div class="text-h6 q-mb-lg">Currency Information</div>

              <CurrencyForm
                v-model="form"
                :loading="submitting"
                :errors="errors"
                :is-edit="true"
                submit-label="Update Currency"
                @submit="submitForm"
                @reset="resetForm"
              />
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { router } from '@inertiajs/vue3'
import CurrencyForm from '@/components/Currency/CurrencyForm.vue'

const $q = useQuasar()

// Props
const props = defineProps({
  currency: {
    type: Object,
    required: true
  }
})

// Form data
const form = ref({
  name: {
    en: '',
    ku: ''
  },
  code: '',
  symbol: '',
  country: '',
  flag_icon: '',
  decimal_places: 2,
  min_amount: 0.01,
  max_amount: 1000000,
  order: 0,
  description: {
    en: '',
    ku: ''
  },
  is_active: true,
  is_base_currency: false
})

const errors = ref({})
const submitting = ref(false)

// Methods
const submitForm = (formData) => {
  submitting.value = true
  errors.value = {}

  router.put(`/admin/currencies/${props.currency.id}`, formData, {
    onSuccess: () => {
      $q.notify({
        type: 'positive',
        message: 'Currency updated successfully',
        position: 'top'
      })
    },
    onError: (formErrors) => {
      errors.value = formErrors
      $q.notify({
        type: 'negative',
        message: 'Please check the form for errors',
        position: 'top'
      })
    },
    onFinish: () => {
      submitting.value = false
    }
  })
}

const resetForm = () => {
  loadCurrencyData()
  errors.value = {}
}

const loadCurrencyData = () => {
  if (props.currency) {
    form.value = {
      name: {
        en: props.currency.name?.en || props.currency.name || '',
        ku: props.currency.name?.ku || ''
      },
      code: props.currency.code || '',
      symbol: props.currency.symbol || '',
      country: props.currency.country || '',
      flag_icon: props.currency.flag_icon || '',
      decimal_places: props.currency.decimal_places || 2,
      min_amount: props.currency.min_amount || 0.01,
      max_amount: props.currency.max_amount || 1000000,
      order: props.currency.order || 0,
      description: {
        en: props.currency.description?.en || props.currency.description || '',
        ku: props.currency.description?.ku || ''
      },
      is_active: props.currency.is_active !== undefined ? props.currency.is_active : true,
      is_base_currency: props.currency.is_base_currency !== undefined ? props.currency.is_base_currency : false
    }
  }
}

// Lifecycle
onMounted(() => {
  loadCurrencyData()
})
</script>

<style scoped>
.currency-edit {
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  border-bottom: 1px solid #e0e0e0;
}

.form-card, .preview-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.currency-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 2px dashed #e0e0e0;
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
}
</style>
