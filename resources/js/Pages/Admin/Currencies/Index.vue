<template>
  <q-page class="currencies-admin">
    <!-- Modern Header -->
    <div class="modern-header">
      <div class="header-content">
        <div class="header-main">
          <div class="header-info">
            <h1 class="header-title">Currency Management</h1>
            <p class="header-subtitle">Manage currencies, exchange rates, and flag assignments</p>
          </div>
          <div class="header-actions">
            <q-btn
              @click="showCurrencyCreateModal = true"
              icon="add"
              label="Add Currency"
              color="primary"
              size="lg"
              class="add-currency-btn"
              no-caps
              unelevated
            />
          </div>
        </div>
      </div>
    </div>

    <div class="q-pa-lg">
      <!-- Enhanced Filters and Search -->
      <q-card class="modern-filter-card q-mb-lg">
        <q-card-section>
          <div class="filter-header q-mb-md">
            <h6 class="filter-title">Search & Filter</h6>
            <q-chip
              v-if="hasActiveFilters"
              @click="resetFilters"
              removable
              color="primary"
              text-color="white"
              size="sm"
            >
              {{ activeFiltersCount }} filter(s) active
            </q-chip>
          </div>

          <div class="row q-gutter-md items-end">
            <div class="col-12 col-sm-6 col-md-4">
              <q-input
                v-model="filters.search"
                label="Search currencies..."
                outlined
                clearable
                debounce="300"
                @update:model-value="loadCurrencies"
                class="modern-input"
              >
                <template v-slot:prepend>
                  <q-icon name="search" color="primary" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="filters.is_active"
                :options="statusOptions"
                label="Status"
                outlined
                clearable
                @update:model-value="loadCurrencies"
                class="modern-select"
              >
                <template v-slot:prepend>
                  <q-icon name="toggle_on" color="positive" />
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
              <q-select
                v-model="filters.is_base_currency"
                :options="baseOptions"
                label="Base Currency"
                outlined
                clearable
                @update:model-value="loadCurrencies"
                class="modern-select"
              >
                <template v-slot:prepend>
                  <q-icon name="star" color="accent" />
                </template>
              </q-select>
            </div>

            <div class="col-12 col-sm-6 col-md-2">
              <q-btn
                @click="resetFilters"
                icon="clear_all"
                label="Reset"
                color="grey-7"
                outline
                class="full-width modern-btn"
                no-caps
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Enhanced Statistics Cards -->
      <div class="row q-gutter-lg q-mb-xl">
        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-primary">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="account_balance_wallet" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total }}</div>
                <div class="stat-label">Total Currencies</div>
                <div class="stat-change positive">
                  <q-icon name="trending_up" size="14px" />
                  All currencies
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-success">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="check_circle" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.active }}</div>
                <div class="stat-label">Active Currencies</div>
                <div class="stat-change positive">
                  <q-icon name="trending_up" size="14px" />
                  {{ stats.total > 0 ? Math.round((stats.active / stats.total) * 100) : 0 }}% active
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-warning">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="pause_circle" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.inactive }}</div>
                <div class="stat-label">Inactive Currencies</div>
                <div class="stat-change neutral">
                  <q-icon name="trending_flat" size="14px" />
                  Not in use
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-sm-6 col-md-3">
          <q-card class="modern-stat-card stat-card-accent">
            <q-card-section class="stat-content">
              <div class="stat-icon">
                <q-icon name="star" size="32px" />
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.base }}</div>
                <div class="stat-label">Base Currency</div>
                <div class="stat-change" :class="stats.base > 0 ? 'positive' : 'neutral'">
                  <q-icon :name="stats.base > 0 ? 'check' : 'warning'" size="14px" />
                  {{ stats.base > 0 ? 'Configured' : 'Not set' }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Currencies Table -->
      <q-card class="table-card">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <div class="col">
              <h6 class="q-ma-none">Currencies List</h6>
            </div>
            <div class="col-auto">
              <q-btn-toggle
                v-model="viewMode"
                :options="[
                  { label: 'Table', value: 'table', icon: 'table_view' },
                  { label: 'Grid', value: 'grid', icon: 'grid_view' }
                ]"
                color="primary"
                outline
                size="sm"
              />
            </div>
          </div>

          <!-- Table View -->
          <div v-if="viewMode === 'table'">
            <q-table
              :rows="currencies.data"
              :columns="columns"
              row-key="id"
              :loading="loading"
              :pagination="pagination"
              @request="onRequest"
              flat
              bordered
            >
              <template v-slot:body-cell-flag="props">
                <q-td :props="props">
                  <CountryFlag
                    :code="props.row.code"
                    :country="props.row.country"
                    :flag-icon="props.row.flag_icon"
                    size="medium"
                    :shadow="true"
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-name="props">
                <q-td :props="props">
                  <div>
                    <div class="text-weight-medium">{{ props.row.name.en || props.row.name }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.country }}</div>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-code="props">
                <q-td :props="props">
                  <q-chip
                    :color="props.row.is_base_currency ? 'accent' : 'primary'"
                    text-color="white"
                    size="sm"
                  >
                    {{ props.row.code }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-status="props">
                <q-td :props="props">
                  <q-chip
                    :color="props.row.is_active ? 'positive' : 'negative'"
                    text-color="white"
                    size="sm"
                  >
                    {{ props.row.is_active ? 'Active' : 'Inactive' }}
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-limits="props">
                <q-td :props="props">
                  <div class="text-caption">
                    Min: {{ formatAmount(props.row.min_amount, props.row.symbol) }}<br>
                    Max: {{ formatAmount(props.row.max_amount, props.row.symbol) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-actions="props">
                <q-td :props="props">
                  <div class="q-gutter-xs">
                    <q-btn
                      @click="$inertia.visit(route('admin.currencies.edit', props.row.id))"
                      icon="edit"
                      color="primary"
                      flat
                      round
                      size="sm"
                    >
                      <q-tooltip>Edit Currency</q-tooltip>
                    </q-btn>

                    <q-btn
                      @click="toggleStatus(props.row)"
                      :icon="props.row.is_active ? 'pause' : 'play_arrow'"
                      :color="props.row.is_active ? 'warning' : 'positive'"
                      flat
                      round
                      size="sm"
                    >
                      <q-tooltip>{{ props.row.is_active ? 'Deactivate' : 'Activate' }}</q-tooltip>
                    </q-btn>

                    <q-btn
                      @click="confirmDelete(props.row)"
                      icon="delete"
                      color="negative"
                      flat
                      round
                      size="sm"
                    >
                      <q-tooltip>Delete Currency</q-tooltip>
                    </q-btn>
                  </div>
                </q-td>
              </template>
            </q-table>
          </div>

          <!-- Grid View -->
          <div v-else class="row q-gutter-md">
            <div
              v-for="currency in currencies.data"
              :key="currency.id"
              class="col-12 col-sm-6 col-md-4 col-lg-3"
            >
              <q-card class="currency-card">
                <q-card-section class="text-center">
                  <div class="q-mb-sm flex justify-center">
                    <CountryFlag
                      :code="currency.code"
                      :country="currency.country"
                      :flag-icon="currency.flag_icon"
                      size="large"
                      :shadow="true"
                    />
                  </div>
                  <h6 class="q-ma-none q-mb-xs">{{ currency.name.en || currency.name }}</h6>
                  <q-chip
                    :color="currency.is_base_currency ? 'accent' : 'primary'"
                    text-color="white"
                    size="sm"
                    class="q-mb-sm"
                  >
                    {{ currency.code }}
                  </q-chip>
                  <div class="text-caption text-grey-6 q-mb-sm">{{ currency.country }}</div>

                  <q-chip
                    :color="currency.is_active ? 'positive' : 'negative'"
                    text-color="white"
                    size="xs"
                  >
                    {{ currency.is_active ? 'Active' : 'Inactive' }}
                  </q-chip>
                </q-card-section>

                <q-card-actions align="center">
                  <q-btn
                    @click="$inertia.visit(route('admin.currencies.edit', currency.id))"
                    icon="edit"
                    color="primary"
                    flat
                    round
                    size="sm"
                  />

                  <q-btn
                    @click="toggleStatus(currency)"
                    :icon="currency.is_active ? 'pause' : 'play_arrow'"
                    :color="currency.is_active ? 'warning' : 'positive'"
                    flat
                    round
                    size="sm"
                  />

                  <q-btn
                    @click="confirmDelete(currency)"
                    icon="delete"
                    color="negative"
                    flat
                    round
                    size="sm"
                  />
                </q-card-actions>
              </q-card>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="currencies.data.length === 0 && !loading" class="text-center q-pa-xl">
            <q-icon name="account_balance" size="4rem" color="grey-4" class="q-mb-md" />
            <div class="text-h6 text-grey-6 q-mb-sm">No currencies found</div>
            <div class="text-subtitle2 text-grey-5 q-mb-lg">
              {{ filters.search ? 'Try adjusting your search criteria' : 'Get started by adding your first currency' }}
            </div>
            <q-btn
              @click="showCurrencyCreateModal = true"
              icon="add"
              label="Add Currency"
              color="primary"
            />
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog">
      <q-card>
        <q-card-section>
          <div class="text-h6">Confirm Delete</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          Are you sure you want to delete <strong>{{ selectedCurrency?.name?.en || selectedCurrency?.name }}</strong>?
          This action cannot be undone.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            @click="deleteCurrency"
            :loading="deleting"
            label="Delete"
            color="negative"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Currency Create Modal -->
    <CurrencyCreateModal
      v-model="showCurrencyCreateModal"
      @created="onCurrencyCreated"
      @error="onCurrencyCreateError"
    />
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import CountryFlag from '@/components/CountryFlag.vue'
import CurrencyCreateModal from '@/components/CurrencyCreateModal.vue'

const router = useRouter()
const $q = useQuasar()

// Props
const props = defineProps({
  currencies: {
    type: Object,
    default: () => ({ data: [], total: 0 })
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

// Reactive data
const loading = ref(false)
const deleting = ref(false)
const showDeleteDialog = ref(false)
const showCurrencyCreateModal = ref(false)
const selectedCurrency = ref(null)
const viewMode = ref('table')

const filters = ref({
  search: props.filters.search || '',
  is_active: props.filters.is_active || null,
  is_base_currency: props.filters.is_base_currency || null
})

const currencies = ref(props.currencies)

const pagination = ref({
  page: 1,
  rowsPerPage: 15,
  rowsNumber: props.currencies.total || 0
})

// Options
const statusOptions = [
  { label: 'Active', value: true },
  { label: 'Inactive', value: false }
]

const baseOptions = [
  { label: 'Base Currency', value: true },
  { label: 'Regular Currency', value: false }
]

// Table columns
const columns = [
  { name: 'flag', label: 'Flag', field: 'flag_icon', sortable: false, align: 'center' },
  { name: 'name', label: 'Name', field: 'name', sortable: true, align: 'left' },
  { name: 'code', label: 'Code', field: 'code', sortable: true, align: 'center' },
  { name: 'symbol', label: 'Symbol', field: 'symbol', sortable: false, align: 'center' },
  { name: 'decimal_places', label: 'Decimals', field: 'decimal_places', sortable: true, align: 'center' },
  { name: 'limits', label: 'Limits', field: 'limits', sortable: false, align: 'center' },
  { name: 'status', label: 'Status', field: 'is_active', sortable: true, align: 'center' },
  { name: 'actions', label: 'Actions', field: 'actions', sortable: false, align: 'center' }
]

// Computed properties
const stats = computed(() => {
  const data = currencies.value.data || []
  return {
    total: data.length,
    active: data.filter(c => c.is_active).length,
    inactive: data.filter(c => !c.is_active).length,
    base: data.filter(c => c.is_base_currency).length
  }
})

const hasActiveFilters = computed(() => {
  return filters.value.search ||
         filters.value.is_active !== null ||
         filters.value.is_base_currency !== null
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (filters.value.search) count++
  if (filters.value.is_active !== null) count++
  if (filters.value.is_base_currency !== null) count++
  return count
})

// Methods
const loadCurrencies = async (requestProp = null) => {
  loading.value = true

  try {
    const params = {
      page: requestProp?.pagination?.page || pagination.value.page,
      per_page: requestProp?.pagination?.rowsPerPage || pagination.value.rowsPerPage,
      search: filters.value.search,
      is_active: filters.value.is_active,
      is_base_currency: filters.value.is_base_currency
    }

    // Remove null/undefined values
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })

    const response = await axios.get('/api/admin/currencies', { params })

    currencies.value = response.data.currencies
    pagination.value.page = response.data.currencies.current_page
    pagination.value.rowsNumber = response.data.currencies.total

  } catch (error) {
    console.error('Error loading currencies:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load currencies'
    })
  } finally {
    loading.value = false
  }
}

const onRequest = (props) => {
  loadCurrencies(props)
}

const resetFilters = () => {
  filters.value = {
    search: '',
    is_active: null,
    is_base_currency: null
  }
  loadCurrencies()
}

const toggleStatus = async (currency) => {
  try {
    await axios.patch(`/api/admin/currencies/${currency.id}/toggle-status`)

    // Update local data
    const index = currencies.value.data.findIndex(c => c.id === currency.id)
    if (index !== -1) {
      currencies.value.data[index].is_active = !currencies.value.data[index].is_active
    }

    $q.notify({
      type: 'positive',
      message: `Currency ${currency.is_active ? 'deactivated' : 'activated'} successfully`
    })
  } catch (error) {
    console.error('Error toggling currency status:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to update currency status'
    })
  }
}

const confirmDelete = (currency) => {
  selectedCurrency.value = currency
  showDeleteDialog.value = true
}

const deleteCurrency = async () => {
  deleting.value = true

  try {
    await axios.delete(`/api/admin/currencies/${selectedCurrency.value.id}`)

    // Remove from local data
    const index = currencies.value.data.findIndex(c => c.id === selectedCurrency.value.id)
    if (index !== -1) {
      currencies.value.data.splice(index, 1)
    }

    showDeleteDialog.value = false
    selectedCurrency.value = null

    $q.notify({
      type: 'positive',
      message: 'Currency deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting currency:', error)
    $q.notify({
      type: 'negative',
      message: error.response?.data?.message || 'Failed to delete currency'
    })
  } finally {
    deleting.value = false
  }
}

const formatAmount = (amount, symbol) => {
  const num = parseFloat(amount)
  if (num >= 1000000) {
    return `${symbol}${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${symbol}${(num / 1000).toFixed(1)}K`
  }
  return `${symbol}${num.toFixed(2)}`
}

const onCurrencyCreated = async (result) => {
  showCurrencyCreateModal.value = false

  if (result.success) {
    await loadCurrencies()

    $q.notify({
      type: 'positive',
      message: `Currency "${result.currency.name}" created successfully`,
      timeout: 5000
    })
  }
}

const onCurrencyCreateError = (error) => {
  showCurrencyCreateModal.value = false

  $q.notify({
    type: 'negative',
    message: error.error || 'Failed to create currency',
    timeout: 5000
  })
}

// Watchers
watch(() => filters.value, () => {
  // Debounce the search
  setTimeout(() => {
    loadCurrencies()
  }, 300)
}, { deep: true })

// Lifecycle
onMounted(() => {
  if (!currencies.value.data.length) {
    loadCurrencies()
  }
})
</script>

<style scoped>
.currencies-admin {
  background: #f8fafc;
  min-height: 100vh;
}

/* Modern Header */
.modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
  position: relative;
  overflow: hidden;
}

.header-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

.add-currency-btn {
  border-radius: 12px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.add-currency-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* Enhanced Filter Card */
.modern-filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

/* Enhanced Statistics Cards */
.modern-stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.modern-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.stat-card-accent {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stat-icon {
  margin-right: 16px;
  opacity: 0.9;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.stat-change.neutral {
  color: rgba(255, 255, 255, 0.7);
}

/* Enhanced Cards */
.table-card,
.currency-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
}

.currency-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-header {
    padding: 24px 16px;
  }

  .header-title {
    font-size: 2rem;
  }

  .header-main {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .add-currency-btn {
    width: 100%;
  }

  .stat-content {
    padding: 16px;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}
</style>
