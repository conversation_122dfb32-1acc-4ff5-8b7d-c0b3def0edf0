import { ref, computed } from 'vue';

export function useLanguage() {
  const translations = {
    ku: {
      title: 'نرخی دراوەکان',
      lastUpdate: 'دوایین نوێکردنەوە',
      weather: {
        title: 'کەش و هەوا',
        humidity: 'شێ',
        wind: 'با',
        temperature: 'پلەی گەرمی',
        loading: 'بارکردن...',
        error: 'هەڵە ڕوویدا',
        error_not_supported: 'خزمەتگوزاری شوێن لە وێبگەڕەکەت پشتگیری ناکرێت',
        error_permission_denied: 'دەستپێگەیشتن بە شوێن ڕەتکرایەوە',
        error_unavailable: 'زانیاری شوێن بەردەست نییە',
        error_timeout: 'داواکاری شوێن کاتی بەسەرچوو',
        error_unknown: 'هەڵەیەکی نەناسراو ڕوویدا',
        refresh: 'نوێکردنەوە',
        tap_refresh: 'کلیک بکە بۆ نوێکردنەوە'
      },
      currency: 'دراو',
      code: 'کۆد',
      sale: 'فرۆشتن',
      buy: 'کڕین',
      close: 'داخستن',
      clear: 'پاککردنەوە',
      amount: 'بڕی دراو',
      buyAmount: 'بڕی دراو بۆ کڕین',
      sellAmount: 'بڕی دراو بۆ فرۆشتن',
      buyRate: 'نرخی کڕین',
      sellRate: 'نرخی فرۆشتن',
      iqdAmount: 'بڕی دینار',
      usdAmount: 'بڕی دۆلار',
      secondAmount: 'بڕی دووەم',
      divideByZero: 'ناتوانرێت دابەش بکرێت بە سفر',
      timeAgo: {
        justNow: 'ئێستا',
        seconds: '{s} چرکە پێش',
        minute: '1 خولەک پێش',
        minutes: '{s} خولەک پێش',
        hour: '1 کاتژمێر پێش',
        hours: '{s} کاتژمێر پێش',
        day: '1 ڕۆژ پێش',
        days: '{s} ڕۆژ پێش'
      },
      datetime: {
        days: {
          monday: 'دووشەممە',
          tuesday: 'سێشەممە',
          wednesday: 'چوارشەممە',
          thursday: 'پێنجشەممە',
          friday: 'هەینی',
          saturday: 'شەممە',
          sunday: 'یەکشەممە'
        },
        months: {
          january: 'کانوونی دووەم',
          february: 'شوبات',
          march: 'ئازار',
          april: 'نیسان',
          may: 'ئایار',
          june: 'حوزەیران',
          july: 'تەمموز',
          august: 'ئاب',
          september: 'ئەیلوول',
          october: 'تشرینی یەکەم',
          november: 'تشرینی دووەم',
          december: 'کانوونی یەکەم'
        }
      }
    },
    en: {
      title: 'Currency Rates',
      lastUpdate: 'Last Update',
      weather: {
        title: 'Weather',
        humidity: 'Humidity',
        wind: 'Wind',
        temperature: 'Temperature',
        loading: 'Loading...',
        error: 'Error loading data',
        error_not_supported: 'Geolocation is not supported by your browser',
        error_permission_denied: 'Location access was denied',
        error_unavailable: 'Location information is unavailable',
        error_timeout: 'Location request timed out',
        error_unknown: 'An unknown error occurred',
        refresh: 'Refresh',
        tap_refresh: 'Tap to refresh'
      },
      currency: 'Currency',
      code: 'Code',
      sale: 'Sale',
      buy: 'Buy',
      close: 'Close',
      clear: 'Clear',
      amount: 'Amount',
      buyAmount: 'Amount to Buy',
      sellAmount: 'Amount to Sell',
      buyRate: 'Buy Rate',
      sellRate: 'Sell Rate',
      iqdAmount: 'IQD Amount',
      usdAmount: 'USD Amount',
      secondAmount: 'Second Amount',
      divideByZero: 'Cannot divide by zero',
      timeAgo: {
        justNow: 'just now',
        seconds: '{s} seconds ago',
        minute: '1 minute ago',
        minutes: '{s} minutes ago',
        hour: '1 hour ago',
        hours: '{s} hours ago',
        day: '1 day ago',
        days: '{s} days ago'
      },
      datetime: {
        days: {
          monday: 'Monday',
          tuesday: 'Tuesday',
          wednesday: 'Wednesday',
          thursday: 'Thursday',
          friday: 'Friday',
          saturday: 'Saturday',
          sunday: 'Sunday'
        },
        months: {
          january: 'January',
          february: 'February',
          march: 'March',
          april: 'April',
          may: 'May',
          june: 'June',
          july: 'July',
          august: 'August',
          september: 'September',
          october: 'October',
          november: 'November',
          december: 'December'
        }
      }
    },
    ar: {
      title: 'أسعار العملات',
      lastUpdate: 'آخر تحديث',
      weather: {
        title: 'الطقس',
        humidity: 'الرطوبة',
        wind: 'الرياح',
        temperature: 'درجة الحرارة',
        loading: 'جار التحميل...',
        error: 'خطأ في تحميل البيانات',
        error_not_supported: 'متصفحك لا يدعم خدمة تحديد الموقع',
        error_permission_denied: 'تم رفض الوصول إلى الموقع',
        error_unavailable: 'معلومات الموقع غير متوفرة',
        error_timeout: 'انتهت مهلة طلب الموقع',
        error_unknown: 'حدث خطأ غير معروف',
        refresh: 'تحديث',
        tap_refresh: 'انقر للتحديث'
      },
      currency: 'العملة',
      code: 'الرمز',
      sale: 'بيع',
      buy: 'شراء',
      close: 'إغلاق',
      clear: 'مسح',
      amount: 'المبلغ',
      buyAmount: 'المبلغ للشراء',
      sellAmount: 'المبلغ للبيع',
      buyRate: 'سعر الشراء',
      sellRate: 'سعر البيع',
      iqdAmount: 'المبلغ بالدينار',
      usdAmount: 'المبلغ بالدولار',
      secondAmount: 'المبلغ الثاني',
      divideByZero: 'لا يمكن القسمة على صفر',
      timeAgo: {
        justNow: 'الآن',
        seconds: 'منذ {s} ثانية',
        minute: 'منذ دقيقة',
        minutes: 'منذ {s} دقائق',
        hour: 'منذ ساعة',
        hours: 'منذ {s} ساعات',
        day: 'منذ يوم',
        days: 'منذ {s} أيام'
      },
      datetime: {
        days: {
          monday: 'الإثنين',
          tuesday: 'الثلاثاء',
          wednesday: 'الأربعاء',
          thursday: 'الخميس',
          friday: 'الجمعة',
          saturday: 'السبت',
          sunday: 'الأحد'
        },
        months: {
          january: 'يناير',
          february: 'فبراير',
          march: 'مارس',
          april: 'أبريل',
          may: 'مايو',
          june: 'يونيو',
          july: 'يوليو',
          august: 'أغسطس',
          september: 'سبتمبر',
          october: 'أكتوبر',
          november: 'نوفمبر',
          december: 'ديسمبر'
        }
      }
    }
  };

  const currentLang = ref('ku');
  const languages = [
    { code: 'ku', name: 'کوردی', flag: 'iq' },
    { code: 'en', name: 'English', flag: 'gb' },
    { code: 'ar', name: 'العربية', flag: 'sa' }
  ];

  const t = computed(() => translations[currentLang.value]);
  const isRTL = computed(() => currentLang.value === 'ku' || currentLang.value === 'ar');

  return {
    currentLang,
    languages,
    t,
    isRTL
  };
}
