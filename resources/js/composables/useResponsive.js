import { ref, onMounted, onUnmounted } from 'vue';

export function useResponsive() {
  const isMobile = ref(false);
  const mouseX = ref(0);
  const mouseY = ref(0);
  const isParallaxEnabled = ref(true);

  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768;
  };

  const handleParallax = (event) => {
    if (!isParallaxEnabled.value) return;

    const rect = event.currentTarget.getBoundingClientRect();
    mouseX.value = (event.clientX - rect.left) / rect.width;
    mouseY.value = (event.clientY - rect.top) / rect.height;
  };

  const enableParallax = () => {
    isParallaxEnabled.value = true;
  };

  const disableParallax = () => {
    isParallaxEnabled.value = false;
    mouseX.value = 0;
    mouseY.value = 0;
  };

  const handleTouchStart = () => {
    disableParallax();
  };

  onMounted(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
  });

  return {
    isMobile,
    mouseX,
    mouseY,
    isParallaxEnabled,
    handleParallax,
    enableParallax,
    disableParallax,
    handleTouchStart
  };
}
