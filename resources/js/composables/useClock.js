import { ref, onMounted, onUnmounted } from 'vue';

export function useClock(t) {
  const digitalTime = ref('');
  const currentDate = ref('');

  const updateClock = () => {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    digitalTime.value = `${hours}:${minutes}:${seconds}`;

    // Update analog clock hands
    const hoursDeg = (now.getHours() % 12) * 30 + now.getMinutes() * 0.5;
    const minutesDeg = now.getMinutes() * 6;
    const secondsDeg = now.getSeconds() * 6;

    document.documentElement.style.setProperty('--hours-rotation', `${hoursDeg}deg`);
    document.documentElement.style.setProperty('--minutes-rotation', `${minutesDeg}deg`);
    document.documentElement.style.setProperty('--seconds-rotation', `${secondsDeg}deg`);

    // Update date
    const dayName = t.value.datetime.days[now.toLocaleDateString('en-US', { weekday: 'lowercase' })];
    const monthName = t.value.datetime.months[now.toLocaleDateString('en-US', { month: 'lowercase' })];
    const date = now.getDate();
    const year = now.getFullYear();

    currentDate.value = `${dayName}, ${monthName} ${date}, ${year}`;
  };

  let clockInterval;

  onMounted(() => {
    updateClock();
    clockInterval = setInterval(updateClock, 1000);
  });

  onUnmounted(() => {
    if (clockInterval) {
      clearInterval(clockInterval);
    }
  });

  return {
    digitalTime,
    currentDate,
    updateClock
  };
}
