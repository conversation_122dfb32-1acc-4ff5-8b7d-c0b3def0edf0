import { ref, computed } from 'vue';
import { useQuasar } from 'quasar';

export function useCurrencyExchange(t) {
  const $q = useQuasar();
  const currencies = ref([
    {
      "id": 1,
      "name": "دۆلار",
      "code": "us",
      "currency_code": "USD",
      "order": 1,
      "sale": 142700,
      'sale_status': 'up',
      "buy": 142300,
      "buy_status": 'down',
      "created_at": "28-03-2025 15:41",
      "updated_at": "08-05-2025 15:44"
    },
    // ... other currencies
  ]);

  const selectedCurrency = ref(null);
  const amount = ref(1);
  const operation = ref(null);
  const secondAmount = ref(null);
  const showSecondInput = ref(false);
  const result = ref(null);
  const activeTab = ref('buy');
  const calculationHistory = ref([]);

  const usdRate = computed(() => {
    const usd = currencies.value.find(c => c.currency_code === 'USD');
    return usd ? usd.buy : 1;
  });

  const formatCurrency = (value, currency = 'IQD') => {
    if (currency === 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    }
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value) + ' د.ع';
  };

  const convertToUSD = (amount, rate) => {
    return amount / rate;
  };

  const calculateExchange = () => {
    if (!selectedCurrency.value || !amount.value) return;

    const buyResult = amount.value * selectedCurrency.value.buy;
    const saleResult = amount.value * selectedCurrency.value.sale;

    result.value = {
      buy: {
        iqd: formatCurrency(buyResult),
        usd: formatCurrency(convertToUSD(buyResult, usdRate.value), 'USD')
      },
      sale: {
        iqd: formatCurrency(saleResult),
        usd: formatCurrency(convertToUSD(saleResult, usdRate.value), 'USD')
      },
      timestamp: new Date().toISOString()
    };

    // Add to history
    calculationHistory.value.unshift({
      currency: selectedCurrency.value,
      amount: amount.value,
      result: result.value,
      type: activeTab.value,
      timestamp: new Date().toISOString()
    });

    // Keep only last 5 calculations
    if (calculationHistory.value.length > 5) {
      calculationHistory.value.pop();
    }
  };

  const performOperation = (op) => {
    if (!amount.value) return;
    operation.value = op;
    showSecondInput.value = true;
    secondAmount.value = null;
  };

  const calculateFinal = () => {
    if (!amount.value || !secondAmount.value || !operation.value) return;

    let finalAmount;
    let operationSymbol;

    switch (operation.value) {
      case '+':
        finalAmount = amount.value + secondAmount.value;
        operationSymbol = '+';
        break;
      case '-':
        finalAmount = amount.value - secondAmount.value;
        operationSymbol = '-';
        break;
      case '*':
        finalAmount = amount.value * secondAmount.value;
        operationSymbol = '×';
        break;
      case '/':
        if (secondAmount.value === 0) {
          $q.notify({
            message: t.value.divideByZero,
            color: 'negative',
            icon: 'error',
            position: 'top'
          });
          return;
        }
        finalAmount = amount.value / secondAmount.value;
        operationSymbol = '÷';
        break;
      default:
        return;
    }

    amount.value = finalAmount;
    calculateExchange();
    showSecondInput.value = false;
    operation.value = null;
    secondAmount.value = null;
  };

  const clearOperation = () => {
    operation.value = null;
    showSecondInput.value = false;
    secondAmount.value = null;
  };

  const clearAll = () => {
    amount.value = 1;
    clearOperation();
    calculateExchange();
  };

  return {
    currencies,
    selectedCurrency,
    amount,
    operation,
    secondAmount,
    showSecondInput,
    result,
    activeTab,
    calculationHistory,
    formatCurrency,
    calculateExchange,
    performOperation,
    calculateFinal,
    clearOperation,
    clearAll,
  };
}