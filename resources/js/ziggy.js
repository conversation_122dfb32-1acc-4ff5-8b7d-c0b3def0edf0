const Ziggy = {"url":"http:\/\/da<PERSON>y-bawar.test","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"dashboard":{"uri":"api\/dashboard","methods":["GET","HEAD"]},"currencies.index":{"uri":"api\/admin\/currencies","methods":["GET","HEAD"]},"currencies.store":{"uri":"api\/admin\/currencies","methods":["POST"]},"currencies.show":{"uri":"api\/admin\/currencies\/{currency}","methods":["GET","HEAD"],"parameters":["currency"],"bindings":{"currency":"id"}},"currencies.update":{"uri":"api\/admin\/currencies\/{currency}","methods":["PUT","PATCH"],"parameters":["currency"],"bindings":{"currency":"id"}},"currencies.destroy":{"uri":"api\/admin\/currencies\/{currency}","methods":["DELETE"],"parameters":["currency"],"bindings":{"currency":"id"}},"exchange-rates.index":{"uri":"api\/admin\/exchange-rates","methods":["GET","HEAD"]},"exchange-rates.store":{"uri":"api\/admin\/exchange-rates","methods":["POST"]},"exchange-rates.show":{"uri":"api\/admin\/exchange-rates\/{exchange_rate}","methods":["GET","HEAD"],"parameters":["exchange_rate"]},"exchange-rates.update":{"uri":"api\/admin\/exchange-rates\/{exchange_rate}","methods":["PUT","PATCH"],"parameters":["exchange_rate"]},"exchange-rates.destroy":{"uri":"api\/admin\/exchange-rates\/{exchange_rate}","methods":["DELETE"],"parameters":["exchange_rate"]},"language.switch":{"uri":"language\/{locale}","methods":["GET","HEAD"],"parameters":["locale"]},"admin.dashboard":{"uri":"admin","methods":["GET","HEAD"]},"admin.dashboard.rates":{"uri":"admin\/dashboard\/rates","methods":["GET","HEAD"]},"admin.currencies.index":{"uri":"admin\/currencies","methods":["GET","HEAD"]},"admin.currencies.create":{"uri":"admin\/currencies\/create","methods":["GET","HEAD"]},"admin.currencies.store":{"uri":"admin\/currencies","methods":["POST"]},"admin.currencies.edit":{"uri":"admin\/currencies\/{currency}\/edit","methods":["GET","HEAD"],"parameters":["currency"],"bindings":{"currency":"id"}},"admin.currencies.update":{"uri":"admin\/currencies\/{currency}","methods":["PUT","PATCH"],"parameters":["currency"],"bindings":{"currency":"id"}},"admin.currencies.destroy":{"uri":"admin\/currencies\/{currency}","methods":["DELETE"],"parameters":["currency"],"bindings":{"currency":"id"}},"admin.currencies.toggle-status":{"uri":"admin\/currencies\/{currency}\/toggle-status","methods":["PATCH"],"parameters":["currency"],"bindings":{"currency":"id"}},"admin.currency-exchange-rates.index":{"uri":"admin\/currency-exchange-rates","methods":["GET","HEAD"]},"admin.currency-exchange-rates.create":{"uri":"admin\/currency-exchange-rates\/create","methods":["GET","HEAD"]},"admin.currency-exchange-rates.store":{"uri":"admin\/currency-exchange-rates","methods":["POST"]},"admin.currency-exchange-rates.edit":{"uri":"admin\/currency-exchange-rates\/{currency_exchange_rate}\/edit","methods":["GET","HEAD"],"parameters":["currency_exchange_rate"]},"admin.currency-exchange-rates.update":{"uri":"admin\/currency-exchange-rates\/{currency_exchange_rate}","methods":["PUT","PATCH"],"parameters":["currency_exchange_rate"]},"admin.currency-exchange-rates.destroy":{"uri":"admin\/currency-exchange-rates\/{currency_exchange_rate}","methods":["DELETE"],"parameters":["currency_exchange_rate"]},"admin.currency-exchange-rates.toggle-status":{"uri":"admin\/currency-exchange-rates\/{currencyExchangeRate}\/toggle-status","methods":["PATCH"],"parameters":["currencyExchangeRate"],"bindings":{"currencyExchangeRate":"id"}},"admin.currency-exchange-rates.bulk-update":{"uri":"admin\/currency-exchange-rates\/bulk-update","methods":["PATCH"]},"admin.currency-exchange-rates.get-rate":{"uri":"admin\/currency-exchange-rates\/get-rate","methods":["GET","HEAD"]},"admin.exchange-rate-history.index":{"uri":"admin\/exchange-rate-history","methods":["GET","HEAD"]},"admin.exchange-rate-history.create":{"uri":"admin\/exchange-rate-history\/create","methods":["GET","HEAD"]},"admin.exchange-rate-history.store":{"uri":"admin\/exchange-rate-history","methods":["POST"]},"admin.exchange-rate-history.show":{"uri":"admin\/exchange-rate-history\/{exchange_rate_history}","methods":["GET","HEAD"],"parameters":["exchange_rate_history"]},"admin.exchange-rate-history.edit":{"uri":"admin\/exchange-rate-history\/{exchange_rate_history}\/edit","methods":["GET","HEAD"],"parameters":["exchange_rate_history"]},"admin.exchange-rate-history.update":{"uri":"admin\/exchange-rate-history\/{exchange_rate_history}","methods":["PUT","PATCH"],"parameters":["exchange_rate_history"]},"admin.exchange-rate-history.destroy":{"uri":"admin\/exchange-rate-history\/{exchange_rate_history}","methods":["DELETE"],"parameters":["exchange_rate_history"]},"admin.exchange-rate-history.chart-data":{"uri":"admin\/exchange-rate-history\/chart-data","methods":["GET","HEAD"]},"admin.exchange-rate-history.analytics":{"uri":"admin\/exchange-rate-history\/analytics","methods":["GET","HEAD"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"exchange":{"uri":"exchange","methods":["GET","HEAD"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
