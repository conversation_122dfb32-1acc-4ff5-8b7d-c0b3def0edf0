import { usePage } from '@inertiajs/vue3'

export const hasPermission = (permission) => {
    const page = usePage()
    const user = page?.props?.auth?.user
    if (!user) {
        return false
    }

    // Check if user has the permission directly
    if (user.permissions?.some(p => p.name === permission)) {
        return true
    }

    // Check if any of user's roles have the permission
    return user.roles?.some(role =>
        role.permissions?.some(p => p.name === permission)
    )
}

export const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission))
}

export const hasAllPermissions = (permissions) => {
    return permissions.every(permission => hasPermission(permission))
}

export const hasRole = (role) => {
    const page = usePage()
    const user = page?.props?.auth?.user
    if (!user) {
        return false
    }

    return user.roles?.some(r => r.name === role)
}

export const hasAnyRole = (roles) => {
    return roles.some(role => hasRole(role))
}

export const hasAllRoles = (roles) => {
    return roles.every(role => hasRole(role))
}

export const getUserPermissions = () => {
    const page = usePage()
    const user = page?.props?.auth?.user
    if (!user) {
        return []
    }

    const directPermissions = user.permissions || []
    const rolePermissions = user.roles?.flatMap(role => role.permissions || []) || []

    // Combine and deduplicate permissions
    return [...new Set([...directPermissions, ...rolePermissions])]
}

export const getUserRoles = () => {
    const page = usePage()
    const user = page?.props?.auth?.user
    if (!user) {
        return []
    }

    return user.roles || []
}
