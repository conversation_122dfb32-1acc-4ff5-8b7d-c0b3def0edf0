/**
 * API utility functions for making HTTP requests
 */

/**
 * Get CSRF token from meta tag
 */
const getCsrfToken = () => {
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
}

/**
 * Default headers for API requests
 */
const getDefaultHeaders = () => {
    return {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': getCsrfToken(),
    }
}

/**
 * Handle API response
 */
const handleResponse = async (response) => {
    const data = await response.json()
    
    if (!response.ok) {
        const error = new Error(data.message || 'API request failed')
        error.status = response.status
        error.data = data
        throw error
    }
    
    return data
}

/**
 * Make a GET request
 */
export const apiGet = async (url, params = {}) => {
    const urlParams = new URLSearchParams(params)
    const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url
    
    const response = await fetch(fullUrl, {
        method: 'GET',
        headers: getDefaultHeaders(),
        credentials: 'same-origin'
    })
    
    return handleResponse(response)
}

/**
 * Make a POST request
 */
export const apiPost = async (url, data = {}) => {
    const response = await fetch(url, {
        method: 'POST',
        headers: getDefaultHeaders(),
        credentials: 'same-origin',
        body: JSON.stringify(data)
    })
    
    return handleResponse(response)
}

/**
 * Make a PUT request
 */
export const apiPut = async (url, data = {}) => {
    const response = await fetch(url, {
        method: 'PUT',
        headers: getDefaultHeaders(),
        credentials: 'same-origin',
        body: JSON.stringify(data)
    })
    
    return handleResponse(response)
}

/**
 * Make a PATCH request
 */
export const apiPatch = async (url, data = {}) => {
    const response = await fetch(url, {
        method: 'PATCH',
        headers: getDefaultHeaders(),
        credentials: 'same-origin',
        body: JSON.stringify(data)
    })
    
    return handleResponse(response)
}

/**
 * Make a DELETE request
 */
export const apiDelete = async (url) => {
    const response = await fetch(url, {
        method: 'DELETE',
        headers: getDefaultHeaders(),
        credentials: 'same-origin'
    })
    
    return handleResponse(response)
}

/**
 * User API functions
 */
export const userApi = {
    // Get all users with filters
    getUsers: (params = {}) => apiGet('/api/admin/users', params),
    
    // Get user statistics
    getStats: () => apiGet('/api/admin/users/stats'),
    
    // Get single user
    getUser: (id) => apiGet(`/api/admin/users/${id}`),
    
    // Create user
    createUser: (data) => apiPost('/api/admin/users', data),
    
    // Update user
    updateUser: (id, data) => apiPut(`/api/admin/users/${id}`, data),
    
    // Toggle user status
    toggleStatus: (id) => apiPatch(`/api/admin/users/${id}/toggle-status`),
    
    // Update user roles
    updateRoles: (id, roles) => apiPatch(`/api/admin/users/${id}/update-roles`, { roles }),
    
    // Delete user
    deleteUser: (id) => apiDelete(`/api/admin/users/${id}`)
}

/**
 * Role API functions
 */
export const roleApi = {
    // Get all roles
    getRoles: (params = {}) => apiGet('/api/admin/roles', params),
    
    // Get single role
    getRole: (id) => apiGet(`/api/admin/roles/${id}`),
    
    // Create role
    createRole: (data) => apiPost('/api/admin/roles', data),
    
    // Update role
    updateRole: (id, data) => apiPut(`/api/admin/roles/${id}`, data),
    
    // Update role permissions
    updatePermissions: (id, permissions) => apiPatch(`/api/admin/roles/${id}/update-permissions`, { permissions }),
    
    // Delete role
    deleteRole: (id) => apiDelete(`/api/admin/roles/${id}`)
}

/**
 * Permission API functions
 */
export const permissionApi = {
    // Get all permissions
    getPermissions: (params = {}) => apiGet('/api/admin/permissions', params),
    
    // Get single permission
    getPermission: (id) => apiGet(`/api/admin/permissions/${id}`),
    
    // Create permission
    createPermission: (data) => apiPost('/api/admin/permissions', data),
    
    // Update permission
    updatePermission: (id, data) => apiPut(`/api/admin/permissions/${id}`, data),
    
    // Delete permission
    deletePermission: (id) => apiDelete(`/api/admin/permissions/${id}`)
}

/**
 * Error handler for API calls
 */
export const handleApiError = (error, $q) => {
    console.error('API Error:', error)
    
    let message = 'An error occurred'
    
    if (error.status === 422 && error.data?.errors) {
        // Validation errors
        const firstError = Object.values(error.data.errors)[0]
        message = Array.isArray(firstError) ? firstError[0] : firstError
    } else if (error.data?.message) {
        message = error.data.message
    } else if (error.message) {
        message = error.message
    }
    
    $q.notify({
        type: 'negative',
        message: message,
        position: 'top'
    })
    
    return error
}
