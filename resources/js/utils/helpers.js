export function formatCurrency(amount, currency = null) {
    if (!currency) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    return `${currency.symbol} ${amount.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })}`;
}

export function convertCurrency(amount, fromCurrency, toCurrency) {
    if (fromCurrency.id === toCurrency.id) {
        return amount;
    }

    // Convert to default currency first
    const amountInDefault = amount * fromCurrency.exchange_rate;

    // Then convert to target currency
    return amountInDefault / toCurrency.exchange_rate;
}
