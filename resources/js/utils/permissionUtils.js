// Permission naming convention utilities
export const standardizePermissionName = (permission) => {
    // Convert from "view users" to "users.view" format
    if (permission.includes(' ')) {
        const [action, ...resource] = permission.split(' ');
        return `${resource.join('.')}.${action}`;
    }
    return permission;
};

export const unstandardizePermissionName = (permission) => {
    // Convert from "users.view" to "view users" format
    if (permission.includes('.')) {
        const [resource, action] = permission.split('.');
        return `${action} ${resource}`;
    }
    return permission;
};

// Permission validation utilities
export const validatePermission = (permission, userPermissions) => {
    const standardizedPermission = standardizePermissionName(permission);
    return userPermissions.some(p =>
        standardizePermissionName(p.name) === standardizedPermission
    );
};

// Permission grouping utilities
export const groupPermissionsByCategory = (permissions) => {
    const groups = {};
    permissions.forEach(permission => {
        const [category] = permission.name.split('.');
        if (!groups[category]) {
            groups[category] = [];
        }
        groups[category].push(permission);
    });
    return groups;
};

// Permission mapping
export const PERMISSION_MAP = {
    // Dashboard
    'dashboard.view': 'View Dashboard',
    'dashboard.statistics': 'View Dashboard Statistics',
    'dashboard.charts': 'View Dashboard Charts',
    'dashboard.alerts': 'View Dashboard Alerts',

    // User Management
    'users.view': 'View Users',
    'users.create': 'Create Users',
    'users.edit': 'Edit Users',
    'users.delete': 'Delete Users',
    'users.manage': 'Manage Users',

    // Role Management
    'roles.view': 'View Roles',
    'roles.create': 'Create Roles',
    'roles.edit': 'Edit Roles',
    'roles.delete': 'Delete Roles',
    'roles.manage': 'Manage Roles',

    // Permission Management
    'permissions.view': 'View Permissions',
    'permissions.create': 'Create Permissions',
    'permissions.edit': 'Edit Permissions',
    'permissions.delete': 'Delete Permissions',
    'permissions.manage': 'Manage Permissions',

    // Product Management
    'products.view': 'View Products',
    'products.create': 'Create Products',
    'products.edit': 'Edit Products',
    'products.delete': 'Delete Products',
    'products.import': 'Import Products',
    'products.export': 'Export Products',
    'products.manage': 'Manage Products',

    // Category Management
    'categories.view': 'View Categories',
    'categories.create': 'Create Categories',
    'categories.edit': 'Edit Categories',
    'categories.delete': 'Delete Categories',
    'categories.manage': 'Manage Categories',

    // Order Management
    'orders.view': 'View Orders',
    'orders.create': 'Create Orders',
    'orders.edit': 'Edit Orders',
    'orders.delete': 'Delete Orders',
    'orders.approve': 'Approve Orders',
    'orders.reject': 'Reject Orders',
    'orders.print': 'Print Orders',
    'orders.export': 'Export Orders',
    'orders.manage': 'Manage Orders',

    // Sales Management
    'sales.view': 'View Sales',
    'sales.create': 'Create Sales',
    'sales.edit': 'Edit Sales',
    'sales.delete': 'Delete Sales',
    'sales.approve': 'Approve Sales',
    'sales.reject': 'Reject Sales',
    'sales.print': 'Print Sales',
    'sales.export': 'Export Sales',
    'sales.manage': 'Manage Sales',

    // Inventory Management
    'inventory.view': 'View Inventory',
    'inventory.create': 'Create Inventory',
    'inventory.edit': 'Edit Inventory',
    'inventory.delete': 'Delete Inventory',
    'inventory.import': 'Import Inventory',
    'inventory.export': 'Export Inventory',
    'inventory.manage': 'Manage Inventory',

    // Customer Management
    'customers.view': 'View Customers',
    'customers.create': 'Create Customers',
    'customers.edit': 'Edit Customers',
    'customers.delete': 'Delete Customers',
    'customers.manage': 'Manage Customers',

    // Supplier Management
    'suppliers.view': 'View Suppliers',
    'suppliers.create': 'Create Suppliers',
    'suppliers.edit': 'Edit Suppliers',
    'suppliers.delete': 'Delete Suppliers',
    'suppliers.manage': 'Manage Suppliers',

    // Payment Management
    'payments.view': 'View Payments',
    'payments.create': 'Create Payments',
    'payments.edit': 'Edit Payments',
    'payments.delete': 'Delete Payments',
    'payments.approve': 'Approve Payments',
    'payments.reject': 'Reject Payments',
    'payments.print': 'Print Payments',
    'payments.export': 'Export Payments',
    'payments.manage': 'Manage Payments',

    // Report Management
    'reports.view': 'View Reports',
    'reports.create': 'Create Reports',
    'reports.export': 'Export Reports',
    'reports.print': 'Print Reports',
    'reports.manage': 'Manage Reports',

    // Settings Management
    'settings.view': 'View Settings',
    'settings.edit': 'Edit Settings',
    'settings.manage': 'Manage Settings',

    // Activity Logs
    'activity-logs.view': 'View Activity Logs',
    'activity-logs.export': 'Export Activity Logs',
    'activity-logs.manage': 'Manage Activity Logs',

    // System Management
    'system.view': 'View System',
    'system.manage': 'Manage System',

    // Backup Management
    'backup.view': 'View Backups',
    'backup.create': 'Create Backups',
    'backup.restore': 'Restore Backups',
    'backup.delete': 'Delete Backups',
    'backup.manage': 'Manage Backups'
};
