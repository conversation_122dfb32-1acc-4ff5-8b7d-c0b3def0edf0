<template>
  <q-layout view="lHh Lpr lFf">
    <!-- Header -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
          class="q-mr-sm"
        />

        <q-toolbar-title class="text-weight-bold">
          <div class="row items-center">
            <q-icon name="admin_panel_settings" size="sm" class="q-mr-sm text-primary" />
            Admin Panel
          </div>
        </q-toolbar-title>

        <div class="q-gutter-sm row items-center no-wrap">
          <!-- Notifications -->
          <q-btn flat round dense icon="notifications">
            <q-badge color="red" floating>{{ notifications.length }}</q-badge>
            <q-menu>
              <q-list style="min-width: 300px">
                <q-item-label header>Notifications</q-item-label>
                <q-item
                  v-for="notification in notifications"
                  :key="notification.id"
                  clickable
                  v-close-popup
                >
                  <q-item-section avatar>
                    <q-icon :name="notification.icon" :color="notification.color" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ notification.title }}</q-item-label>
                    <q-item-label caption>{{ notification.message }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <q-item-label caption>{{ notification.time }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-separator />
                <q-item clickable>
                  <q-item-section>
                    <q-item-label class="text-center text-primary">View All</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>

          <!-- User Menu -->
          <q-btn flat round>
            <q-avatar size="32px">
              <img :src="user.avatar || '/images/default-avatar.png'" />
            </q-avatar>
            <q-menu>
              <q-list style="min-width: 200px">
                <q-item-label header>{{ user.name }}</q-item-label>
                <q-item clickable @click="$inertia.visit(route('profile.edit'))">
                  <q-item-section avatar>
                    <q-icon name="person" />
                  </q-item-section>
                  <q-item-section>Profile</q-item-section>
                </q-item>
                <q-item clickable @click="$inertia.visit(route('admin.settings'))">
                  <q-item-section avatar>
                    <q-icon name="settings" />
                  </q-item-section>
                  <q-item-section>Settings</q-item-section>
                </q-item>
                <q-separator />
                <q-item clickable @click="logout">
                  <q-item-section avatar>
                    <q-icon name="logout" />
                  </q-item-section>
                  <q-item-section>Logout</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>
        </div>
      </q-toolbar>
    </q-header>

    <!-- Left Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      class="bg-grey-1"
      :width="280"
    >
      <q-scroll-area class="fit">
        <div class="q-pa-md">
          <!-- Logo/Brand -->
          <div class="text-center q-mb-lg">
            <div class="text-h6 text-weight-bold text-primary">Currency Exchange</div>
            <div class="text-caption text-grey-6">Admin Dashboard</div>
          </div>

          <!-- Navigation Menu -->
          <q-list>
            <!-- Dashboard -->
            <q-item
              clickable
              @click="$inertia.visit(route('admin.dashboard'))"
              :active="page.component === 'Admin/Dashboard'"
              active-class="bg-primary text-white"
              class="rounded-borders q-mb-xs"
            >
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Dashboard</q-item-label>
              </q-item-section>
            </q-item>

            <!-- Currencies -->
            <q-expansion-item
              icon="account_balance"
              label="Currencies"
              :default-opened="isCurrentSection('currencies')"
              header-class="text-weight-medium"
            >
              <q-item
                clickable
                @click="$inertia.visit(route('admin.currencies.index'))"
                :active="page.component === 'Admin/Currencies/Index'"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="list" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>All Currencies</q-item-label>
                </q-item-section>
              </q-item>

              <q-item
                clickable
                @click="$inertia.visit(route('admin.currencies.create'))"
                :active="page.component === 'Admin/Currencies/Create'"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="add" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Add Currency</q-item-label>
                </q-item-section>
              </q-item>
            </q-expansion-item>

            <!-- Exchange Rates -->
            <q-expansion-item
              icon="swap_horiz"
              label="Exchange Rates"
              :default-opened="isCurrentSection('exchange-rates')"
              header-class="text-weight-medium"
            >
              <q-item
                clickable
                @click="$inertia.visit(route('admin.currency-exchange-rates.index'))"
                :active="page.component === 'Admin/ExchangeRates/Index'"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="list" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>All Rates</q-item-label>
                </q-item-section>
              </q-item>

              <q-item
                clickable
                @click="$inertia.visit(route('admin.currency-exchange-rates.create'))"
                :active="page.component === 'Admin/ExchangeRates/Create'"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="add" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Add Rate</q-item-label>
                </q-item-section>
              </q-item>
            </q-expansion-item>

            <!-- History -->
            <q-item
              clickable
              @click="$inertia.visit(route('admin.exchange-rate-history.index'))"
              :active="page.component?.includes('ExchangeRateHistory')"
              active-class="bg-primary text-white"
              class="rounded-borders q-mb-xs"
            >
              <q-item-section avatar>
                <q-icon name="history" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Rate History</q-item-label>
              </q-item-section>
            </q-item>

            <!-- Analytics -->
            <q-item
              clickable
              @click="$inertia.visit(route('admin.analytics'))"
              :active="page.component === 'Admin/Analytics'"
              active-class="bg-primary text-white"
              class="rounded-borders q-mb-xs"
            >
              <q-item-section avatar>
                <q-icon name="analytics" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Analytics</q-item-label>
              </q-item-section>
            </q-item>

            <q-separator class="q-my-md" />

            <!-- Settings -->
            <q-item
              clickable
              @click="$inertia.visit(route('admin.settings'))"
              :active="page.component === 'Admin/Settings'"
              active-class="bg-primary text-white"
              class="rounded-borders q-mb-xs"
            >
              <q-item-section avatar>
                <q-icon name="settings" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Settings</q-item-label>
              </q-item-section>
            </q-item>

            <!-- User Management -->
            <q-expansion-item
              icon="people"
              label="User Management"
              :default-opened="isCurrentSection('users') || isCurrentSection('roles') || isCurrentSection('permissions')"
              header-class="text-weight-medium"
            >
              <q-item
                clickable
                @click="$inertia.visit(route('admin.users.index'))"
                :active="page.component?.includes('Admin/Users')"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="person" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Users</q-item-label>
                </q-item-section>
              </q-item>

              <q-item
                clickable
                @click="$inertia.visit(route('admin.roles.index'))"
                :active="page.component?.includes('Admin/Roles')"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="admin_panel_settings" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Roles</q-item-label>
                </q-item-section>
              </q-item>

              <q-item
                clickable
                @click="$inertia.visit(route('admin.permissions.index'))"
                :active="page.component?.includes('Admin/Permissions')"
                active-class="bg-blue-1 text-primary"
                class="q-ml-md rounded-borders"
              >
                <q-item-section avatar>
                  <q-icon name="key" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>Permissions</q-item-label>
                </q-item-section>
              </q-item>
            </q-expansion-item>
          </q-list>

          <!-- System Status -->
          <div class="q-mt-xl">
            <q-card flat bordered class="bg-white">
              <q-card-section class="q-pa-sm">
                <div class="text-caption text-weight-medium q-mb-xs">System Status</div>
                <div class="row items-center q-gutter-xs">
                  <q-chip
                    :color="systemStatus.api ? 'positive' : 'negative'"
                    text-color="white"
                    size="xs"
                  >
                    API
                  </q-chip>
                  <q-chip
                    :color="systemStatus.database ? 'positive' : 'negative'"
                    text-color="white"
                    size="xs"
                  >
                    DB
                  </q-chip>
                  <q-chip
                    :color="systemStatus.rates ? 'positive' : 'warning'"
                    text-color="white"
                    size="xs"
                  >
                    Rates
                  </q-chip>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-scroll-area>
    </q-drawer>

    <!-- Page Content -->
    <q-page-container>
      <slot />
    </q-page-container>

    <!-- Footer -->
    <q-footer class="bg-grey-8 text-white">
      <q-toolbar class="justify-between">
        <div class="text-caption">
          © 2024 Currency Exchange Admin. All rights reserved.
        </div>
        <div class="text-caption">
          Version 1.0.0
        </div>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { usePage } from '@inertiajs/vue3'
import { route } from 'ziggy-js'
import axios from 'axios'

const page = usePage()

// Reactive data
const leftDrawerOpen = ref(false)

const notifications = ref([
  {
    id: 1,
    icon: 'warning',
    color: 'warning',
    title: 'Stale Exchange Rates',
    message: 'Some rates haven\'t been updated in over 1 hour',
    time: '5m ago'
  },
  {
    id: 2,
    icon: 'info',
    color: 'info',
    title: 'System Update',
    message: 'New features available in admin panel',
    time: '1h ago'
  }
])

const systemStatus = ref({
  api: true,
  database: true,
  rates: true
})

// Computed properties
const user = computed(() => page.props.auth?.user || {
  name: 'Admin User',
  email: '<EMAIL>',
  avatar: null
})

// Methods
const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

const isCurrentSection = (section) => {
  return page.component?.includes(section)
}

const logout = async () => {
  try {
    await axios.post('/logout')
    window.location.href = '/login'
  } catch (error) {
    console.error('Logout error:', error)
  }
}

const checkSystemStatus = async () => {
  try {
    // Check API status
    const apiResponse = await axios.get('/api/exchange/update-status')
    systemStatus.value.api = apiResponse.status === 200
    systemStatus.value.rates = !apiResponse.data.data?.is_stale
  } catch (error) {
    systemStatus.value.api = false
    systemStatus.value.rates = false
  }
}

// Lifecycle
onMounted(() => {
  checkSystemStatus()

  // Check system status every 5 minutes
  setInterval(checkSystemStatus, 5 * 60 * 1000)
})
</script>

<style scoped>
.q-layout {
  min-height: 100vh;
}

.q-drawer {
  border-right: 1px solid #e0e0e0;
}

.q-item {
  margin-bottom: 2px;
}

.q-expansion-item {
  margin-bottom: 4px;
}

.rounded-borders {
  border-radius: 8px;
}

.q-toolbar-title {
  font-size: 1.2rem;
}

@media (max-width: 768px) {
  .q-toolbar-title {
    font-size: 1rem;
  }
}
</style>
