/* Fonts - Must be at the top */
@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --bg-primary: linear-gradient(135deg, #e0edfa 0%, #f0f6fd 100%);
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --card-bg: rgba(255, 255, 255, 0.95);
  --header-bg: linear-gradient(135deg, rgba(222, 239, 255, 0.92) 0%, rgba(230, 244, 255, 0.86) 100%);
  --panel-bg: rgba(255, 255, 255, 0.1);
  --primary-color: #2563eb;
  --primary-color-light: rgba(37, 99, 235, 0.12);
  --primary-color-hover: rgba(37, 99, 235, 0.18);
  --shadow-color: rgba(37, 99, 235, 0.16);
  --border-color: rgba(37, 99, 235, 0.25);
  --wave-color: #2563eb;
}

.dark-theme {
  --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --card-bg: rgba(30, 41, 59, 0.95);
  --header-bg: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(30, 41, 59, 0.85) 100%);
  --panel-bg: rgba(15, 23, 42, 0.3);
  --primary-color: #60a5fa;
  --primary-color-light: rgba(96, 165, 250, 0.15);
  --primary-color-hover: rgba(96, 165, 250, 0.25);
  --shadow-color: rgba(15, 23, 42, 0.35);
  --border-color: rgba(99, 102, 241, 0.25);
  --wave-color: #6366f1;
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

/* Animations */
@keyframes headerAppear {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes borderFlow {
  0% {
    background-position: 0% 50%;
  }

  100% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-150%) skewX(-15deg);
  }

  100% {
    transform: translateX(150%) skewX(-15deg);
  }
}

@keyframes rise {
  0% {
    transform: translateY(0);
    opacity: 0.5;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    transform: translateY(-300px);
    opacity: 0;
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes bottomWave {
  0% {
    background-position: 0 calc(0px - var(--wave-scroll-offset, 0px)),
      0 calc(20px - var(--wave-scroll-offset, 0px)),
      0 calc(40px - var(--wave-scroll-offset, 0px));
  }

  100% {
    background-position: 1440px calc(0px - var(--wave-scroll-offset, 0px)),
      -1440px calc(20px - var(--wave-scroll-offset, 0px)),
      1440px calc(40px - var(--wave-scroll-offset, 0px));
  }
}



/* Font settings */
.rtl-text {
  direction: rtl;
  text-align: right;
  font-family: 'Vazirmatn', 'Tajawal', sans-serif;
}

.ltr-text {
  direction: ltr;
  text-align: left;
}

/* Global styles */
body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: background 0.3s ease, color 0.3s ease;
}

/* Safe area insets for mobile */
@supports (padding-top: env(safe-area-inset-top)) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Utility classes */
.animate-fade {
  animation: fadeIn 1s ease forwards;
}
