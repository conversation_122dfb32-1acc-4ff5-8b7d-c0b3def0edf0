/* Import Quasar's styles */
@import 'quasar/dist/quasar.css';

/*
 * Enhanced Quasar UI Theme - Daryay <PERSON>war Admin Panel
 * Modern design system with enhanced accessibility and admin-focused styling
 */
:root {
    /* Primary Brand Colors - Enhanced for Admin */
    --q-primary: #1e40af;       /* Professional blue for admin */
    --q-primary-light: #3b82f6; /* Lighter variant for hovers */
    --q-primary-dark: #1e3a8a;  /* Darker variant for active states */
    --q-secondary: #0f766e;     /* Teal for secondary actions */
    --q-accent: #f59e0b;        /* Amber accent for important CTAs */

    /* Admin-specific colors */
    --q-admin-bg: #f8fafc;      /* Light admin background */
    --q-admin-surface: #ffffff; /* Card/surface background */
    --q-admin-border: #e2e8f0;  /* Border color */
    --q-admin-text: #1e293b;    /* Primary text */
    --q-admin-text-light: #64748b; /* Secondary text */

    /* Neutral Colors */
    --q-dark: #0f172a;          /* Text/headers (improved contrast) */
    --q-gray-900: #1e293b;      /* Dark gray */
    --q-gray-700: #334155;      /* Medium gray */
    --q-gray-500: #64748b;      /* Medium-light gray */
    --q-gray-300: #cbd5e1;      /* Light gray for borders */
    --q-gray-100: #f1f5f9;      /* Very light gray backgrounds */
    --q-light: #f8fafc;         /* Off-white backgrounds */
    --q-background: #ffffff;    /* Default white */

    /* Status Colors */
    --q-positive: #10b981;      /* Success green (more vibrant) */
    --q-negative: #ef4444;      /* Error red (more vibrant) */
    --q-info: #3b82f6;          /* Informational blue (more vibrant) */
    --q-warning: #f59e0b;       /* Warning amber (more vibrant) */

    /* Extended Brand Colors */
    --q-currency-dark: #023456; /* Secondary brand from logo */
    --q-currency-light: #ebf5ff;/* Light blue tint (enhanced) */

    /* Shadows */
    --q-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --q-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --q-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Transitions */
    --q-transition-fast: 150ms;
    --q-transition-normal: 250ms;
    --q-transition-slow: 350ms;

    /* Border Radius */
    --q-radius-sm: 0.25rem;
    --q-radius-md: 0.375rem;
    --q-radius-lg: 0.5rem;
    --q-radius-xl: 0.75rem;
    --q-radius-2xl: 1rem;
    --q-radius-full: 9999px;

    /* Admin-specific enhancements */
    --q-admin-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --q-admin-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
    --q-admin-gradient-primary: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    --q-admin-gradient-secondary: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
    --q-admin-gradient-accent: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
}

/* Dark Mode Theme */
.dark {
    --q-dark: #f8fafc;
    --q-light: #1e293b;
    --q-background: #0f172a;
    --q-primary-light: #1e429f;
    --q-currency-light: #1e3a5f;
    --q-gray-900: #f1f5f9;
    --q-gray-700: #cbd5e1;
    --q-gray-500: #94a3b8;
    --q-gray-300: #475569;
    --q-gray-100: #1e293b;
}

/* Font families */
html {
    font-family: Figtree, system-ui, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: var(--q-dark);
    background-color: var(--q-background);
    transition: background-color var(--q-transition-normal) ease;
}

[dir="rtl"] {
    font-family: Vazirmatn, system-ui, sans-serif;
}

/* Base element styling */
body {
    margin: 0;
    padding: 0;
}

a {
    color: var(--q-primary);
    text-decoration: none;
    transition: color var(--q-transition-fast) ease;
}

a:hover {
    color: var(--q-primary-light);
}

/* Custom utility classes */
.shadow-sm { box-shadow: var(--q-shadow-sm); }
.shadow-md { box-shadow: var(--q-shadow-md); }
.shadow-lg { box-shadow: var(--q-shadow-lg); }

.rounded-sm { border-radius: var(--q-radius-sm); }
.rounded-md { border-radius: var(--q-radius-md); }
.rounded-lg { border-radius: var(--q-radius-lg); }
.rounded-xl { border-radius: var(--q-radius-xl); }
.rounded-2xl { border-radius: var(--q-radius-2xl); }
.rounded-full { border-radius: var(--q-radius-full); }

/* ===== ENHANCED ADMIN PANEL STYLES ===== */

/* Modern Admin Layout */
.admin-layout {
    background: var(--q-admin-bg);
    min-height: 100vh;
}

.admin-header {
    background: var(--q-admin-surface);
    border-bottom: 1px solid var(--q-admin-border);
    box-shadow: var(--q-shadow-sm);
}

.admin-sidebar {
    background: var(--q-admin-surface);
    border-right: 1px solid var(--q-admin-border);
}

/* Enhanced Cards */
.admin-card {
    background: var(--q-admin-surface);
    border: 1px solid var(--q-admin-border);
    border-radius: var(--q-radius-xl);
    box-shadow: var(--q-admin-shadow);
    transition: all var(--q-transition-normal) ease;
}

.admin-card:hover {
    box-shadow: var(--q-admin-shadow-hover);
    transform: translateY(-2px);
}

.admin-card-header {
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid var(--q-admin-border);
    margin-bottom: 24px;
}

.admin-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--q-admin-text);
    margin: 0 0 8px 0;
}

.admin-card-subtitle {
    font-size: 0.875rem;
    color: var(--q-admin-text-light);
    margin: 0;
}

/* Enhanced Statistics Cards */
.stats-card-modern {
    background: var(--q-admin-surface);
    border: 1px solid var(--q-admin-border);
    border-radius: var(--q-radius-xl);
    box-shadow: var(--q-admin-shadow);
    transition: all var(--q-transition-normal) ease;
    overflow: hidden;
    position: relative;
}

.stats-card-modern:hover {
    box-shadow: var(--q-admin-shadow-hover);
    transform: translateY(-4px);
}

.stats-card-modern.primary {
    background: var(--q-admin-gradient-primary);
    color: white;
}

.stats-card-modern.secondary {
    background: var(--q-admin-gradient-secondary);
    color: white;
}

.stats-card-modern.accent {
    background: var(--q-admin-gradient-accent);
    color: white;
}

.stats-value-large {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
}

.stats-label-modern {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 12px;
    font-weight: 500;
}

.stats-change-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Enhanced Tables */
.admin-table {
    background: var(--q-admin-surface);
    border-radius: var(--q-radius-xl);
    box-shadow: var(--q-admin-shadow);
    overflow: hidden;
}

.admin-table .q-table__top {
    padding: 24px;
    border-bottom: 1px solid var(--q-admin-border);
}

.admin-table .q-table__bottom {
    padding: 16px 24px;
    border-top: 1px solid var(--q-admin-border);
}

/* Enhanced Buttons */
.admin-btn-primary {
    background: var(--q-admin-gradient-primary);
    color: white;
    border: none;
    border-radius: var(--q-radius-lg);
    padding: 12px 24px;
    font-weight: 600;
    transition: all var(--q-transition-normal) ease;
    box-shadow: var(--q-shadow-md);
}

.admin-btn-primary:hover {
    box-shadow: var(--q-shadow-lg);
    transform: translateY(-2px);
}

.admin-btn-secondary {
    background: var(--q-admin-gradient-secondary);
    color: white;
    border: none;
    border-radius: var(--q-radius-lg);
    padding: 12px 24px;
    font-weight: 600;
    transition: all var(--q-transition-normal) ease;
    box-shadow: var(--q-shadow-md);
}

.admin-btn-secondary:hover {
    box-shadow: var(--q-shadow-lg);
    transform: translateY(-2px);
}

/* Enhanced Navigation */
.admin-nav-modern {
    padding: 16px 0;
}

.admin-nav-item-modern {
    margin: 4px 16px;
    border-radius: var(--q-radius-lg);
    transition: all var(--q-transition-normal) ease;
    position: relative;
    overflow: hidden;
}

.admin-nav-item-modern:hover {
    background: var(--q-admin-bg);
    transform: translateX(4px);
}

.admin-nav-item-modern.active {
    background: var(--q-admin-gradient-primary);
    color: white;
    box-shadow: var(--q-shadow-md);
}

.admin-nav-item-modern.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: rgba(255, 255, 255, 0.8);
}

/* Enhanced Badges and Status */
.status-badge {
    padding: 4px 12px;
    border-radius: var(--q-radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: var(--q-admin-success);
    color: var(--q-positive);
}

.status-badge.inactive {
    background: var(--q-admin-error);
    color: var(--q-negative);
}

.status-badge.pending {
    background: var(--q-admin-warning);
    color: var(--q-warning);
}

.status-badge.info {
    background: var(--q-admin-info);
    color: var(--q-info);
}

/* Enhanced Notifications */
.notification-item {
    padding: 16px;
    border-radius: var(--q-radius-lg);
    margin: 8px 0;
    transition: all var(--q-transition-normal) ease;
}

.notification-item:hover {
    background: var(--q-admin-bg);
    transform: translateX(4px);
}

.admin-notification-menu,
.admin-user-menu {
    border-radius: var(--q-radius-xl);
    box-shadow: var(--q-admin-shadow-hover);
    border: 1px solid var(--q-admin-border);
}

.admin-menu-item {
    padding: 12px 16px;
    border-radius: var(--q-radius-lg);
    margin: 4px 8px;
    transition: all var(--q-transition-normal) ease;
}

.admin-menu-item:hover {
    background: var(--q-admin-bg);
    transform: translateX(2px);
}

/* Enhanced Loading States */
.admin-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 32px;
    color: var(--q-admin-text-light);
}

.admin-loading-spinner {
    margin-bottom: 16px;
}

.admin-loading-text {
    font-size: 1rem;
    font-weight: 500;
}

/* Enhanced Empty States */
.admin-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 32px;
    text-align: center;
}

.admin-empty-icon {
    margin-bottom: 24px;
    opacity: 0.5;
}

.admin-empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--q-admin-text);
    margin-bottom: 8px;
}

.admin-empty-description {
    font-size: 0.875rem;
    color: var(--q-admin-text-light);
    margin-bottom: 24px;
    max-width: 400px;
}

/* Enhanced Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

