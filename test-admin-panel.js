#!/usr/bin/env node

/**
 * Comprehensive Admin Panel Test Suite
 * Tests all admin functionality including currency management, flags, and API endpoints
 */

import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const BASE_URL = process.env.APP_URL || 'http://localhost:8000';
const API_BASE = `${BASE_URL}/api`;
const ADMIN_BASE = `${BASE_URL}/admin`;

// Test utilities
const log = (message, data = null) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
    if (data) {
        console.log(JSON.stringify(data, null, 2));
    }
};

const error = (message, err = null) => {
    console.error(`[${new Date().toISOString()}] ❌ ERROR: ${message}`);
    if (err) {
        console.error(err.response?.data || err.message || err);
    }
};

const success = (message) => {
    console.log(`[${new Date().toISOString()}] ✅ ${message}`);
};

const warning = (message) => {
    console.log(`[${new Date().toISOString()}] ⚠️  ${message}`);
};

// Test data
const testCurrency = {
    name: {
        en: 'Test Currency',
        ku: 'دراوی تاقیکردنەوە'
    },
    code: 'TST',
    symbol: '₮',
    country: 'Test Country',
    decimal_places: 2,
    min_amount: 1,
    max_amount: 1000000,
    order: 999,
    is_active: true,
    is_base_currency: false,
    description: {
        en: 'A test currency for admin panel validation',
        ku: 'دراوێکی تاقیکردنەوە بۆ پشتڕاستکردنەوەی پانێڵی بەڕێوەبەر'
    }
};

// Test functions
async function testFileStructure() {
    log('🗂️  Testing admin file structure...');

    const requiredFiles = [
        // Backend files
        'app/Services/CurrencyManagementService.php',
        'app/Http/Resources/CurrencyResource.php',
        'app/Http/Resources/ExchangeRateResource.php',
        'app/Http/Resources/ExchangeRateHistoryResource.php',
        'app/Http/Requests/StoreCurrencyRequest.php',
        'app/Http/Requests/UpdateCurrencyRequest.php',

        // Frontend components
        'resources/js/components/CountryFlag.vue',
        'resources/js/components/Currency/CurrencySelector.vue',
        'resources/js/components/Currency/FlagSelector.vue',
        'resources/js/components/Currency/CurrencyForm.vue',

        // Admin pages
        'resources/js/Pages/Admin/Currencies/Index.vue',
        'resources/js/Pages/Admin/Currencies/Create.vue',
        'resources/js/Pages/Admin/Currencies/Edit.vue',

        // Routes
        'routes/api.php',
        'routes/web.php'
    ];

    let allExist = true;
    let existingFiles = 0;

    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            success(`${file} exists`);
            existingFiles++;
        } else {
            error(`${file} missing`);
            allExist = false;
        }
    }

    log(`📊 File structure: ${existingFiles}/${requiredFiles.length} files exist`);
    return allExist;
}

async function testComponentIntegration() {
    log('🧩 Testing component integration...');

    const componentsToCheck = [
        {
            file: 'resources/js/components/CountryFlag.vue',
            shouldContain: ['vue-country-flag-next', 'kurdistan', 'CountryFlag']
        },
        {
            file: 'resources/js/components/Currency/CurrencyForm.vue',
            shouldContain: ['FlagSelector', 'CurrencyForm', 'submit']
        },
        {
            file: 'resources/js/Pages/Admin/Currencies/Create.vue',
            shouldContain: ['CurrencyForm', 'StoreCurrencyRequest', 'submit']
        }
    ];

    let allValid = true;

    for (const component of componentsToCheck) {
        if (fs.existsSync(component.file)) {
            const content = fs.readFileSync(component.file, 'utf8');
            let hasAllRequired = true;

            for (const required of component.shouldContain) {
                if (!content.includes(required)) {
                    error(`${component.file} missing: ${required}`);
                    hasAllRequired = false;
                    allValid = false;
                }
            }

            if (hasAllRequired) {
                success(`${component.file} has all required elements`);
            }
        } else {
            error(`${component.file} does not exist`);
            allValid = false;
        }
    }

    return allValid;
}

async function testAPIEndpoints() {
    log('🌐 Testing API endpoints...');

    const endpoints = [
        {
            name: 'Public Currencies',
            url: `${API_BASE}/public/currencies`,
            method: 'GET',
            requiresAuth: false
        },
        {
            name: 'Admin Currencies List',
            url: `${API_BASE}/admin/currencies`,
            method: 'GET',
            requiresAuth: true
        },
        {
            name: 'Currency Statistics',
            url: `${API_BASE}/admin/currencies-statistics`,
            method: 'GET',
            requiresAuth: true
        }
    ];

    let passedTests = 0;

    for (const endpoint of endpoints) {
        try {
            const config = {
                method: endpoint.method,
                url: endpoint.url,
                timeout: 5000
            };

            if (endpoint.requiresAuth) {
                // For auth-required endpoints, we expect 401 or 403
                config.validateStatus = (status) => status < 500;
            }

            const response = await axios(config);

            if (endpoint.requiresAuth && (response.status === 401 || response.status === 403)) {
                success(`${endpoint.name}: Properly requires authentication`);
                passedTests++;
            } else if (!endpoint.requiresAuth && response.status === 200) {
                success(`${endpoint.name}: Working correctly`);
                if (response.data.success !== false) {
                    log(`Response sample:`, response.data);
                }
                passedTests++;
            } else if (response.status === 200) {
                success(`${endpoint.name}: Accessible (may need auth setup)`);
                passedTests++;
            } else {
                warning(`${endpoint.name}: Unexpected status ${response.status}`);
            }
        } catch (err) {
            if (err.code === 'ECONNREFUSED') {
                error(`${endpoint.name}: Server not running`);
            } else {
                error(`${endpoint.name}: ${err.message}`);
            }
        }
    }

    log(`📊 API endpoints: ${passedTests}/${endpoints.length} working`);
    return passedTests === endpoints.length;
}

async function testFlagSystem() {
    log('🏳️  Testing flag system...');

    const flagTests = [
        { currency: 'USD', expectedFlag: 'us' },
        { currency: 'EUR', expectedFlag: 'eu' },
        { currency: 'GBP', expectedFlag: 'gb' },
        { currency: 'IQD', expectedFlag: 'iq' },
        { currency: 'KURDISTAN', expectedFlag: 'kurdistan' },
        { currency: 'IRR', expectedFlag: 'ir' },
        { currency: 'TRY', expectedFlag: 'tr' }
    ];

    // Test the mapping function (simulated from our components)
    const mapCurrencyToCountryCode = (currencyCode) => {
        const mapping = {
            'USD': 'us', 'EUR': 'eu', 'GBP': 'gb', 'JPY': 'jp',
            'CAD': 'ca', 'AUD': 'au', 'CHF': 'ch', 'CNY': 'cn',
            'INR': 'in', 'TRY': 'tr', 'IRR': 'ir', 'IQD': 'iq',
            'SAR': 'sa', 'AED': 'ae', 'KWD': 'kw', 'BHD': 'bh',
            'OMR': 'om', 'QAR': 'qa', 'JOD': 'jo', 'LBP': 'lb',
            'EGP': 'eg', 'KURDISTAN': 'kurdistan'
        };
        return mapping[currencyCode.toUpperCase()] || currencyCode.toLowerCase().slice(0, 2);
    };

    let allPassed = true;

    for (const test of flagTests) {
        const result = mapCurrencyToCountryCode(test.currency);
        if (result === test.expectedFlag) {
            success(`${test.currency} → ${result}`);
        } else {
            error(`${test.currency} → ${result} (expected: ${test.expectedFlag})`);
            allPassed = false;
        }
    }

    // Test vue-country-flag-next package
    try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        if (packageJson.dependencies['vue-country-flag-next']) {
            success('vue-country-flag-next package is installed');
        } else {
            error('vue-country-flag-next package not found in dependencies');
            allPassed = false;
        }
    } catch (err) {
        warning('Could not check package.json');
    }

    return allPassed;
}

async function testAdminRoutes() {
    log('🛣️  Testing admin routes...');

    const routes = [
        `${ADMIN_BASE}/currencies`,
        `${ADMIN_BASE}/currencies/create`,
        `${ADMIN_BASE}/currency-exchange-rates`,
        `${ADMIN_BASE}/exchange-rate-history`
    ];

    let accessibleRoutes = 0;

    for (const route of routes) {
        try {
            const response = await axios.get(route, {
                timeout: 5000,
                validateStatus: (status) => status < 500
            });

            if (response.status === 200) {
                success(`${route}: Accessible`);
                accessibleRoutes++;
            } else if (response.status === 401 || response.status === 403) {
                success(`${route}: Properly protected (requires auth)`);
                accessibleRoutes++;
            } else if (response.status === 302) {
                success(`${route}: Redirects (likely to login)`);
                accessibleRoutes++;
            } else {
                warning(`${route}: Status ${response.status}`);
            }
        } catch (err) {
            if (err.code === 'ECONNREFUSED') {
                error(`${route}: Server not running`);
            } else {
                error(`${route}: ${err.message}`);
            }
        }
    }

    log(`📊 Admin routes: ${accessibleRoutes}/${routes.length} accessible`);
    return accessibleRoutes > 0;
}

async function testDatabaseConnection() {
    log('🗄️  Testing database connection...');

    try {
        // Try to access a simple API endpoint that would require DB
        const response = await axios.get(`${API_BASE}/public/currencies`, {
            timeout: 5000,
            validateStatus: (status) => status < 500
        });

        if (response.status === 200 && response.data) {
            success('Database connection working');
            return true;
        } else {
            warning('Database might not be properly configured');
            return false;
        }
    } catch (err) {
        error('Database connection test failed', err);
        return false;
    }
}

// Main test runner
async function runAdminTests() {
    console.log('🚀 Starting Admin Panel Test Suite');
    console.log('='.repeat(60));

    const testResults = {
        fileStructure: await testFileStructure(),
        componentIntegration: await testComponentIntegration(),
        apiEndpoints: await testAPIEndpoints(),
        flagSystem: await testFlagSystem(),
        adminRoutes: await testAdminRoutes(),
        databaseConnection: await testDatabaseConnection()
    };

    console.log('='.repeat(60));
    console.log('📊 ADMIN PANEL TEST RESULTS:');
    console.log('='.repeat(60));

    let passedTests = 0;
    let totalTests = 0;

    for (const [testName, passed] of Object.entries(testResults)) {
        totalTests++;
        const status = passed ? '✅ PASSED' : '❌ FAILED';
        const displayName = testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        console.log(`${status} - ${displayName}`);
        if (passed) passedTests++;
    }

    console.log('='.repeat(60));
    console.log(`🎯 OVERALL RESULT: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
        console.log('🎉 ALL TESTS PASSED! Admin panel is ready for use.');
    } else if (passedTests >= totalTests * 0.8) {
        console.log('⚠️  Most tests passed. Minor issues may need attention.');
    } else {
        console.log('❌ Several tests failed. Please review the issues above.');
    }

    // Provide next steps
    console.log('\n📋 NEXT STEPS:');
    if (!testResults.databaseConnection) {
        console.log('1. ⚠️  Set up database connection and run migrations');
    }
    if (!testResults.adminRoutes) {
        console.log('2. ⚠️  Start the Laravel development server');
    }
    if (!testResults.apiEndpoints) {
        console.log('3. ⚠️  Check API routes and authentication setup');
    }

    console.log('4. ✅ Access admin panel at: ' + ADMIN_BASE);
    console.log('5. ✅ Test currency creation with flag selection');
    console.log('6. ✅ Verify flag display on public home page');

    return passedTests >= totalTests * 0.8;
}

// Export for use in other scripts
export { runAdminTests };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runAdminTests().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(err => {
        console.error('Test runner failed:', err);
        process.exit(1);
    });
}
