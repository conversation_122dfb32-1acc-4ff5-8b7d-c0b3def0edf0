# 🧪 Admin Panel Testing Guide

## 🚀 Quick Start

### 1. **Run Automated Tests**
```bash
# Run the comprehensive admin test suite
node test-admin-panel.js

# Or run the general currency system tests
node test-currency-system.js
```

### 2. **Start Your Development Environment**
```bash
# Start Laravel development server
php artisan serve

# In another terminal, start Vite (if using)
npm run dev
# or
yarn dev
```

### 3. **Access Admin Panel**
Navigate to: `http://localhost:8000/admin`

---

## 📋 Manual Testing Checklist

### **A. Authentication & Access**
- [ ] Admin login page loads correctly
- [ ] Authentication redirects work properly
- [ ] Admin dashboard is accessible after login
- [ ] Unauthorized access is properly blocked

### **B. Currency Management - Index Page**
Navigate to: `/admin/currencies`

**Test the enhanced Index page:**
- [ ] Currency list loads with proper flag display
- [ ] Flags are displayed using vue-country-flag-next (not emojis)
- [ ] Kurdistan flag shows custom image (if applicable)
- [ ] Search functionality works
- [ ] Filtering by status works (Active/Inactive)
- [ ] Sorting works correctly
- [ ] Grid/Table view toggle works
- [ ] Pagination works if you have many currencies

**Flag Display Verification:**
- [ ] USD shows US flag 🇺🇸
- [ ] EUR shows EU flag 🇪🇺
- [ ] GBP shows UK flag 🇬🇧
- [ ] IQD shows Iraq flag 🇮🇶
- [ ] Kurdistan shows custom Kurdistan flag
- [ ] All flags are consistent in size and style

### **C. Currency Creation**
Navigate to: `/admin/currencies/create`

**Test the new CurrencyForm component:**
- [ ] Form loads with all fields
- [ ] Flag selector shows comprehensive list
- [ ] Auto-flag detection works (enter "USD" → US flag auto-selected)
- [ ] Kurdistan flag option is available
- [ ] Form validation works:
  - [ ] Required fields show errors
  - [ ] Currency code must be 3 letters
  - [ ] Max amount > Min amount validation
  - [ ] Only one base currency allowed
- [ ] Flag preview updates in real-time
- [ ] Multilingual support (English/Kurdish names)
- [ ] Form submission works correctly
- [ ] Success notification appears
- [ ] Redirects to currency list after creation

**Test Data to Try:**
```
Name (EN): Test Currency
Name (KU): دراوی تاقیکردنەوە
Code: TST
Symbol: ₮
Country: Test Country
Flag: Select from dropdown (should auto-suggest based on code)
Decimal Places: 2
Min Amount: 1
Max Amount: 1000000
```

### **D. Currency Editing**
Navigate to: `/admin/currencies/{id}/edit`

**Test the enhanced Edit page:**
- [ ] Form pre-populates with existing data
- [ ] Flag selector shows current flag
- [ ] Can change flag selection
- [ ] Validation prevents invalid changes
- [ ] Cannot deactivate base currency
- [ ] Cannot set multiple base currencies
- [ ] Update works correctly
- [ ] Changes reflect immediately in list

### **E. Flag System Integration**

**Test Flag Consistency:**
- [ ] Same currency shows same flag across all pages
- [ ] Flags load quickly without flickering
- [ ] Error handling for missing flags works
- [ ] Kurdistan flag displays correctly everywhere
- [ ] Flag sizes are consistent (small/medium/large)

**Test Flag Sources:**
- [ ] All flags come from vue-country-flag-next package
- [ ] No hardcoded flag emojis or images (except Kurdistan)
- [ ] Flag mapping works for all major currencies

### **F. API Endpoints**

**Test New API Endpoints:**
```bash
# Test public currencies endpoint
curl http://localhost:8000/api/public/currencies

# Test admin statistics (requires auth)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8000/api/admin/currencies-statistics

# Test currency CRUD operations
curl -X POST http://localhost:8000/api/admin/currencies \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"name":{"en":"Test"},"code":"TST",...}'
```

### **G. Public Home Page Integration**
Navigate to: `/` (home page)

**Verify Flag Integration:**
- [ ] Currency cards show proper flags
- [ ] Language selector shows country flags
- [ ] All flags use vue-country-flag-next
- [ ] Kurdistan flag displays correctly
- [ ] Flags are responsive on mobile
- [ ] Flag loading is smooth

### **H. Error Handling**

**Test Error Scenarios:**
- [ ] Invalid currency code (not 3 letters)
- [ ] Duplicate currency codes
- [ ] Setting multiple base currencies
- [ ] Deactivating base currency
- [ ] Network errors during flag loading
- [ ] Missing flag images
- [ ] Form submission with invalid data

### **I. Performance & UX**

**Test Performance:**
- [ ] Pages load quickly
- [ ] Flag images load efficiently
- [ ] No console errors
- [ ] Smooth animations and transitions
- [ ] Mobile responsiveness
- [ ] Keyboard navigation works

---

## 🐛 Common Issues & Solutions

### **Issue: Flags not displaying**
**Solution:** 
- Check if vue-country-flag-next is installed: `npm list vue-country-flag-next`
- Verify component imports are correct
- Check browser console for errors

### **Issue: Kurdistan flag not showing**
**Solution:**
- Ensure Kurdistan flag image exists in `/public/flags/kurdistan.png`
- Check CountryFlag component handles special case

### **Issue: API endpoints returning 404**
**Solution:**
- Run `php artisan route:list` to verify routes
- Check if routes/api.php has new endpoints
- Clear route cache: `php artisan route:clear`

### **Issue: Form validation not working**
**Solution:**
- Check if new Request classes are being used
- Verify validation rules in StoreCurrencyRequest/UpdateCurrencyRequest
- Check for JavaScript console errors

### **Issue: Admin pages showing old design**
**Solution:**
- Clear browser cache
- Run `npm run build` or `yarn build`
- Check if new components are properly imported

---

## ✅ Success Criteria

Your admin panel is working correctly if:

1. **✅ All flags display using vue-country-flag-next**
2. **✅ Currency creation/editing uses new form components**
3. **✅ Flag selection works with comprehensive options**
4. **✅ Kurdistan flag displays correctly**
5. **✅ API endpoints respond properly**
6. **✅ Public home page shows proper flags**
7. **✅ No console errors**
8. **✅ Mobile responsive design works**

---

## 📞 Need Help?

If you encounter issues:

1. **Check the automated test results** first
2. **Look at browser console** for JavaScript errors
3. **Check Laravel logs** in `storage/logs/laravel.log`
4. **Verify database** has proper currency data
5. **Ensure all dependencies** are installed

The admin panel should now provide a much better experience with proper flag integration and enhanced functionality!
