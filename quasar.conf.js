/* eslint-env node */

const { configure } = require('@quasar/extras/roboto-font')
const { extendConfig } = require('@quasar/vite-plugin')

// Import Roboto font
configure()

module.exports = extendConfig({
  // Quasar configuration
  quasar: {
    extras: [
      'roboto-font',
      'material-icons',
      'material-icons-outlined',
      'material-icons-round',
      'material-icons-sharp',
    ],
    framework: {
      config: {
        brand: {
            primary: '#154897',     // Deep blue (from your numbers) - main brand color
            secondary: '#2B6CB0',   // Lighter, friendly blue (Quasar-compatible)
            accent: '#D69E2E',      // Golden-amber (wealth accent, Quasar-friendly contrast)
            dark: '#1A202C',        // Quasar's recommended dark shade (better than pure black)
            positive: '#38A169',    // Quasar's success green (accessible)
            negative: '#E53E3E',    // Quasar's error red (vibrant but not harsh)
            info: '#3182CE',        // Quasar's info blue
            warning: '#DD6B20',     // Quasar's warning orange
            light: '#F7FAFC',       // Backgrounds/cards
            background: '#FFFFFF'
        },
        notify: {},
        loading: {},
        loadingBar: {
          color: 'primary',
          size: '4px',
          position: 'top'
        }
      },
      plugins: [
        'Notify',
        'Loading',
        'LoadingBar',
        'Dialog',
        'LocalStorage'
      ]
    },
    animations: [],
    ssr: {
      pwa: false,
      prodPort: 3000,
      middlewares: [
        'render' // keep this as last one
      ]
    },
    pwa: {
      workboxMode: 'generateSW',
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json',
      useCredentials: false
    },
    cordova: {
      // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
    },
    capacitor: {
      hideSplashscreen: true
    },
    electron: {
      inspectPort: 5858,
      bundler: 'packager',
      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options
        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',
        // Windows only
        // win32metadata: { ... }
      },
      builder: {
        // https://www.electron.build/configuration/configuration
        appId: 'rwnaki-shein'
      }
    },
    bex: {
      contentScripts: [
        'src-bex/content-script.js'
      ]
    }
  },

  // Vite configuration
  vite: {
    define: {
      'process.env.DEBUG': false,
    },
    server: {
      port: 8080,
      open: true
    },
    build: {
      target: 'esnext',
      minify: 'terser',
      sourcemap: false,
      chunkSizeWarningLimit: 1000
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @import "quasar/dist/quasar.css";
            @import "tailwindcss/base";
            @import "tailwindcss/components";
            @import "tailwindcss/utilities";
          `
        }
      }
    },
    plugins: [
      require('tailwindcss'),
      require('autoprefixer'),
      require('postcss-import'),
      require('postcss-nesting')
    ]
  }
})
