<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Console\Scheduling\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule automatic exchange rate updates
app(Schedule::class)->command('rates:update --provider=exchangerate')
    ->hourly()
    ->withoutOverlapping()
    ->runInBackground();
