<?php

use App\Http\Controllers\Api\TranslationController;
use App\Http\Controllers\Api\ExchangeRateController;
use App\Http\Controllers\Api\Admin\AdminDashboardController;
use App\Http\Controllers\Api\Admin\AdminCurrencyController;
use App\Http\Controllers\Api\Admin\AdminExchangeRateController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CurrencyExchange7Controller;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_middleware')
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});

// Admin API Routes
Route::middleware(['auth:web'])->prefix('admin')->group(function () {
    // Dashboard
    Route::get('/dashboard/stats', [AdminDashboardController::class, 'getStats']);
    Route::get('/dashboard/chart-data', [AdminDashboardController::class, 'getChartData']);
    Route::get('/dashboard/recent-activity', [AdminDashboardController::class, 'getRecentActivity']);
    Route::post('/trigger-rate-update', [AdminDashboardController::class, 'triggerRateUpdate']);

    // Currencies
    Route::apiResource('currencies', AdminCurrencyController::class);
    Route::patch('currencies/{currency}/toggle-status', [AdminCurrencyController::class, 'toggleStatus']);
    Route::get('currencies-statistics', [AdminCurrencyController::class, 'statistics']);
    Route::post('currencies-update-orders', [AdminCurrencyController::class, 'updateOrders']);

    // Exchange Rates
    Route::apiResource('exchange-rates', AdminExchangeRateController::class);
    Route::patch('exchange-rates/{rate}/toggle-status', [AdminExchangeRateController::class, 'toggleStatus']);
    Route::patch('exchange-rates/bulk-update', [AdminExchangeRateController::class, 'bulkUpdate']);
    Route::get('exchange-rates/{rate}/history', [AdminExchangeRateController::class, 'getHistory']);
    Route::get('exchange-rates/check-existing', [AdminExchangeRateController::class, 'checkExisting']);

    // User Management API Routes
    Route::middleware(['permission:view users'])->group(function () {
        Route::get('users', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'index']);
        Route::get('users/stats', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'getStats']);
        Route::get('users/{user}', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'show']);
    });

    Route::middleware(['permission:create users'])->group(function () {
        Route::post('users', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'store']);
    });

    Route::middleware(['permission:edit users'])->group(function () {
        Route::put('users/{user}', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'update']);
        Route::patch('users/{user}/toggle-status', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'toggleStatus']);
        Route::patch('users/{user}/update-roles', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'updateRoles']);
    });

    Route::middleware(['permission:delete users'])->group(function () {
        Route::delete('users/{user}', [App\Http\Controllers\Api\Admin\AdminUserController::class, 'destroy']);
    });

    // Role Management API Routes
    Route::middleware(['permission:view roles'])->group(function () {
        Route::get('roles', [App\Http\Controllers\Api\Admin\AdminRoleController::class, 'index']);
        Route::get('roles/{role}', [App\Http\Controllers\Api\Admin\AdminRoleController::class, 'show']);
    });

    Route::middleware(['permission:create roles'])->group(function () {
        Route::post('roles', [App\Http\Controllers\Api\Admin\AdminRoleController::class, 'store']);
    });

    Route::middleware(['permission:edit roles'])->group(function () {
        Route::put('roles/{role}', [App\Http\Controllers\Api\Admin\AdminRoleController::class, 'update']);
        Route::patch('roles/{role}/update-permissions', [App\Http\Controllers\Api\Admin\AdminRoleController::class, 'updatePermissions']);
    });

    Route::middleware(['permission:delete roles'])->group(function () {
        Route::delete('roles/{role}', [App\Http\Controllers\Api\Admin\AdminRoleController::class, 'destroy']);
    });

    // Permission Management API Routes
    Route::middleware(['permission:view permissions'])->group(function () {
        Route::get('permissions', [App\Http\Controllers\Api\Admin\AdminPermissionController::class, 'index']);
        Route::get('permissions/{permission}', [App\Http\Controllers\Api\Admin\AdminPermissionController::class, 'show']);
    });

    Route::middleware(['permission:create permissions'])->group(function () {
        Route::post('permissions', [App\Http\Controllers\Api\Admin\AdminPermissionController::class, 'store']);
    });

    Route::middleware(['permission:edit permissions'])->group(function () {
        Route::put('permissions/{permission}', [App\Http\Controllers\Api\Admin\AdminPermissionController::class, 'update']);
    });

    Route::middleware(['permission:delete permissions'])->group(function () {
        Route::delete('permissions/{permission}', [App\Http\Controllers\Api\Admin\AdminPermissionController::class, 'destroy']);
    });
});

// Public API Routes
Route::prefix('public')->group(function () {
    Route::get('currencies', [AdminCurrencyController::class, 'publicCurrencies']);
});

// Public Exchange Rate API
Route::prefix('exchange')->group(function () {
    Route::get('/currencies', [ExchangeRateController::class, 'getCurrencies']);
    Route::get('/rates', [ExchangeRateController::class, 'getExchangeRates']);
    Route::get('/convert', [ExchangeRateController::class, 'convertCurrency']);
    Route::get('/rate', [ExchangeRateController::class, 'getRate']);
    Route::get('/convert', [ExchangeRateController::class, 'convert']);
    Route::get('/history', [ExchangeRateController::class, 'history']);
    Route::get('/latest', [ExchangeRateController::class, 'latest']);
    Route::get('/statistics', [ExchangeRateController::class, 'statistics']);
    Route::get('/update-status', [ExchangeRateController::class, 'updateStatus']);

    // Enhanced multi-source rate fetching
    Route::get('/fetch-multi-source', [ExchangeRateController::class, 'fetchFromMultipleSources']);
    Route::post('/update-multi-source', [ExchangeRateController::class, 'updateFromMultipleSources']);
    Route::get('/providers', [ExchangeRateController::class, 'getProviders']);
    Route::get('/test-providers', [ExchangeRateController::class, 'testProviders']);
});

// Legacy routes (for backward compatibility)
Route::post('/currency/convert', [CurrencyExchange7Controller::class, 'convert']);
Route::get('/currency/rates', [CurrencyExchange7Controller::class, 'getRates']);
Route::post('/currency/store', [CurrencyExchange7Controller::class, 'store']);

// Translation API
Route::get('/translations/{locale}', [TranslationController::class, 'getTranslations'])->middleware('api');
