<?php

use Illuminate\Support\Facades\Route;

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LanguageController;

Route::get('language/{locale}', [LanguageController::class, 'switchLang'])->name('language.switch');

Route::middleware('auth')->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/rates', [DashboardController::class, 'getRates'])->name('dashboard.rates');

    // Currency management
    Route::resource('currencies', App\Http\Controllers\CurrencyController::class)->except(['show']);
    Route::patch('currencies/{currency}/toggle-status', [App\Http\Controllers\CurrencyController::class, 'toggleStatus'])->name('currencies.toggle-status');

    // Exchange rates management
    Route::resource('currency-exchange-rates', App\Http\Controllers\CurrencyExchangeRateController::class)->except(['show']);
    Route::patch('currency-exchange-rates/{currencyExchangeRate}/toggle-status', [App\Http\Controllers\CurrencyExchangeRateController::class, 'toggleStatus'])->name('currency-exchange-rates.toggle-status');
    Route::patch('currency-exchange-rates/bulk-update', [App\Http\Controllers\CurrencyExchangeRateController::class, 'bulkUpdate'])->name('currency-exchange-rates.bulk-update');
    Route::get('currency-exchange-rates/get-rate', [App\Http\Controllers\CurrencyExchangeRateController::class, 'getRate'])->name('currency-exchange-rates.get-rate');

    // Exchange rate history
    Route::resource('exchange-rate-history', App\Http\Controllers\ExchangeRateHistoryController::class);
    Route::get('exchange-rate-history/chart-data', [App\Http\Controllers\ExchangeRateHistoryController::class, 'chartData'])->name('exchange-rate-history.chart-data');
    Route::get('exchange-rate-history/analytics', [App\Http\Controllers\ExchangeRateHistoryController::class, 'analytics'])->name('exchange-rate-history.analytics');

    // User management
    Route::middleware(['auth', 'permission:view users'])->group(function () {
        Route::get('users', [App\Http\Controllers\UserController::class, 'index'])->name('users.index');
        Route::get('users/{user}', [App\Http\Controllers\UserController::class, 'show'])->name('users.show');
    });

    Route::middleware(['auth', 'permission:create users'])->group(function () {
        Route::get('users/create', [App\Http\Controllers\UserController::class, 'create'])->name('users.create');
        Route::post('users', [App\Http\Controllers\UserController::class, 'store'])->name('users.store');
    });

    Route::middleware(['auth', 'permission:edit users'])->group(function () {
        Route::get('users/{user}/edit', [App\Http\Controllers\UserController::class, 'edit'])->name('users.edit');
        Route::put('users/{user}', [App\Http\Controllers\UserController::class, 'update'])->name('users.update');
        Route::patch('users/{user}', [App\Http\Controllers\UserController::class, 'update'])->name('users.patch');
        Route::patch('users/{user}/toggle-status', [App\Http\Controllers\UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::get('users/{user}/assign-roles', [App\Http\Controllers\UserController::class, 'assignRoles'])->name('users.assign-roles');
        Route::patch('users/{user}/update-roles', [App\Http\Controllers\UserController::class, 'updateRoles'])->name('users.update-roles');
    });

    Route::middleware(['auth', 'permission:delete users'])->group(function () {
        Route::delete('users/{user}', [App\Http\Controllers\UserController::class, 'destroy'])->name('users.destroy');
    });

    // Role management
    Route::middleware(['auth', 'permission:view roles'])->group(function () {
        Route::get('roles', [App\Http\Controllers\RoleController::class, 'index'])->name('roles.index');
        Route::get('roles/{role}', [App\Http\Controllers\RoleController::class, 'show'])->name('roles.show');
    });

    Route::middleware(['auth', 'permission:create roles'])->group(function () {
        Route::get('roles/create', [App\Http\Controllers\RoleController::class, 'create'])->name('roles.create');
        Route::post('roles', [App\Http\Controllers\RoleController::class, 'store'])->name('roles.store');
    });

    Route::middleware(['auth', 'permission:edit roles'])->group(function () {
        Route::get('roles/{role}/edit', [App\Http\Controllers\RoleController::class, 'edit'])->name('roles.edit');
        Route::put('roles/{role}', [App\Http\Controllers\RoleController::class, 'update'])->name('roles.update');
        Route::patch('roles/{role}', [App\Http\Controllers\RoleController::class, 'update'])->name('roles.patch');
        Route::get('roles/{role}/assign-permissions', [App\Http\Controllers\RoleController::class, 'assignPermissions'])->name('roles.assign-permissions');
        Route::patch('roles/{role}/update-permissions', [App\Http\Controllers\RoleController::class, 'updatePermissions'])->name('roles.update-permissions');
    });

    Route::middleware(['auth', 'permission:delete roles'])->group(function () {
        Route::delete('roles/{role}', [App\Http\Controllers\RoleController::class, 'destroy'])->name('roles.destroy');
    });

    // Permission management
    Route::middleware(['auth', 'permission:view permissions'])->group(function () {
        Route::get('permissions', [App\Http\Controllers\PermissionController::class, 'index'])->name('permissions.index');
        Route::get('permissions/{permission}', [App\Http\Controllers\PermissionController::class, 'show'])->name('permissions.show');
    });

    Route::middleware(['auth', 'permission:create permissions'])->group(function () {
        Route::get('permissions/create', [App\Http\Controllers\PermissionController::class, 'create'])->name('permissions.create');
        Route::post('permissions', [App\Http\Controllers\PermissionController::class, 'store'])->name('permissions.store');
    });

    Route::middleware(['auth', 'permission:edit permissions'])->group(function () {
        Route::get('permissions/{permission}/edit', [App\Http\Controllers\PermissionController::class, 'edit'])->name('permissions.edit');
        Route::put('permissions/{permission}', [App\Http\Controllers\PermissionController::class, 'update'])->name('permissions.update');
        Route::patch('permissions/{permission}', [App\Http\Controllers\PermissionController::class, 'update'])->name('permissions.patch');
    });

    Route::middleware(['auth', 'permission:delete permissions'])->group(function () {
        Route::delete('permissions/{permission}', [App\Http\Controllers\PermissionController::class, 'destroy'])->name('permissions.destroy');
    });

    // Demo page for enhanced components
    Route::get('/demo/enhanced-components', function () {
        return inertia('Admin/Demo/EnhancedComponents');
    })->name('demo.enhanced-components');
});

// Public routes
Route::get('/', [App\Http\Controllers\Web\HomeController::class, 'index'])->name('home');
Route::get('/exchange', function () {
    return inertia('Web/Exchange/Index');
})->name('exchange');

// Remove this test route in production

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
