<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('currencies', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('order');
            $table->boolean('is_base_currency')->default(false)->after('is_active');
            $table->integer('decimal_places')->default(2)->after('is_base_currency');
            $table->string('flag_icon')->nullable()->after('decimal_places');
            $table->text('description')->nullable()->after('flag_icon');
            $table->decimal('min_amount', 15, 8)->default(0.01)->after('description');
            $table->decimal('max_amount', 15, 8)->default(999999999.99)->after('min_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('currencies', function (Blueprint $table) {
            $table->dropColumn([
                'is_active',
                'is_base_currency',
                'decimal_places',
                'flag_icon',
                'description',
                'min_amount',
                'max_amount'
            ]);
        });
    }
};
