<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exchange_rate_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('from_currency_id')->constrained('currencies')->onDelete('cascade');
            $table->foreignId('to_currency_id')->constrained('currencies')->onDelete('cascade');
            $table->decimal('rate', 15, 8);
            $table->decimal('previous_rate', 15, 8)->nullable();
            $table->decimal('change_percentage', 8, 4)->nullable();
            $table->enum('change_type', ['increase', 'decrease', 'no_change'])->nullable();
            $table->string('source')->default('manual'); // manual, api, automatic
            $table->text('notes')->nullable();
            $table->timestamp('effective_date');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['from_currency_id', 'to_currency_id', 'effective_date']);
            $table->index('effective_date');
            $table->index(['from_currency_id', 'to_currency_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exchange_rate_history');
    }
};
