<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Currency;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'name' => 'US Dollar',
                'code' => 'USD',
                'symbol' => '$',
                'country' => 'United States',
                'order' => 1,
                'is_active' => true,
                'is_base_currency' => true,
                'decimal_places' => 2,
                'flag_icon' => 'us',
                'description' => 'United States Dollar',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Iraqi Dinar',
                'code' => 'IQD',
                'symbol' => 'ع.د',
                'country' => 'Iraq',
                'order' => 2,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 3,
                'flag_icon' => 'iq',
                'description' => 'Iraqi Dinar',
                'min_amount' => 1,
                'max_amount' => *********999.999,
            ],
            [
                'name' => 'Euro',
                'code' => 'EUR',
                'symbol' => '€',
                'country' => 'European Union',
                'order' => 3,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'eu',
                'description' => 'Euro',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'British Pound',
                'code' => 'GBP',
                'symbol' => '£',
                'country' => 'United Kingdom',
                'order' => 4,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'gb',
                'description' => 'British Pound Sterling',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Japanese Yen',
                'code' => 'JPY',
                'symbol' => '¥',
                'country' => 'Japan',
                'order' => 5,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 0,
                'flag_icon' => 'jp',
                'description' => 'Japanese Yen',
                'min_amount' => 1,
                'max_amount' => *********,
            ],
            [
                'name' => 'Canadian Dollar',
                'code' => 'CAD',
                'symbol' => 'C$',
                'country' => 'Canada',
                'order' => 6,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'ca',
                'description' => 'Canadian Dollar',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Australian Dollar',
                'code' => 'AUD',
                'symbol' => 'A$',
                'country' => 'Australia',
                'order' => 7,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'au',
                'description' => 'Australian Dollar',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Swiss Franc',
                'code' => 'CHF',
                'symbol' => 'Fr',
                'country' => 'Switzerland',
                'order' => 8,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'ch',
                'description' => 'Swiss Franc',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Chinese Yuan',
                'code' => 'CNY',
                'symbol' => '¥',
                'country' => 'China',
                'order' => 9,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'cn',
                'description' => 'Chinese Yuan Renminbi',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Turkish Lira',
                'code' => 'TRY',
                'symbol' => '₺',
                'country' => 'Turkey',
                'order' => 10,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'tr',
                'description' => 'Turkish Lira',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'Saudi Riyal',
                'code' => 'SAR',
                'symbol' => 'ر.س',
                'country' => 'Saudi Arabia',
                'order' => 11,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'sa',
                'description' => 'Saudi Riyal',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
            [
                'name' => 'UAE Dirham',
                'code' => 'AED',
                'symbol' => 'د.إ',
                'country' => 'United Arab Emirates',
                'order' => 12,
                'is_active' => true,
                'is_base_currency' => false,
                'decimal_places' => 2,
                'flag_icon' => 'ae',
                'description' => 'UAE Dirham',
                'min_amount' => 0.01,
                'max_amount' => *********.99,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::updateOrCreate(
                ['code' => $currency['code']],
                $currency
            );
        }
    }
}
