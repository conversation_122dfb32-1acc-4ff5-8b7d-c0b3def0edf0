<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'view users',
            'create users',
            'edit users',
            'delete users',
            'assign roles',
            
            // Role Management
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
            'assign permissions',
            
            // Permission Management
            'view permissions',
            'create permissions',
            'edit permissions',
            'delete permissions',
            
            // Currency Management
            'view currencies',
            'create currencies',
            'edit currencies',
            'delete currencies',
            'toggle currency status',
            
            // Exchange Rate Management
            'view exchange rates',
            'create exchange rates',
            'edit exchange rates',
            'delete exchange rates',
            'bulk update rates',
            'toggle rate status',
            
            // Exchange Rate History
            'view rate history',
            'create rate history',
            'edit rate history',
            'delete rate history',
            
            // Dashboard & Analytics
            'view dashboard',
            'view analytics',
            'view statistics',
            'trigger rate updates',
            
            // System Settings
            'view settings',
            'edit settings',
            'manage system',
            
            // Public Access
            'view public rates',
            'use exchange calculator',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        
        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'Super Admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - has most permissions except user management and system settings
        $admin = Role::create(['name' => 'Admin']);
        $admin->givePermissionTo([
            'view users',
            'view roles',
            'view permissions',
            'view currencies',
            'create currencies',
            'edit currencies',
            'delete currencies',
            'toggle currency status',
            'view exchange rates',
            'create exchange rates',
            'edit exchange rates',
            'delete exchange rates',
            'bulk update rates',
            'toggle rate status',
            'view rate history',
            'create rate history',
            'edit rate history',
            'delete rate history',
            'view dashboard',
            'view analytics',
            'view statistics',
            'trigger rate updates',
            'view settings',
            'view public rates',
            'use exchange calculator',
        ]);

        // Manager - can manage currencies and rates but not delete
        $manager = Role::create(['name' => 'Manager']);
        $manager->givePermissionTo([
            'view currencies',
            'create currencies',
            'edit currencies',
            'toggle currency status',
            'view exchange rates',
            'create exchange rates',
            'edit exchange rates',
            'bulk update rates',
            'toggle rate status',
            'view rate history',
            'create rate history',
            'edit rate history',
            'view dashboard',
            'view analytics',
            'view statistics',
            'trigger rate updates',
            'view public rates',
            'use exchange calculator',
        ]);

        // Editor - can edit currencies and rates but not create/delete
        $editor = Role::create(['name' => 'Editor']);
        $editor->givePermissionTo([
            'view currencies',
            'edit currencies',
            'view exchange rates',
            'edit exchange rates',
            'view rate history',
            'edit rate history',
            'view dashboard',
            'view statistics',
            'view public rates',
            'use exchange calculator',
        ]);

        // Viewer - read-only access
        $viewer = Role::create(['name' => 'Viewer']);
        $viewer->givePermissionTo([
            'view currencies',
            'view exchange rates',
            'view rate history',
            'view dashboard',
            'view statistics',
            'view public rates',
            'use exchange calculator',
        ]);

        // User - basic public access
        $user = Role::create(['name' => 'User']);
        $user->givePermissionTo([
            'view public rates',
            'use exchange calculator',
        ]);

        // Assign Super Admin role to the first user (if exists)
        $firstUser = User::first();
        if ($firstUser) {
            $firstUser->assignRole('Super Admin');
        }
    }
}
