<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Currency;
use App\Models\CurrencyExchangeRate;

class ExchangeRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get currencies
        $usd = Currency::where('code', 'USD')->first();
        $iqd = Currency::where('code', 'IQD')->first();
        $eur = Currency::where('code', 'EUR')->first();
        $gbp = Currency::where('code', 'GBP')->first();
        $jpy = Currency::where('code', 'JPY')->first();
        $cad = Currency::where('code', 'CAD')->first();
        $aud = Currency::where('code', 'AUD')->first();
        $chf = Currency::where('code', 'CHF')->first();
        $cny = Currency::where('code', 'CNY')->first();
        $try = Currency::where('code', 'TRY')->first();
        $sar = Currency::where('code', 'SAR')->first();
        $aed = Currency::where('code', 'AED')->first();

        $exchangeRates = [
            // USD to other currencies
            ['from' => $usd->id, 'to' => $iqd->id, 'rate' => 1310.00],
            ['from' => $usd->id, 'to' => $eur->id, 'rate' => 0.85],
            ['from' => $usd->id, 'to' => $gbp->id, 'rate' => 0.73],
            ['from' => $usd->id, 'to' => $jpy->id, 'rate' => 110.50],
            ['from' => $usd->id, 'to' => $cad->id, 'rate' => 1.25],
            ['from' => $usd->id, 'to' => $aud->id, 'rate' => 1.35],
            ['from' => $usd->id, 'to' => $chf->id, 'rate' => 0.92],
            ['from' => $usd->id, 'to' => $cny->id, 'rate' => 6.45],
            ['from' => $usd->id, 'to' => $try->id, 'rate' => 27.50],
            ['from' => $usd->id, 'to' => $sar->id, 'rate' => 3.75],
            ['from' => $usd->id, 'to' => $aed->id, 'rate' => 3.67],

            // EUR to other currencies
            ['from' => $eur->id, 'to' => $usd->id, 'rate' => 1.18],
            ['from' => $eur->id, 'to' => $iqd->id, 'rate' => 1545.80],
            ['from' => $eur->id, 'to' => $gbp->id, 'rate' => 0.86],
            ['from' => $eur->id, 'to' => $jpy->id, 'rate' => 130.39],

            // GBP to other currencies
            ['from' => $gbp->id, 'to' => $usd->id, 'rate' => 1.37],
            ['from' => $gbp->id, 'to' => $iqd->id, 'rate' => 1794.70],
            ['from' => $gbp->id, 'to' => $eur->id, 'rate' => 1.16],

            // IQD to other currencies
            ['from' => $iqd->id, 'to' => $usd->id, 'rate' => 0.000763],
            ['from' => $iqd->id, 'to' => $eur->id, 'rate' => 0.000647],
            ['from' => $iqd->id, 'to' => $gbp->id, 'rate' => 0.000557],

            // JPY to major currencies
            ['from' => $jpy->id, 'to' => $usd->id, 'rate' => 0.009050],
            ['from' => $jpy->id, 'to' => $eur->id, 'rate' => 0.007669],

            // CAD to major currencies
            ['from' => $cad->id, 'to' => $usd->id, 'rate' => 0.80],
            ['from' => $cad->id, 'to' => $eur->id, 'rate' => 0.68],

            // AUD to major currencies
            ['from' => $aud->id, 'to' => $usd->id, 'rate' => 0.74],
            ['from' => $aud->id, 'to' => $eur->id, 'rate' => 0.63],

            // CHF to major currencies
            ['from' => $chf->id, 'to' => $usd->id, 'rate' => 1.09],
            ['from' => $chf->id, 'to' => $eur->id, 'rate' => 0.92],

            // CNY to major currencies
            ['from' => $cny->id, 'to' => $usd->id, 'rate' => 0.155],
            ['from' => $cny->id, 'to' => $eur->id, 'rate' => 0.131],

            // TRY to major currencies
            ['from' => $try->id, 'to' => $usd->id, 'rate' => 0.036],
            ['from' => $try->id, 'to' => $eur->id, 'rate' => 0.031],

            // SAR to major currencies
            ['from' => $sar->id, 'to' => $usd->id, 'rate' => 0.267],
            ['from' => $sar->id, 'to' => $eur->id, 'rate' => 0.226],

            // AED to major currencies
            ['from' => $aed->id, 'to' => $usd->id, 'rate' => 0.272],
            ['from' => $aed->id, 'to' => $eur->id, 'rate' => 0.231],
        ];

        foreach ($exchangeRates as $rate) {
            CurrencyExchangeRate::updateOrCreate(
                [
                    'from_currency_id' => $rate['from'],
                    'to_currency_id' => $rate['to']
                ],
                [
                    'rate' => $rate['rate'],
                    'is_active' => true,
                ]
            );
        }
    }
}
