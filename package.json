{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build && vite build --ssr", "preview": "vite preview"}, "devDependencies": {"@inertiajs/vue3": "^2.0.11", "@quasar/vite-plugin": "^1.9.0", "@vitejs/plugin-vue": "^4.6.2", "axios": "^1.9.0", "laravel-vite-plugin": "^1.2.0", "quasar": "^2.18.1", "sass": "^1.89.0", "vite": "^5.4.19", "vue": "^3.5.15"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@quasar/extras": "^1.17.0", "chart.js": "^4.4.9", "dayjs": "^1.11.13", "flag-icons": "^7.3.2", "laravel-echo": "^2.1.4", "pinia": "^3.0.2", "vue-chartjs": "^5.3.2", "vue-country-flag-next": "^2.3.2", "vue-i18n": "^11.1.5", "vue-router": "^4.5.1", "ziggy-js": "^2.5.3"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}